import React from "react";

import DoubleBackwardIcon from "../../svgs/DoubleBackwardIcon";
import BackwardIcon from "../../svgs/BackwardIcon";
import DoubleForwardIcon from "../../svgs/DoubleForwardIcon";
import Forward from "../../svgs/ForwardIcon";
import styles from "./index.module.scss";

interface ITableOptionProps {
  data: any;
  gotoPage: (arg: number) => void;
  canPreviousPage: () => void;
  canNextPage: () => void;
  previousPage: () => void;
  nextPage: () => void;
  pageIndex: number;
  pageOptions: Array<any>;
  pageCount: number;
}

export default function TableOptions({
  gotoPage,
  canPreviousPage,
  canNextPage,
  previousPage,
  nextPage,
  pageIndex,
  pageOptions,
  pageCount,
  data,
}: ITableOptionProps) {
  return (
    <>
      <div className="container-fluid d-flex flex-column flex-md-row flex-lg-row flex-xl-row justify-content-between align-items-center w-100 pr-5">
        <div className="d-flex align-items-center">
          <button
            type="button"
            onClick={() => gotoPage(0)}
            // disabled={!canPreviousPage}
            className={`${styles.btn} border-0 outline-none px-3 py-2 bg-transparent`}
          >
            <DoubleBackwardIcon
              color={!canPreviousPage ? "rgba(0,0,0,0.4)" : "#181818"}
            />
          </button>
          <button
            type="button"
            className={`${styles.btn} border-0 outline-none px-3 py-2 bg-none ml-3 bg-transparent`}
            onClick={() => previousPage()}
            disabled={!canPreviousPage}
          >
            <BackwardIcon
              color={!canPreviousPage ? "rgba(0,0,0,0.4)" : "#181818"}
            />
          </button>
          <div className="d-flex align-self-center mx-3">
            <span className={`${styles.paginatedFont} mt-1`}>
              {pageIndex <= 0 ? pageIndex + 1 : pageIndex * 10} -{" "}
              {10 * (pageIndex + 1)} of {pageOptions?.length * 10}
            </span>
          </div>
          <button
            type="button"
            className={`${styles.btn} border-0 outline-none px-3 py-2 mr-4 bg-transparent`}
            onClick={() => nextPage()}
            disabled={!canNextPage}
          >
            <Forward color={!canNextPage ? "rgba(0,0,0,0.4)" : "#181818"} />
          </button>
          <button
            type="button"
            className={`${styles.btn} border-0 outline-none px-3 py-2 bg-transparent`}
            onClick={() => gotoPage(pageCount - 1)}
            disabled={!canNextPage}
          >
            <DoubleForwardIcon
              color={!canNextPage ? "rgba(0,0,0,0.4)" : "#181818"}
            />
          </button>
        </div>

        {/* <div className={styles.estimates}>
          <span className={styles.title}>Active:</span>{" "}
          <span className={styles.body}>{data?.active}</span>
        </div> */}

        {/* <div className={styles.estimates}>
          <span className={styles.title}>Closed:</span>{" "}
          <span className={styles.body}>{data?.closed}</span>
        </div> */}

        {/* <div className={styles.estimates}>
          <span className={styles.title}>Revenue Estimate:</span>{" "}
          <span className={styles.body}>{data?.revenue}</span>
        </div> */}
      </div>
    </>
  );
}
