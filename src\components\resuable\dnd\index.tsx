import React from "react";

import styles from "./index.module.scss";

import UploadSvg from "../../svgs/UploadSvg";

interface IDragAndDrop {
  formik?: any;
  name: string;
  label: string;
}

const DragAndDrop = ({ formik, name, label }: IDragAndDrop) => {
  const [isDragging, setIsDragging] = React.useState(false);

  const onDragOver = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const onDragragEnter = (e: any) => {
    e.preventDefault();
    e.nativeEvent.stopImmediatePropagation();
    setIsDragging(true);
  };

  const onDrop = (e: any) => {
    e.preventDefault();
    e.stopPropagation();

    if (!e.dataTransfer.files[0]?.type.includes("image")) return;
    formik.setFieldValue(name, e.dataTransfer.files[0]);
    setIsDragging(false);
  };

  const onDragLeave = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
  };

  return (
    <label
      onDragOver={onDragOver}
      onDragEnter={onDragragEnter}
      onDrop={onDrop}
      onDragLeave={onDragLeave}
      className={`${styles.wrapper} ${isDragging && `${styles.opacity}`} p-2 `}
    >
      <div
        className={`${styles.inner} d-flex align-items-center justify-content-center flex-column `}
      >
        <input
          type="file"
          className="d-none"
          onChange={(e: any) => formik.setFieldValue(name, e.target.files[0])}
        />
        <div className={`text-center`}>
          <div>
            <UploadSvg />
          </div>

          <div className={`${styles.name} mt-2`}>
            Upload &nbsp;
            <span className={styles.lenderName}>{label}</span>
          </div>

          <div className={`${styles.text}`}>Drag/drop or click to upload</div>
        </div>
      </div>
    </label>
  );
};

export default DragAndDrop;
