import React from "react";

import styles from "./index.module.scss";

interface ITrackerLeftGroup {
  children: React.ReactNode;
  formik?: any;
  data?: any;
}

const TrackerLeftGroup = ({ children, formik, data }: ITrackerLeftGroup) => {
  return (
    <section
      className={`col-12 col-md-3 col-lg-3 col-xl-3  ${styles.outerWrapper}`}
    >
      <div className={`${styles.wrapper}`}>
        {React.Children.map(children, (child: any) => {
          return React.cloneElement(child, { formik, data });
        })}
      </div>
    </section>
  );
};

export default React.memo(TrackerLeftGroup, (prev, next) => {
  return (
    prev?.data?.portfolio_properties?.length !==
    next?.data?.portfolio_properties?.length
  );
});
