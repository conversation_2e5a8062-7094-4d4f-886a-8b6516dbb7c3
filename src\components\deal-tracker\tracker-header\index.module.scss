@import "../../../../styles/_mixins.scss";

.wrapper {
  top: 0;
  z-index: 2;
  background-color: #f1f3f2;
}

.cancel {
  background-color: #f8f8f8;
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    opacity: 0.5;
  }
}

.deal {
  font-weight: 600;
  font-size: 1.1em;
  border-right: 1px solid #a1aaa6; 
  padding-right: 10px;
}

.orderNumber {
  font-weight: 600;
  font-size: 1.0em;
  border-right: 1px solid #a1aaa6;
  color: #595959;  
  padding-right: 10px;  
}

.dealDesc {
  color: #b9b9b9;
  font-size: 1.1em;
  font-weight: 600;
  margin-left: 10px;
}

.button {
  outline: 0;
  background-color: var(--risk-primary);
  outline: 0;
  border: 0;
  margin-right: 1.5em;
  color: #ffffff;
  font-size: 1em;
  font-weight: 500;
  transition: 0.3s ease-in;

  &:hover {
    opacity: 0.7;
  }
}

.lender {
  color: rgba(89, 89, 89, 1);
  border: 1px solid rgba(89, 89, 89, 1);
  border-radius: 15px;
  padding: 2px 10px;
  @include font(0.9em, 600);
}

.setUpText{
  color: var(--primary-light);
  font-size: 0.85em;
  font-weight: 600;
  transition: 0.3s ease-in;
  margin-left: 0.5em;
  animation: blink 2s infinite cubic-bezier(0.42, 0, 0.58, 1);

}

.setUpButton{
  outline: 0;
  color: var(--primary-light);
  outline: 0;
  border: 0;
  font-size: 0.85em;
  font-weight: 600;
  transition: 0.3s ease-in;

  &:hover {
    opacity: 0.7;
    text-decoration: underline;
  }
}


@keyframes blink {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.8;
  }
}
