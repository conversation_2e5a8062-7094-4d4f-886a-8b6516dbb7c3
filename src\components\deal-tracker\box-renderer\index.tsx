import React from "react";

import styles from "./index.module.scss";

interface IBoxRenderer {
  boxName: string;
  wrapperHeight?: boolean;
  className?: string;
  id: string;
}

const BoxRenderer = ({ boxName, wrapperHeight, className, id }: IBoxRenderer) => {
  return (
    <section className="position-relative" id={id}>
      <section
        className={`w-100 ${boxName} ${className && className} ${
          styles.wrapper
        } ${wrapperHeight && styles.heightSm}`}
      ></section>
    </section>
  );
};

export default BoxRenderer;
