import React from "react";

import styles from "./index.module.scss";

import { Field } from "formik";

interface ICheckbox {
  name: string;
  label: string;
  [key: string]: any;
}

export const Checkbox = ({ name, label, formik, ...rest }: ICheckbox) => {
  const handleClickOutside = () => {
    formik.setFieldValue(name, !formik.values?.[name]);
  };

  return (
    <section
      className={`${styles.wrapper} d-flex align-items-center justify-content-center mt-2`}
      onClick={handleClickOutside}
    >
      <div
        className={`text-right ${styles.label} ${!!formik.values?.[name] && styles.green}`}
        style={{ flex: 8 }}
      >
        {label}
      </div>

      <div style={{ flex: 1 }}></div>

      <div style={{ flex: 3 }} className="text-center">
        <Field
          name={name}
          type={"checkbox"}
          className={`${styles.field}`}
          {...rest}
        />
      </div>
    </section>
  );
};

export default Checkbox;
