import React from "react";

import styles from "./index.module.css";

import dayjs from "dayjs";

export default function SubmittedDate({ data }: { data: any }) {
  const options: any = {
    day: "numeric",
    month: "long",
    year: "numeric",
  };

  return (
    <section
      className={`${styles.wrapper} text-capitalize px-2 py-3 my-3 mr-2 text-center rounded`}
    >
      <div className={styles.submittedText}>Submitted date</div>
      <div className={styles.mainText}>
        {dayjs(
          new Date(data?.submitted_date)?.toLocaleDateString("en-GB", options)
        ).format("MMMM D, YYYY")}
      </div>
    </section>
  );
}
