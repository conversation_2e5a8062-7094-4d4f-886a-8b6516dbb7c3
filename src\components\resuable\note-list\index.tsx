import React from "react";
import DeleteSvg from "../../svgs/Delete";
import EditIconSvg from "../../svgs/EditIcon";
import CustomModal from "../modal";

import styles from "./index.module.scss";

interface INoteList {
  note: { id: number; note: string; time: Date; author: string };
  setNoteVal: (arg: { id: number; note: any }) => void;
  noteCheck: string | null;
  formik: any;
  handleSubmitNote: () => void;
}

const NoteList = ({
  note: { id, note, time, author },
  setNoteVal,
  noteCheck,
  formik,
  handleSubmitNote,
}: INoteList) => {
  const handleEdit = (note: string, id: number) => {
    setNoteVal({ id, note });
  };

  const handleDelete = (id: number) => {
    const currentNotes = formik.values?.["note"];

    const newNotes = currentNotes?.filter((note: any) => note.id !== id);

    formik.setFieldValue("note", newNotes);

    setShow(false);

    handleSubmitNote();
  };

  const [show, setShow] = React.useState(false);

  return (
    <>
      <CustomModal
        {...{
          title: "Delete",
          show,
          setShow,
          columnLayout: "col-12 col-md-8 col-lg-7 col-xl-4",
          align: "align-items-center",
        }}
      >
        <div className="text-center my-3">
          <div className={styles.delete}>Do you want to delete this note?</div>

          <div className="mt-3">
            <button
              className={`${styles.buttonBorder} mr-2 py-2`}
              onClick={() => setShow(false)}
              type="button"
            >
              No
            </button>

            <button
              type="button"
              onClick={() => handleDelete(id)}
              className={`${styles.button} py-2`}
            >
              Yes
            </button>
          </div>
        </div>
      </CustomModal>
      <section className={`${styles.wrapper} px-2`}>
        <div className={`${styles.innerWrapper} px-2 py-1 mt-2`}>
          <div className={`${styles.time} d-flex align-items-center`}>
            <div className="flex-fill text-right">
              <span className={`${styles.author} mx-3`}>{author}</span>

              <span className={styles.time}>{time}</span>
            </div>
          </div>
          <div className={`${styles.note}`}>{note}</div>

          <div
            className={`${styles.icons} d-flex align-items-center justify-content-end`}
          >
            <div className={`px-2 mr-1`}>
              <div onClick={() => !noteCheck && setShow(true)}>
                <DeleteSvg />
              </div>
            </div>

            <div
              className="d-flex align-self-center px-2"
              onClick={() => handleEdit(note, id)}
            >
              <EditIconSvg />
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default NoteList;
