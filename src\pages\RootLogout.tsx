import React from "react";
import { handleLogout } from "../utils/handleLogout";
import { useHistory } from "react-router";
import { useAppDispatch } from "../store/storeConfig";
import SuspenseFallback from "../components/suspense/index";

export default function RootLogout() {
  const history = useHistory();
  const dispatch = useAppDispatch();

  React.useEffect(() => {
    handleLogout(history, dispatch);
  }, [dispatch, history]);

  return <SuspenseFallback />;
}
