import React from "react";
import styles from "./index.module.css";

interface ISectionHeaderProps {
  title: string;
}

export default function SectionHeader({ title }: ISectionHeaderProps) {
  return (
    <section className="text-center w-100">
      <span
        className={`${styles.title} ${styles.container} px-3 px-sm-3 px-md-3 px-lg-3 px-xl-5 py-2`}
      >
        {title}
      </span>
    </section>
  );
}
