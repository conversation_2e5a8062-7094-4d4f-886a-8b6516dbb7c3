export const addPropertyData: Array<any> = [
  {
    name: "property_name",
    label: "Property Name",
    type: "text",
  },
  {
    name: "property_address",
    label: "Property Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "city",
    label: "City",
    type: "select",
    options: ["CA", "Canada"],
  },
  {
    name: "state",
    label: "State*",
    type: "text",
  },
  {
    name: "zip",
    label: "Zip*",
    type: "text",
  },
  {
    name: "loan_number",
    label: "Loan number",
    type: "text",
  },
  {
    name: "legal_name",
    label: "Legal name of borrwowing",
    type: "text",
  },
  {
    name: "Insurable_value",
    label: "Insurable value",
    type: "text",
  },
  {
    name: "land_value",
    label: "Land value",
    type: "text",
  },
  {
    name: "annual_income",
    label: "Annual income (EGI)",
    type: "text",
  },
  {
    name: "mo_income",
    label: "18 MO income (if app)",
    type: "text",
  },
  {
    name: "equip_breakdown",
    label: "Equip breakdown",
    type: "text",
  },
  {
    name: "o/l",
    label: "O/L:",
    type: "buttons",
    options: ["Yes", "No"],
  },
  {
    name: "flood",
    label: "Flood:",
    type: "buttons",
    options: ["Yes", "No"],
  },
  {
    name: "quake",
    label: "Quake:",
    type: "buttons",
    options: ["Yes", "No"],
  },
  {
    name: "employees",
    label: "Employees:",
    type: "buttons",
    options: ["Yes", "No"],
  },
  {
    name: "autos",
    label: "Autos:",
    type: "buttons",
    options: ["Yes", "No"],
  },

  {
    name: "tenant_insurance",
    label: "Tenant Ins BLDGs:",
    type: "buttons",
    options: ["Yes", "No"],
  },
];

export const trackerProperSummary: Array<any> = [
  {
    name: "property_street",
    label: "Property Name",
    type: "text",
  },
  {
    name: "loan_number",
    label: "Loan Number",
    type: "text",
  },
  {
    name: "loan_execution",
    label: "Loan Execution",
    type: "text",
  },
  {
    name: "sponsor",
    label: "Sponsor",
    type: "text",
  },
  {
    name: "name",
    label: "Named Insured/ Borrowing Entity",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
  {
    name: "street_address",
    label: "Street Address",
    type: "text",
  },
];

export const loanSummary: Array<any> = [
  {
    name: "loan_amount",
    label: "Loan Amount:",
    type: "text",

    value: null,
    occupy: true,
  },
  {
    name: "bkk",
    label: "Refi/ACQ:",
    type: "text",
    showType: "buttons",
    occupy: true,
    options: ["Acquisition", "Refinance"],
  },
  {
    name: "property_type",
    label: "Property Type:",
    type: "text",

    occupy: true,
  },
  {
    name: "insurable_value",
    label: "Insurable Value:",
    type: "text",

    occupy: true,
  },
  {
    name: "insurable_valueff",
    label: "Insurable Value FF & E:",
    type: "text",
    occupy: true,
  },
  {
    name: "land_value",
    label: "Land Value:",
    type: "text",
    occupy: true,
  },
  {
    name: "Annual income",
    label: "Annual Income",
    type: "text",
    occupy: true,
  },
  {
    name: "18 - Month Income",
    label: "18 - Month Income",
    type: "text",
    occupy: true,
  },
  {
    name: "EPI",
    label: "EPI",
    type: "text",
    occupy: true,
  },
];

export const waiverSummary: Array<any> = [
  {
    name: "waiver_exception",
    label: "Waiver/Exceptions:",
    commentName: "comment",
    type: "waiver",
  },
  {
    name: "status",
    label: "Status:",
    type: "buttons",
    options: ["Approved", "Pending"],
  },
  {
    name: "type",
    label: "Type:",
    type: "buttons",
    options: ["Rep Exception", "Lender"],
  },
];



export const waiversFromDB = [
  {
    id: 1,
    waiver: "waiver-exception_1",
    waiver_comment: "waiver_comment_1",
    status: "waiver_status_1",
    type: "waiver_type_1",
    archive: "archive_1",
  },
  {
    id: 2,
    waiver: "waiver-exception_2", //eui names
    waiver_comment: "waiver_comment_2",
    status: "waiver_status_2",
    type: "waiver_type_2",
    archive: "archive_2",
  },
  {
    id: 3,
    waiver: "waiver-exception_3",
    waiver_comment: "waiver_comment_3",
    status: "waiver_status_3",
    type: "waiver_type_3",
    archive: "archive_3",
  },
];

const checkListName = "chklist";

export const checkListData = [
  {
    header: {
      title: "Phase 1: Engagement",
      percentage: "15",
    },
    items: [
      {
        name: `${checkListName}_file_setup`,
        label: "Order form submission received, file setup",
      },
      {
        name: `${checkListName}_ordered`,
        label: "Flood determination ordered ",
      },
      {
        name: `${checkListName}_agent`,
        label: "Requirements to agent ",
      },
    ],
  },
  {
    header: {
      title: "Phase 2: Diligence Review",
      percentage: "20",
    },
    items: [
      {
        name: `${checkListName}_appraisal`,
        label: "Appraisal",
      },
      {
        name: `${checkListName}_pca`,
        label: "PCA",
      },
      {
        name: `${checkListName}_zonning`,
        label: "Zonning, etc",
      },
    ],
  },
  {
    header: {
      title: "Phase 3: Insurance Review",
      percentage: "40",
    },
    items: [
      {
        name: `${checkListName}_property`,
        label: "Evidence of property",
      },
      {
        name: `${checkListName}_liability`,
        label: "Liability",
      },
      {
        name: `${checkListName}_flood`,
        label: "Flood",
      },
    ],
  },

  {
    header: {
      title: "Phase 4: Finalization",
      percentage: "25",
    },
    items: [
      {
        name: `${checkListName}_lender`,
        label: "Negotiation of terms with lender",
      },
      {
        name: `${checkListName}_documentation`,
        label: "Obtaining final insurance documentation",
      },
      {
        name: `${checkListName}_premiums`,
        label: "Confirming final premiums",
      },
      {
        name: `${checkListName}_client`,
        label: "Deliverable to client",
      },
    ],
  },
];

