@import "../../../styles/_mixins.scss";

.indicator {
  display: flex;
  align-items: center;
  gap: 4px;

  &.inline {
    position: relative;
  }

  &.top-right {
    position: absolute;
    top: 4px;
    right: 4px;
  }

  &.top-left {
    position: absolute;
    top: 4px;
    left: 4px;
  }

  &.bottom-right {
    position: absolute;
    bottom: 4px;
    right: 4px;
  }

  &.bottom-left {
    position: absolute;
    bottom: 4px;
    left: 4px;
  }
}

// Dot variant
.dot {
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;

  &.small {
    width: 6px;
    height: 6px;
  }

  &.medium {
    width: 8px;
    height: 8px;
  }

  &.large {
    width: 12px;
    height: 12px;
  }

  &.online {
    background-color: var(--success);
    box-shadow: 0 0 6px rgba(39, 174, 96, 0.6);
    animation: pulse 2s infinite;
  }

  &.offline {
    background-color: var(--warning);
    box-shadow: 0 0 6px rgba(235, 172, 11, 0.6);
    animation: blink 1s infinite;
  }
}

// Badge variant
.badge {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;

  &.small {
    width: 12px;
    height: 12px;
    @include font(0.6em, 600);
  }

  &.medium {
    width: 16px;
    height: 16px;
    @include font(0.7em, 600);
  }

  &.large {
    width: 20px;
    height: 20px;
    @include font(0.8em, 600);
  }

  &.online {
    background-color: var(--success);
    color: white;
    box-shadow: 0 0 8px rgba(39, 174, 96, 0.4);
    animation: pulse 2s infinite;
  }

  &.offline {
    background-color: var(--warning);
    color: white;
    box-shadow: 0 0 8px rgba(235, 172, 11, 0.4);
    animation: blink 1s infinite;
  }
}

// Icon variant
.icon {
  transition: all 0.3s ease;

  &.small {
    font-size: 0.7em;
  }

  &.medium {
    font-size: 0.9em;
  }

  &.large {
    font-size: 1.2em;
  }

  &.online {
    animation: pulse 2s infinite;
  }

  &.offline {
    animation: blink 1s infinite;
  }
}

// Text variant
.text {
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;

  &.small {
    @include font(0.6em, 600);
  }

  &.medium {
    @include font(0.75em, 600);
  }

  &.large {
    @include font(0.9em, 600);
  }

  &.online {
    color: var(--success);
  }

  &.offline {
    color: var(--warning);
  }
}

// Status text (when showText is true)
.statusText {
  @include font(0.7em, 500);
  text-transform: uppercase;
  letter-spacing: 0.3px;

  &.online {
    color: var(--success);
  }

  &.offline {
    color: var(--warning);
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.4;
  }
}

// Hover effects
.dot:hover,
.badge:hover {
  transform: scale(1.2);
  cursor: pointer;
}

.icon:hover {
  transform: scale(1.1);
  cursor: pointer;
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .dot,
  .badge,
  .icon {
    animation: none !important;
  }

  .dot:hover,
  .badge:hover,
  .icon:hover {
    transform: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .dot,
  .badge {
    border-width: 2px;
  }

  .text {
    &.online {
      color: #008000;
    }

    &.offline {
      color: #ff6600;
    }
  }

  .statusText {
    &.online {
      color: #008000;
    }

    &.offline {
      color: #ff6600;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .dot,
  .badge {
    border-color: rgba(0, 0, 0, 0.8);
  }
}
