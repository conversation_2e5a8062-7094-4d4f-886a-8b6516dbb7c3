import { templateModel } from "../data/waiver-template";

export const generateWaiverModelFromLeanData = (
  waivers: Array<{ [key: string]: any }>
) => {
  if ((waivers || []).length <= 0) return [];

  return waivers.map(
    ({ id, comment, status, waiver_exception, type, archive }: any) => {
      const waiverTemplateCopy = JSON.parse(JSON.stringify(templateModel));
      waiverTemplateCopy.id = id;
      waiverTemplateCopy.comment.value = comment;
      waiverTemplateCopy.status.value = status;
      waiverTemplateCopy.waiver_exception.value = waiver_exception;
      waiverTemplateCopy.type.value = type;
      waiverTemplateCopy.archive = archive;
      return waiverTemplateCopy;
    }
  );
};
