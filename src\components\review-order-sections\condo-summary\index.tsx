import React from "react";
import { EsectionHeaderTitles } from "../../../data/sectionHeader";
import { dynamicFormData } from "./../../../data/dynamic-form";
import ReusableSummary from "../resuable-summary";

interface ICondoSummaryProps {
  formik?: any;
  // setFieldValue: (arg1: string, arg2: string) => void;
}

const CondoSummary = React.forwardRef(
  ({ formik }: ICondoSummaryProps, ref: any) => {

    return (
      <section
        ref={ref}
        className="container-fluid px-0 px-md-0 px-lg-0 px-xl-5 pb-3 pb-md-3 pb-lg-3 pb-xl-5"
      >
        <ReusableSummary
          {...{
            data: dynamicFormData.condoSummary,
            title: EsectionHeaderTitles.condoSummary,
            formik: formik,
          }}
        />
      </section>
    );
  }
);

export default CondoSummary;
