
export const formData = (data: any, id?: any) => {
  if (data && id) {
    const entries: Array<any> = Object.entries(data);
    let formData = new FormData();

    formData.append("id", id);

    for (const [key, value] of entries) {
      formData.append(key, value || "");
    }
    return formData;
  }
  if (data && !id) {
    const entries: Array<any> = Object.entries(data);
    let formData = new FormData();

    for (const [key, value] of entries) {
      formData.append(key, value || "");
    }
    return formData;
  }
};
