import React from "react";
import { EsectionHeaderTitles } from "../../../data/sectionHeader";
import { dynamicFormData } from "./../../../data/dynamic-form";
import ReusableSummary from "./../resuable-summary";

interface IOrderSummaryProps {
  formik?: any;
}

const OrderSummary = React.forwardRef(
  ({ formik }: IOrderSummaryProps, ref: any) => {
    return (
      <section
        ref={ref}
        className="container-fluid p-0 p-md-0 p-lg-0 p-xl-5 py-0"
      >
        <ReusableSummary
          {...{
            data: dynamicFormData.orderSummary,
            title: EsectionHeaderTitles.orderSummary,
            formik: formik,
          }}
        />
      </section>
    );
  }
);

export default OrderSummary;
