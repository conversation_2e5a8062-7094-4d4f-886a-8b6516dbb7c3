@import "../../../styles/_mixins.scss";

.electronStatus {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background-color: rgba(241, 243, 242, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(46, 113, 111, 0.1);
  font-size: 0.75rem;
  
  .versionInfo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .label {
      color: rgba(24, 24, 24, 0.6);
      @include font(0.7em, 500);
    }
    
    .value {
      color: var(--risk-primary);
      @include font(0.7em, 600);
    }
  }
  
  .networkStatus {
    display: flex;
    align-items: center;
    gap: 6px;
    
    .connectionIcon {
      font-size: 0.8em;
      animation: pulse 2s infinite;
    }
    
    .statusText {
      @include font(0.75em, 600);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      
      &.online {
        color: var(--success);
      }
      
      &.offline {
        color: var(--warning);
      }
    }
  }
  
  .syncStatus {
    display: flex;
    align-items: center;
    gap: 6px;
    
    .syncIcon {
      font-size: 0.8em;
      
      &.syncing {
        animation: spin 1s linear infinite;
      }
    }
    
    .syncText {
      @include font(0.7em, 500);
      
      &.idle {
        color: rgba(24, 24, 24, 0.5);
      }
      
      &.syncing {
        color: var(--primary-light);
        animation: pulse 1.5s infinite;
      }
      
      &.success {
        color: var(--success);
      }
      
      &.error {
        color: var(--danger);
      }
    }
  }
}

.compactStatus {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: rgba(241, 243, 242, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(46, 113, 111, 0.2);
  
  .connectionIndicator {
    font-size: 0.7em;
    animation: pulse 2s infinite;
  }
  
  .syncIndicator {
    font-size: 0.6em;
    
    &.syncing {
      animation: spin 1s linear infinite;
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Status bar variant for header
.statusBar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 12px;
  background: linear-gradient(135deg, rgba(46, 113, 111, 0.05), rgba(46, 113, 111, 0.1));
  border-radius: 6px;
  border: 1px solid rgba(46, 113, 111, 0.15);
  
  .statusItem {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .icon {
      font-size: 0.8em;
    }
    
    .text {
      @include font(0.75em, 500);
      color: rgba(24, 24, 24, 0.8);
    }
    
    &.online .text {
      color: var(--success);
    }
    
    &.offline .text {
      color: var(--warning);
    }
    
    &.syncing .icon {
      animation: spin 1s linear infinite;
    }
  }
  
  .divider {
    width: 1px;
    height: 12px;
    background-color: rgba(46, 113, 111, 0.2);
  }
}

// Notification badge variant
.notificationBadge {
  position: relative;
  display: inline-flex;
  align-items: center;
  
  .badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 1px solid white;
    
    &.online {
      background-color: var(--success);
      animation: pulse 2s infinite;
    }
    
    &.offline {
      background-color: var(--warning);
      animation: blink 1s infinite;
    }
    
    &.syncing {
      background-color: var(--primary-light);
      animation: spin 1s linear infinite;
    }
    
    &.error {
      background-color: var(--danger);
      animation: shake 0.5s infinite;
    }
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

// Responsive adjustments
@media screen and (max-width: 768px) {
  .electronStatus {
    font-size: 0.7rem;
    padding: 6px;
    gap: 6px;
  }
  
  .statusBar {
    padding: 4px 8px;
    gap: 8px;
  }
}

// Dark mode support (if needed in future)
@media (prefers-color-scheme: dark) {
  .electronStatus {
    background-color: rgba(24, 24, 24, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    
    .label {
      color: rgba(255, 255, 255, 0.6);
    }
    
    .syncText.idle {
      color: rgba(255, 255, 255, 0.5);
    }
  }
  
  .compactStatus {
    background-color: rgba(24, 24, 24, 0.8);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .statusBar {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.1));
    border-color: rgba(255, 255, 255, 0.15);
    
    .text {
      color: rgba(255, 255, 255, 0.8);
    }
    
    .divider {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}
