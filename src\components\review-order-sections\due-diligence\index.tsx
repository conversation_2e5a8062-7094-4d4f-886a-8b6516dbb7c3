import React from "react";
import { EsectionHeaderTitles } from "../../../data/sectionHeader";
import { dynamicFormData } from "./../../../data/dynamic-form";
import ReusableSummary from "../resuable-summary";

interface IDueDilegenceProps {
  formik?: any;
}

const DueDiligence = React.forwardRef(
  ({ formik }: IDueDilegenceProps, ref: any) => {
    return (
      <section
        ref={ref}
        className="container-fluid px-0 px-md-0 px-lg-0 px-xl-5 pb-3 pb-md-3 pb-lg-3 pb-xl-5 mt-4"
      >
        <ReusableSummary
          {...{
            data: dynamicFormData.dueDiligenceSummary,
            title: EsectionHeaderTitles.dueDiligence,
            formik: formik,
          }}
        />
      </section>
    );
  }
);

export default DueDiligence;
