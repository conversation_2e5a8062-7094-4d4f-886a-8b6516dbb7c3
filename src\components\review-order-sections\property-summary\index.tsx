import React from "react";
import { EsectionHeaderTitles } from "../../../data/sectionHeader";
import { dynamicFormData } from "./../../../data/dynamic-form";
import ReusableSummary from "./../resuable-summary/index";

interface IPropertySummaryProps {
  renderError?: (arg: string) => React.ReactNode;
  formik?: any;
}

const PropertySummary = React.forwardRef(
  ({ renderError, formik }: IPropertySummaryProps, ref: any) => {
    return (
      <section
        ref={ref}
        className="container-fluid px-0 px-md-0 px-lg-0 px-xl-5"
      >
        <ReusableSummary
          {...{
            data: dynamicFormData.propertySummary,
            title: EsectionHeaderTitles.propertySummary,
            formik: formik,
          }}
        />
      </section>
    );
  }
);

export default PropertySummary;
