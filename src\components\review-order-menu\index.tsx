import React from "react";
import { IReviewOrderMenu } from "../../models/reviewOrderMenu";

interface IReviewOrderMenuProps {
  reviewOrderMenuData: IReviewOrderMenu[];
  selectedMenu: any;
  setIsClicking: (arg: boolean) => void;
}

const ReviewOrderMenu: React.FC<IReviewOrderMenuProps> = ({
  children,
  reviewOrderMenuData,
  selectedMenu,
  setIsClicking,
}) => {
  return (
    <section
      onMouseEnter={() => setIsClicking(true)}
      onMouseLeave={() => setIsClicking(false)}
      className="col-12 col-sm-12 col-md-3 col-lg-2 col-xl-2 px-0 px-sm-0 px-md-0 px-lg-0 px-xl-4 "
    >
      {React.Children.map(children, (child: any) => {
        return React.cloneElement(
          child,
          { reviewOrderMenuData, selectedMenu },
          null
        );
      })}
    </section>
  );
};

export default ReviewOrderMenu;
