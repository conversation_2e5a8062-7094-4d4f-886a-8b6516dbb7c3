import React from "react";

import styles from "./index.module.scss";

interface IReviewsOrderSectionComponentProps {
  children: React.ReactNode;
  handleScroll: () => void;
}

const ReviewsOrderSectionComponent: React.FC<IReviewsOrderSectionComponentProps> =
  React.forwardRef(({ children, handleScroll }, ref: any) => {
    return (
      <section
        ref={ref}
        onScroll={handleScroll}
        className={`${styles.wrapper} px-3 px-lg-5 px-xl-5 pb-5 pt-0 overflow-auto`}
        style={{ height: "100vh" }}
      >
        {children}
      </section>
    );
  });

//

export default ReviewsOrderSectionComponent;
