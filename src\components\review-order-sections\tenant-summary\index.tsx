import React from "react";
import { EsectionHeaderTitles } from "../../../data/sectionHeader";
import { dynamicFormData } from "./../../../data/dynamic-form";
import ReusableSummary from "./../resuable-summary/index";

interface ITenantSummaryProps {
  formik?: any;
}

const TenantSummary = React.forwardRef(
  ({ formik }: ITenantSummaryProps, ref: any) => {
    return (
      <section
        ref={ref}
        className="container-fluid px-0 px-md-0 px-lg-0 px-xl-5"
      >
        <ReusableSummary
          {...{
            data: dynamicFormData.tenantsSummary,
            title: EsectionHeaderTitles.tenantSummary,
            formik: formik,
          }}
        />
      </section>
    );
  }
);

export default TenantSummary;
