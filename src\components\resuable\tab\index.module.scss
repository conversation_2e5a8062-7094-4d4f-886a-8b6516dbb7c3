@import "../../../../styles/_mixins.scss";

.wrapper {
  background-color: var(--white);
  //height: 80vh;

  .tabMenu {
    &:after {
      content: "";
      position: absolute;
      height: 0;
      width: 100%;
      top: 32.5px;
      left: 0;
      //bottom: 400px;
      border-bottom: 1px solid rgba(24, 24, 24, 0.1);
    }

    .btns {
      position: relative;
      border: 0;
      padding: 5px 0;
      background: none;
      transition: 0.3s;
      text-transform: capitalize;
      color: rgba(24, 24, 24, 0.5);
      line-height: -20px;
      min-width: 150px;
      @include font(0.9em, 600);
      &:nth-child(even) {
        margin: 0 30px;
      }

      &:hover {
        opacity: 0.9;
      }
      &.focus {
        color: #d59c00;
      }
      &.focus:after {
        content: "";
        position: absolute;
        height: 0;
        width: 100%;
        left: 0;
        bottom: -2.5px;
        border-bottom: 3px solid #d59c00;
      }
    }

    .viewButton {
      cursor: pointer;
      right: 30px;
      background-color: rgb(213, 156, 0);
      padding: 3px 7px;
      color: var(--white);
      outline: 0;
      border: 0;
      transition: 0.3s;
      @include font(0.8rem, 500);

      &:hover {
        border-radius: 10px;
        opacity: 0.7;
      }

      &:disabled {
        opacity: 0.35;
        cursor: not-allowed;
      }
    }
  }

  .tabView {
    background-color: var(--white);

    .tabContent {
      display: none;

      &.selected {
        display: block !important;
      }
    }
  }

  .count {
    padding: 2px 8px;
    background-color: #d59c00;
    color: #ffffff;
    border-radius: 2px;
  }
}
