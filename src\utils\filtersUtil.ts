import { formatDate } from "./formatDate";

export const buildFilterUrl = (values: { [key: string]: any }) => {
  if (Object.values(values || {})?.length <= 0) return;

  const temporaryUrl = "https://backstage.pillarrisk.com/";

  const url = new URL(temporaryUrl);

  Object.entries(values || {}).forEach(([key, value]) => {
    if (Array.isArray(value) && value?.length > 0) {
      url.searchParams.set(key, value.join(","));
    }

    if (
      typeof value === "object" &&
      !Array.isArray(value) &&
      Object.values(value)?.length > 0
    ) {
      url.searchParams.set("start_date", formatDate(value.startDate));
      url.searchParams.set("end_date", formatDate(value.endDate));
    }
    if ((typeof value === "string" || value === true) && !!value) {
      url.searchParams.set(key, value);
    }
  });

  return url.toString().slice(temporaryUrl.lastIndexOf(".com") + 6);
};

const createMultiSelectField = (
  data: Array<{ [key: string]: any }>,
  field: string
) => {
  return data
    .find(({ field: dataField }) => dataField === field)
    ?.option.map((option: any) => ({
      label: option,
      value: option,
    }));
};

export const buildFilterFields = (data: Array<any>) => {
  return [
    {
      name: "consultant",
      label: "Consultant",
      placeholder: "select/type consultants",
      type: "multiSelect",
      options: createMultiSelectField(data, "consultant"),
    },
    {
      name: "client",
      label: "Client",
      placeholder: "select/type client",
      type: "multiSelect",
      options: createMultiSelectField(data, "client"),
    },
    {
      name: "closing_date",
      label: "Closing Date",
      placeholder: "select date range",
      type: "dateRange",
    },
    {
      name: "status_param",
      label: "Status",
      placeholder: "select status",
      type: "multiSelect",
      options: createMultiSelectField(data, "deal_status"),
    },
    {
      name: "property_state",
      label: "State",
      placeholder: "select state",
      type: "multiSelect",
      options: createMultiSelectField(data, "property_state"),
    },
    {
      name: "portfolio",
      label: "Is Portfolio?",
      placeholder: "",
      type: "portfolio",
      value: false,
    },
  ];
};
