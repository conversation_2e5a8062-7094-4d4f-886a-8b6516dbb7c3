import React from "react";
import ReviewOrderMenuList from "../menu-list";
import styles from "./index.module.css";
import LineIcon from "./../../svgs/LineIcon";

export default function ReviewOrderMenuContainer({
  reviewOrderMenuData,
  selectedMenu,
  setSelectedMenu,
}: any) {
  return (
    <section className={`position-relative ${styles.container} py-5 px-3`}>
      <aside
        className="position-absolute d-flex justify-content-center align-items-center"
        style={{ zIndex: 12, width: "8px", top: "70px" }}
      >
        <LineIcon />
      </aside>
      {reviewOrderMenuData.map((menu: any) => (
        <ReviewOrderMenuList
          {...{ item: menu, selectedMenu, setSelectedMenu }}
        />
      ))}
    </section>
  );
}
