import React from "react";

import styles from "./index.module.scss";

interface ITab {
  defaultIndex: number;
  children: React.ReactNode;
  count: number;
  formik?: any;
  data?: any;
}

const Tab = ({ defaultIndex, count, children, formik, data }: ITab) => {
  const [bindIndex, setBindIndex] = React.useState(defaultIndex);

  const handleTabChange = (newIndex: number) => {
    setBindIndex(newIndex);
  };

  const items = React.Children.toArray(children);

  return (
    <section className={`${styles.wrapper} position-relative`}>
      <div className={`${styles.tabMenu} d-flex align-items-center py-0`}>
        {items.map(({ props: { label, index } }: any) => {
          return (
            <button
              type="button"
              key={`tab-btn-${index}`}
              onClick={() => handleTabChange(index)}
              className={`${bindIndex === index ? `${styles.focus} ` : ""} ${
                styles.btns
              }`}
            >
              {label}
              {index === 1 && (
                <span className={`${styles.count} ml-2`}>{count}</span>
              )}
            </button>
          );
        })}

        {/*<button
          className={`${styles.viewButton} d-none d-lg-block d-xl-block position-absolute rounded`}
          tabIndex={-1}
          disabled={true}
        >
          View Report
        </button>*/}
      </div>

      <div className={`${styles.tabView} mt-1 p-2`}>
        {React.Children.map(children, (child: any) => {
          return React.cloneElement(child, {
            data,
            tabClass: `
              ${styles.tabContent}
              ${child.props.index === bindIndex ? styles.selected : ""} `,
            formik,
          });
        })}
      </div>
    </section>
  );
};

export default Tab;
