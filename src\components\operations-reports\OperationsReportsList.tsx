import {useHistory} from "react-router-dom";

import styles from "./operationsreport.module.scss";
import {useGetReports} from "../../apis";

const OperationsReportsList = () => {
  const history = useHistory();

  const {data, isLoading, isError, error} = useGetReports();

  const handleSaveReportAndNavigate = (id: string, link: string) => {
    localStorage.setItem("report", link);
    history.push("/operations-reports/" + id);
  };

  if (isLoading) {
    return (
      <div
        className="d-flex flex-column align-items-center justify-content-center w-100"
        style={{height: "100vh"}}
      >
        <div className="d-flex justify-content-center align-items-center my-2">
          <div className="spinner-grow spinner-grow-sm text-success"></div>
        </div>
        <div>Fetching reports...</div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-danger">
        {(error as Error)?.message || "Something went wrong."}
      </div>
    );
  }
  return (
    <>
      <div className={`d-flex mb-3 mt-3 px-5 ${styles.wrapper}`}>
        <aside className="py-3"></aside>

        <aside className="py-3">
          <h5>Report name</h5>
        </aside>

        <aside className="py-3">
          <h5>Description</h5>
        </aside>
      </div>
      {data.map(({id, name, link, description}) => {
        return (
          <div
            className={`d-flex align-items-center px-5 my-1 ${styles.wrapper}`}
            key={id}
            onClick={() => handleSaveReportAndNavigate(id, link)}
          >
            <aside className="text-center py-3" style={{marginTop: -8}}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M16.25 11.875V9.6875C16.25 8.1342 14.9908 6.875 13.4375 6.875H12.1875C11.6697 6.875 11.25 6.45527 11.25 5.9375V4.6875C11.25 3.1342 9.9908 1.875 8.4375 1.875H6.875M6.875 12.5H13.125M6.875 15H10M8.75 1.875H4.6875C4.16973 1.875 3.75 2.29473 3.75 2.8125V17.1875C3.75 17.7053 4.16973 18.125 4.6875 18.125H15.3125C15.8303 18.125 16.25 17.7053 16.25 17.1875V9.375C16.25 5.23286 12.8921 1.875 8.75 1.875Z"
                  stroke="#D59C00"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </aside>
            <aside className="py-3 mt-1">
              <h6>{name}</h6>
            </aside>
            <aside className="py-3">{description}</aside>
          </div>
        );
      })}
    </>
  );
};

export default OperationsReportsList;
