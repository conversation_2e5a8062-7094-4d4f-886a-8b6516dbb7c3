import "react-data-grid/lib/styles.css";
import { DataGrid } from "react-data-grid";

import styles from "./index.module.scss";

import React from "react";

import {
  //exportToXlsx,

  extractRows,
  extractColumns,
} from "./utils";

import { toastError } from "../../../apis";

interface IGridSheet {
  [key: string]: any;
}

function rowKeyGetter(row) {
  return row.id;
}

function GridSheet({
  portfolioData,
  mutate,
  isSaving,
  isError,
  isSuccess,
  invalidatePortfolio,
}: IGridSheet) {
  const [rows, setRows] = React.useState(extractRows(portfolioData));

  React.useEffect(() => {
    setRows(extractRows(portfolioData));
  }, [portfolioData]);

  const handleSave = (values: { [key: string]: unknown }) => {
    const data = {};

    Object.entries(values).forEach(([key, value]) => {
      if (!key.includes("_$op") && key !== "null") data[key] = value;
    });

    mutate(data, {
      onSuccess: () => {
        invalidatePortfolio();
      },
      onError: () => {
        toastError("Check your input or network and try again.", 3000);
      },
    });
  };

  const gridElement = portfolioData.length > 0 && (
    <>
      <div className="my-2">
        <span className={styles.instruction}>Instruction:</span>{" "}
        <span className={styles.iText}>
          To delete a portfolio property, double-click on the delete column to
          reveal the delete button
        </span>
      </div>
      <DataGrid
        className={`${styles.reactGrid} mt-2`}
        columns={extractColumns(portfolioData)}
        rowKeyGetter={rowKeyGetter}
        rows={rows}
        aria-description="Portfolio data grid"
        onRowsChange={(value, data: any) => {
          const position = data?.indexes?.[0] ?? data?.id;

          handleSave(value?.[position]);

          setRows(value);
        }}
      />
    </>
  );

  const match = isSaving || isSuccess || isError;

  if (!portfolioData.length)
    return (
      <div className="text-center p-4 text-dark">
        No available portfolio properties.
      </div>
    );

  return (
    <div className="mt-3 px-3 pb-2">
      {/*<ExportButton onExport={() => exportToXlsx(gridElement, "new.xlsx")}>
        Export to Xlsx
      </ExportButton>*/}

      {match && (
        <div className={`${styles.saveStatus} d-inline mt-2  px-2 py-1`}>
          <span>Status:</span> &nbsp;
          {isSaving ? (
            <span className={styles["saving"]}>saving...</span>
          ) : isSuccess ? (
            <span className={styles["saved"]}>saved!</span>
          ) : isError ? (
            <span className={styles["error"]}>not saved</span>
          ) : (
            ""
          )}
        </div>
      )}

      {gridElement}
    </div>
  );
}

//function ExportButton({
//  onExport,
//  children,
//}: {
//  onExport: () => Promise<unknown>;
//  children: React.ReactChild;
//}) {
//  const [exporting, setExporting] = React.useState(false);
//  return (
//    <button
//      disabled={exporting}
//      className="btn btn-dark text-right"
//      onClick={async () => {
//        setExporting(true);
//        await onExport();
//        setExporting(false);
//      }}
//    >
//      {exporting ? "Exporting" : children}
//    </button>
//  );
//}

export default GridSheet;
