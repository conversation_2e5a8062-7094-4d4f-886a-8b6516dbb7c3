import React from "react";
import TrackerBody from "./tracker-body";
import TrackerHeader from "./tracker-header";
import TrackerLeftGroup from "./tracker-left-group";
import TrackerPortfolio from "./tracker-portfolio";
import { useGet } from "./../../apis/index";
//import TrackerUploadThirdParty from "./track-upload-3rd-party";
//import TrackerUploadLender from "./tracker-upload-lender";
import TrackerMiddleGroup from "./tracker-middle-group";
//import TrackerFormHeader from "../resuable/tracker-header";
import TrackerStatus from "./tracker-status";
import Tab from "../resuable/tab";
import TrackerAllFields from "./tracker-all-fields";
//import WaiverSummary from "./tracker-form-sections/waiver-summary";
import TrackerUpdate from "./tracker-update";
import TrackerRightGroup from "./tracker-right-group";
import TrackerNote from "./tracker-note";
import { useParams } from "react-router";
import TrackerSummaryAllFields from "./tracker-form-sections/tracker-summary-all";
import TrackerSummaryMissingFields from "./tracker-form-sections/tracker-summary-missing/index";
import BoxRenderer from "./box-renderer";
import WaiverInputComponents from "../resuable/waivers-input-components";

const DealTrackerComponents = () => {
  const params: any = useParams();

  console.log('DealTracker component loaded with params:', params);
  console.log('DealTracker ID:', params?.id);

  const { data, isLoading, isError, error }: any = useGet(
    `/order/tracker/${params?.id}`
  );

  console.log('DealTracker API state:', { isLoading, isError, hasData: !!data });

  if (isLoading) {
    return (
      <div
        className="d-flex flex-column align-items-center justify-content-center w-100"
        style={{ height: "100vh" }}
      >
        <div className="d-flex justify-content-center align-items-center my-2">
          <div className="spinner-grow spinner-grow-sm text-success"></div>
        </div>
        <div>Kindly wait...</div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-danger">
        {error?.message || "Something went wrong."}
      </div>
    );
  }

  return (
    <>
      <section className="bg-light" style={{ flex: 11 }}>
        <TrackerHeader
          {...{
            name: data?.deal_name,
            status: data?.deal_status,
            lender: data?.mortgage_lender,
            isBoxFolderId: !!data?.box_folder_id,
            orderNumber: data?.order_number,
          }}
          data={data}
        />

        <TrackerBody {...{ data }}>
          <TrackerLeftGroup>
            <TrackerPortfolio />

            {/*<TrackerUploadThirdParty />*/}

            {/*<BoxRenderer
              wrapperHeight
              {...{
                boxName: "uploadContainer",
                fullScreen: fullScreenUpload,
                setFullScreen: setFullScreenUpload,
                className: "mt-2",
              }}
            />*/}

            <BoxRenderer
              {...{
                boxName: "downloadContainer",
                className: "my-2",
                id: "downloadContainer",
              }}
            />

            {/*<TrackerUploadLender />*/}
          </TrackerLeftGroup>

          <TrackerMiddleGroup>
            {/*<TrackerFormHeader text="Deal Status" />*/}
            {/* <TrackerStatus /> */}

            <Tab {...{ defaultIndex: 0, count: data?.missing_field_count }}>
              <TrackerAllFields
                includePadding={false}
                {...{ label: "All fields", index: 0 }}
              >
                <TrackerSummaryAllFields />
                {/*<WaiverSummary />*/}
                <WaiverInputComponents />
              </TrackerAllFields>

              <TrackerAllFields
                includePadding={data?.missing_field_count === 0}
                {...{ label: "Missing Information", index: 1 }}
              >
                <TrackerSummaryMissingFields />
              </TrackerAllFields>
            </Tab>
            <TrackerUpdate />
          </TrackerMiddleGroup>

          <TrackerRightGroup>
            <TrackerNote />
          </TrackerRightGroup>
        </TrackerBody>
      </section>
    </>
  );
};

export default DealTrackerComponents;
