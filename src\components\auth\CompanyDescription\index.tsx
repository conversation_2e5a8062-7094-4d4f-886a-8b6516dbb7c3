import styles from "./index.module.scss";

import { Lines } from "./../../svgs/Lines";

import company_logo from "../../../assets/images/company-logo.png";

export const CompanyDescription = () => {
  return (
    <section
      className={`${styles.wrapper} position-relative col-7 col-md-6 col-lg-7 col-xl-7 d-none d-md-block d-lg-block d-xl-block`}
    >
      <div
        className={`d-flex flex-column align-items-center justify-content-center h-100`}
      >
        <img src={company_logo} alt="" className="img-fluid" />

        <aside className={`${styles.description} mt-3 text-center`}>
          <div>Your Trusted Partners in</div>
          <div>Real Estate Risk Review</div>
        </aside>
      </div>

      <aside className={`position-absolute ${styles.lines}`}>
        <Lines />
      </aside>
    </section>
  );
};

export default CompanyDescription;
