import React from "react";

import styles from "./index.module.scss";

import LenderListUpload from "../../resuable/lender-list-upload";

const data: any = [];

interface ITrackerUploadLender {
  formik?: any;
}

const TrackerUploadLender = ({ formik }: ITrackerUploadLender) => {
  return (
    <section className={`${styles.wrapper} my-4`}>
      <LenderListUpload
        {...{
          formik,
          data,
          label: "Lender Document",
        }}
      />
    </section>
  );
};

export default TrackerUploadLender;
