@import "../../../../../../styles/_mixins.scss";

.label {
  color: rgba(24, 24, 24, 1);
  @include font(0.85em, 600);
}

.field {
  //padding: 6px;
  border: 1px solid rgba(241, 243, 242, 1);
  width: 100%;
  background-color: var(--white);
  @include font(0.85em, 600);

  &:focus {
    outline: 0;
    border: 1px solid var(--risk-primary);
  }
}

.formWrapper {
  max-height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
  @include scrollbars(2px);
}

.formButton {
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  border: 0;
  outline: 0;
  background-color: var(--white);
  transition: 0.2s;
  text-overflow: ellipsis;

  @include font(0.9em, 600);

  &:hover {
    background-color: rgba(0, 153, 34, 0.1);
  }

  &.boxHeight {
    height: 29px;
  }
}

.buttonStyle {
  width: 40%;
  border-radius: 2px;
  outline: 0;
  @include font(0.8em, 600);

  &:hover {
    opacity: 0.7;
  }

  &.buttonBorder {
    color: var(--risk-primary);
    border: 1px solid var(--risk-primary);
    background-color: var(white);
  }

  &.buttonGreen {
    border: 0;
    color: var(--white);
    background-color: var(--risk-primary);
  }
}

.reset {
  background-color: transparent;
  outline: none;
  border: 0;
  transition: 0.3s;
  color: rgba(0.63, 0.67, 0.65, 0.5);
  @include font(0.95em, 600);

  &:hover {
    opacity: 0.49;
  }
}
