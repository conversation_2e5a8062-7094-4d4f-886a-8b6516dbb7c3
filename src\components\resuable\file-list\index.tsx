import React from "react";

import styles from "./index.module.scss";

import CancelSvg from "./../../../assets/svgs/cancel";
import PdfSvg from "../../svgs/PdfSvg";

interface IFileList {
  file: any;
}

const FileList = ({ file }: IFileList) => {
  return (
    <section
      className={`${styles.wrapper} d-flex align-items-center justify-content-between px-3`}
    >
      <div className={`d-flex align-items-center py-2 flex-fill`}>
        <div>
          <PdfSvg />
        </div>

        <div className={`${styles.name} mx-2 mx-md-2 mx-lg-1 mx-xl-2 mx-xxl-2`}>
          {file.name}
        </div>
      </div>
      <div className={`${styles.icon} py-2`}>
        <CancelSvg />
      </div>
    </section>
  );
};

export default FileList;
