#!/usr/bin/env node

/**
 * Comprehensive test suite for Secure Token Storage
 * Tests encryption, storage, retrieval, and validation functionality
 */

const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

// Mock Electron app for testing
const mockApp = {
  getPath: (name) => {
    if (name === 'userData') {
      return path.join(__dirname, '..', 'test-data');
    }
    return path.join(__dirname, '..', 'test-data');
  }
};

// Mock electron module
require.cache[require.resolve('electron')] = {
  exports: { app: mockApp }
};

const SecureTokenStorage = require('../electron/auth/secure-token-storage');

class SecureTokenStorageTests {
  constructor() {
    this.testResults = [];
    this.testDataDir = path.join(__dirname, '..', 'test-data');
    this.setupTestEnvironment();
  }

  /**
   * Setup test environment
   */
  setupTestEnvironment() {
    // Create test data directory if it doesn't exist
    if (!fs.existsSync(this.testDataDir)) {
      fs.mkdirSync(this.testDataDir, { recursive: true });
    }
  }

  /**
   * Cleanup test environment
   */
  cleanupTestEnvironment() {
    try {
      if (fs.existsSync(this.testDataDir)) {
        fs.rmSync(this.testDataDir, { recursive: true, force: true });
      }
    } catch (error) {
      console.warn('⚠️ Could not cleanup test directory:', error.message);
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🔐 Starting Secure Token Storage Tests...');
    console.log('==========================================\n');

    try {
      this.testInitialization();
      this.testTokenStorage();
      this.testTokenRetrieval();
      this.testTokenValidation();
      this.testTokenUpdate();
      this.testTokenClear();
      this.testEncryptionSecurity();
      this.testBackupRestore();
      this.testErrorHandling();

      const passed = this.testResults.filter(r => r.passed).length;
      const total = this.testResults.length;

      console.log(`\n🔐 Secure Token Storage tests completed: ${passed}/${total} passed`);

      if (passed === total) {
        console.log('✅ All secure token storage tests passed!');
        return { success: true, passed, total };
      } else {
        console.log('❌ Some tests failed. Review the results above.');

        // Show failed tests
        const failedTests = this.testResults.filter(r => !r.passed);
        console.log('\nFailed tests:');
        failedTests.forEach(test => {
          console.log(`❌ ${test.test}: ${test.description}`);
          console.log(`   Expected: ${test.expected}, Got: ${test.actual}`);
        });

        return { success: false, passed, total };
      }
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      return { success: false, error: error.message };
    } finally {
      this.cleanupTestEnvironment();
    }
  }

  /**
   * Test storage initialization
   */
  testInitialization() {
    try {
      const storage = new SecureTokenStorage();

      this.addTestResult('Initialization', 'Storage instance created', true, !!storage, true);
      this.addTestResult('Initialization', 'Encryption key generated', true, !!storage.encryptionKey, true);
      this.addTestResult('Initialization', 'Store initialized', true, !!storage.store, true);
      this.addTestResult('Initialization', 'Meta store initialized', true, !!storage.metaStore, true);

      console.log('✅ Initialization tests passed');
    } catch (error) {
      this.addTestResult('Initialization', 'Storage creation failed', true, false, false);
      console.log('❌ Initialization tests failed:', error.message);
    }
  }

  /**
   * Test token storage
   */
  testTokenStorage() {
    try {
      const storage = new SecureTokenStorage();

      const testTokens = {
        access_token: 'test_access_token_' + Date.now(),
        refresh_token: 'test_refresh_token_' + Date.now(),
        expires_in: 3600,
        offline_expires_in: 2592000,
        user: {
          id: 'test_user_123',
          email: '<EMAIL>',
          username: 'testuser',
          role: 'user'
        }
      };

      const stored = storage.storeTokens(testTokens);
      this.addTestResult('Token Storage', 'Store tokens', true, stored, true);

      const hasTokens = storage.hasTokens();
      this.addTestResult('Token Storage', 'Has tokens after storage', true, hasTokens, true);

      console.log('✅ Token storage tests passed');
    } catch (error) {
      this.addTestResult('Token Storage', 'Storage failed', true, false, false);
      console.log('❌ Token storage tests failed:', error.message);
    }
  }

  /**
   * Test token retrieval
   */
  testTokenRetrieval() {
    try {
      const storage = new SecureTokenStorage();

      const testTokens = {
        access_token: 'retrieve_test_token',
        refresh_token: 'retrieve_refresh_token',
        expires_in: 3600,
        offline_expires_in: 2592000,
        user: { id: 'retrieve_test', email: '<EMAIL>' }
      };

      storage.storeTokens(testTokens);

      const retrieved = storage.getTokens();
      this.addTestResult('Token Retrieval', 'Retrieve stored tokens', true, !!retrieved, true);
      this.addTestResult('Token Retrieval', 'Access token matches', testTokens.access_token, retrieved?.access_token, true);
      this.addTestResult('Token Retrieval', 'User data preserved', testTokens.user.email, retrieved?.user?.email, true);

      const accessToken = storage.getAccessToken();
      this.addTestResult('Token Retrieval', 'Get access token', testTokens.access_token, accessToken, true);

      const refreshToken = storage.getRefreshToken();
      this.addTestResult('Token Retrieval', 'Get refresh token', testTokens.refresh_token, refreshToken, true);

      console.log('✅ Token retrieval tests passed');
    } catch (error) {
      this.addTestResult('Token Retrieval', 'Retrieval failed', true, false, false);
      console.log('❌ Token retrieval tests failed:', error.message);
    }
  }

  /**
   * Test token validation
   */
  testTokenValidation() {
    try {
      const storage = new SecureTokenStorage();

      // Test with valid tokens
      const validTokens = {
        access_token: 'valid_token',
        refresh_token: 'valid_refresh',
        expires_in: 3600, // 1 hour from now
        offline_expires_in: 2592000, // 30 days from now
        user: { id: 'valid_user' }
      };

      storage.storeTokens(validTokens);

      const onlineValidation = storage.validateTokens(true);
      this.addTestResult('Token Validation', 'Online validation - valid tokens', true, onlineValidation.isValid, true);

      const offlineValidation = storage.validateTokens(false);
      this.addTestResult('Token Validation', 'Offline validation - valid tokens', true, offlineValidation.isValid, true);

      // Test with no tokens
      storage.clearTokens();
      const noTokenValidation = storage.validateTokens(true);
      this.addTestResult('Token Validation', 'No tokens validation', false, noTokenValidation.isValid, true);

      console.log('✅ Token validation tests passed');
    } catch (error) {
      this.addTestResult('Token Validation', 'Validation failed', true, false, false);
      console.log('❌ Token validation tests failed:', error.message);
    }
  }

  /**
   * Test token updates
   */
  testTokenUpdate() {
    try {
      const storage = new SecureTokenStorage();

      // Store initial tokens
      const initialTokens = {
        access_token: 'initial_token',
        refresh_token: 'initial_refresh',
        expires_in: 3600,
        offline_expires_in: 2592000,
        user: { id: 'update_test' }
      };

      storage.storeTokens(initialTokens);

      // Update tokens
      const updateData = {
        access_token: 'updated_token',
        expires_in: 7200 // 2 hours
      };

      const updated = storage.updateTokens(updateData);
      this.addTestResult('Token Update', 'Update tokens', true, updated, true);

      const retrievedAfterUpdate = storage.getTokens();
      this.addTestResult('Token Update', 'Updated access token', updateData.access_token, retrievedAfterUpdate?.access_token, true);
      this.addTestResult('Token Update', 'Preserved refresh token', initialTokens.refresh_token, retrievedAfterUpdate?.refresh_token, true);

      console.log('✅ Token update tests passed');
    } catch (error) {
      this.addTestResult('Token Update', 'Update failed', true, false, false);
      console.log('❌ Token update tests failed:', error.message);
    }
  }

  /**
   * Test token clearing
   */
  testTokenClear() {
    try {
      const storage = new SecureTokenStorage();

      // Store tokens first
      storage.storeTokens({
        access_token: 'clear_test_token',
        refresh_token: 'clear_refresh_token',
        expires_in: 3600,
        user: { id: 'clear_test' }
      });

      const hasTokensBefore = storage.hasTokens();
      this.addTestResult('Token Clear', 'Has tokens before clear', true, hasTokensBefore, true);

      const cleared = storage.clearTokens();
      this.addTestResult('Token Clear', 'Clear tokens', true, cleared, true);

      const hasTokensAfter = storage.hasTokens();
      this.addTestResult('Token Clear', 'No tokens after clear', false, hasTokensAfter, true);

      const retrievedAfterClear = storage.getTokens();
      this.addTestResult('Token Clear', 'No tokens retrievable after clear', null, retrievedAfterClear, true);

      console.log('✅ Token clear tests passed');
    } catch (error) {
      this.addTestResult('Token Clear', 'Clear failed', true, false, false);
      console.log('❌ Token clear tests failed:', error.message);
    }
  }

  /**
   * Test encryption security
   */
  testEncryptionSecurity() {
    try {
      const storage1 = new SecureTokenStorage();
      const storage2 = new SecureTokenStorage();

      // Both should use the same encryption key (persistent)
      this.addTestResult('Encryption Security', 'Consistent encryption key', storage1.encryptionKey, storage2.encryptionKey, true);

      // Store tokens with first instance
      const testTokens = {
        access_token: 'encryption_test_token',
        refresh_token: 'encryption_refresh_token',
        expires_in: 3600,
        user: { id: 'encryption_test' }
      };

      storage1.storeTokens(testTokens);

      // Retrieve with second instance (should work with same key)
      const retrieved = storage2.getTokens();
      this.addTestResult('Encryption Security', 'Cross-instance token retrieval', testTokens.access_token, retrieved?.access_token, true);

      console.log('✅ Encryption security tests passed');
    } catch (error) {
      this.addTestResult('Encryption Security', 'Encryption test failed', true, false, false);
      console.log('❌ Encryption security tests failed:', error.message);
    }
  }

  /**
   * Test backup and restore
   */
  testBackupRestore() {
    try {
      const storage = new SecureTokenStorage();

      // Store test tokens
      const testTokens = {
        access_token: 'backup_test_token',
        refresh_token: 'backup_refresh_token',
        expires_in: 3600,
        user: { id: 'backup_test', email: '<EMAIL>' }
      };

      storage.storeTokens(testTokens);

      // Create backup
      const backup = storage.exportTokensBackup();
      this.addTestResult('Backup/Restore', 'Create backup', true, !!backup, true);
      this.addTestResult('Backup/Restore', 'Backup is encrypted string', 'string', typeof backup, true);

      // Clear tokens
      storage.clearTokens();
      this.addTestResult('Backup/Restore', 'Tokens cleared before restore', false, storage.hasTokens(), true);

      // Restore from backup
      const restored = storage.importTokensBackup(backup);
      this.addTestResult('Backup/Restore', 'Restore from backup', true, restored, true);

      // Verify restored tokens
      const restoredTokens = storage.getTokens();
      this.addTestResult('Backup/Restore', 'Restored access token', testTokens.access_token, restoredTokens?.access_token, true);
      this.addTestResult('Backup/Restore', 'Restored user data', testTokens.user.email, restoredTokens?.user?.email, true);

      console.log('✅ Backup/restore tests passed');
    } catch (error) {
      this.addTestResult('Backup/Restore', 'Backup/restore failed', true, false, false);
      console.log('❌ Backup/restore tests failed:', error.message);
    }
  }

  /**
   * Test error handling
   */
  testErrorHandling() {
    try {
      const storage = new SecureTokenStorage();

      // Test invalid backup import
      const invalidImport = storage.importTokensBackup('invalid_backup_data');
      this.addTestResult('Error Handling', 'Invalid backup import', false, invalidImport, true);

      // Test validation with no tokens
      const noTokenValidation = storage.validateTokens(true);
      this.addTestResult('Error Handling', 'Validation with no tokens', false, noTokenValidation.isValid, true);
      this.addTestResult('Error Handling', 'No tokens reason', 'no_tokens', noTokenValidation.reason, true);

      console.log('✅ Error handling tests passed');
    } catch (error) {
      this.addTestResult('Error Handling', 'Error handling test failed', true, false, false);
      console.log('❌ Error handling tests failed:', error.message);
    }
  }

  /**
   * Add test result
   */
  addTestResult(category, description, expected, actual, passed) {
    this.testResults.push({
      test: category,
      description,
      expected,
      actual,
      passed: passed && (expected === actual)
    });
  }
}

/**
 * Run tests when called directly
 */
if (require.main === module) {
  const tests = new SecureTokenStorageTests();
  tests.runAllTests().then(result => {
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = SecureTokenStorageTests;
