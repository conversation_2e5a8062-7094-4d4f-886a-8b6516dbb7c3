import { IReviewOrderMenu } from "./../models/reviewOrderMenu";

export const getreviewOrderMenuData = ({
  orderSummaryRef,
  loanSummaryRef,
  propertySummaryRef,
  dueDiligenceRef,
  contactsRef,
  condoRef,
  constructionRef,
  environmentalRef,
  tenantRef,
  suppHeaderRef,
}: any): IReviewOrderMenu[] => {
  let data = [
    {
      id: 1,
      ref: orderSummaryRef,
      name: "Order Summary",
    },
    {
      id: 2,
      ref: loanSummaryRef,
      name: "Loan Summary",
    },
    {
      id: 3,
      ref: propertySummaryRef,
      name: "Property Summary",
    },
    {
      id: 4,
      ref: dueDiligenceRef,
      name: "Due Diligence",
    },
    {
      id: 5,
      ref: contactsRef,
      name: "Contacts",
    },
    {
      id: 6,
      ref: suppHeaderRef,
      name: "Supplemental Order",
    },
    {
      id: 7,
      ref: condoRef,
      name: "Condo",
    },
    {
      id: 8,
      ref: constructionRef,
      name: "Construction",
    },
    {
      id: 9,
      ref: environmentalRef,
      name: "Environmental",
    },
    {
      id: 10,
      ref: tenantRef,
      name: "Tenant Summary",
    },
  ];
  return data;
};
