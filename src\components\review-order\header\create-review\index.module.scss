@import "../../../../../styles/_mixins.scss";

.createReview {
  background: var(--risk-primary);
  cursor: pointer;
  transition: 0.3;
  padding: 10px 40px;
  /*border-radius: 0px;*/
}

.createReview:hover {
  opacity: 0.8;
}

.reviewOrder {
  font-size: 1em;
}

.buttonBorder {
  width: 30%;
  border-radius: 2px;
  outline: 0;
  color: var(--risk-primary);
  border: 1px solid var(--risk-primary);
  background-color: #ffffff;
  @include font(0.9em, 600);
  transition: 0.3s;
  &:hover {
    opacity: 0.7;
  }
}

.button {
  width: 30%;
  border-radius: 2px;
  outline: 0;
  border: 0;
  color: var(--white);
  background-color: var(--risk-primary);
  transition: 0.3s;
  @include font(0.9em, 600);

  &:hover {
    opacity: 0.7;
  }
}
