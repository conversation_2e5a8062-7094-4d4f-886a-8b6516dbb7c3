@import "../../../../../styles/_mixins.scss";

.filterButton {
  background: #ffffff;
  color: black;
  outline: none;
  border: 1px solid var(--risk-primary);
  cursor: pointer;

  @include font(0.9em, 600);
}

.svgBg {
  background-color: var(--risk-primary);
  width: 14px;
  height: 14px;
}

.tag {
  //display: inline-grid;
  //grid-template-columns: repeat(4, auto);
  //justify-items: stretch;
  //align-items: center;
  //justify-content: start;
  //flex: nowrap;
  max-height: 4rem;
  overflow-y: auto;
  row-gap: 5px;
  padding: 3px 6px;
  cursor: pointer;
  @include scrollbars(3px);

  @media screen and (max-width: 1000px) {
    grid-template-columns: repeat(3, auto);
  }

  @media screen and (max-width: 1300px) {
    grid-template-columns: repeat(3, auto);
  }
}

.fieldTagValue {
  overflow: hidden;
  max-width: 7rem;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cancelButton {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 20px;
  height: 20px;
  background-color: #ffffff;
  border-radius: 100%;
  transition: opacity 0.3s;

  &:hover {
    opacity: 0.7;
  }
}
