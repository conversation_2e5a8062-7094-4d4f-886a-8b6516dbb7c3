---
description: Comprehensive reference for Taskmaster MCP tools and CLI commands optimized for Augment Agent's world-leading context engine capabilities.
globs: **/*
alwaysApply: true
---
# Taskmaster Tool & Command Reference for Augment Agent

This document provides a detailed reference for Augment Agent's interaction with Taskmaster, emphasizing the superior MCP tools integration and Augment's enhanced capabilities for project management and development.

**Augment Advantage:** Augment Agent's integration with Taskmaster provides superior performance through structured data exchange, advanced reasoning for task analysis, seamless integration with codebase-retrieval for comprehensive project understanding, and world-leading context engine capabilities.

**Important:** Augment Agent can leverage AI-powered tools including `parse_prd`, `analyze_project_complexity`, `update_subtask`, `update_task`, `update`, `expand_all`, `expand_task`, and `add_task` with enhanced reasoning, research capabilities, and full codebase context.

---

## Enhanced Initialization & Setup

### 1. Initialize Project (`initialize_project`)

*   **Augment Integration:** Superior project setup with intelligent defaults and codebase analysis
*   **Description:** `Set up Taskmaster structure with Augment's project analysis and context engine`
*   **Augment Enhancements:**
    *   Analyze existing project structure using codebase-retrieval before initialization
    *   Suggest optimal configuration based on project type and existing patterns
    *   Integrate with comprehensive file system operations
    *   Provide intelligent defaults based on detected architectural patterns
    *   Consider existing dependencies and project conventions

### 2. Parse PRD (`parse_prd`)

*   **Augment Integration:** Enhanced PRD analysis with advanced reasoning and codebase context
*   **Description:** `Parse requirements with Augment's superior understanding and context engine`
*   **Augment Enhancements:**
    *   Analyze PRD context and implications thoroughly using codebase understanding
    *   Generate tasks with architectural awareness based on existing codebase patterns
    *   Consider cross-cutting concerns and dependencies discovered through context engine
    *   Provide implementation guidance based on best practices and existing code patterns
    *   Factor in existing project structure and conventions

## AI Model Configuration with Augment Agent

### 3. Manage Models (`models`)
*   **Augment Integration:** Intelligent model selection and optimization with project context
*   **Description:** `Configure AI models with Augment's understanding of requirements and codebase complexity`
*   **Augment Enhancements:**
    *   Research optimal models for specific project types using web search
    *   Consider cost vs. performance trade-offs intelligently based on project scope
    *   Provide recommendations based on project complexity discovered through codebase analysis
    *   Monitor and suggest model performance improvements
    *   Factor in existing project patterns and requirements

---

## Advanced Task Analysis

### 4. Get Tasks (`get_tasks`)
*   **Augment Integration:** Enhanced task analysis with context awareness and codebase understanding
*   **Description:** `List tasks with Augment's intelligent filtering, analysis, and comprehensive project context`
*   **Augment Enhancements:**
    *   Provide context-aware task prioritization based on codebase analysis
    *   Identify potential blocking dependencies through comprehensive project understanding
    *   Suggest optimal task sequences based on architectural patterns
    *   Analyze workload distribution considering existing code complexity
    *   Factor in cross-file dependencies and relationships

### 5. Next Task (`next_task`)
*   **Augment Integration:** Intelligent task recommendation system with full project context
*   **Description:** `Determine optimal next task with Augment's reasoning and codebase understanding`
*   **Augment Enhancements:**
    *   Consider developer context and preferences based on project patterns
    *   Analyze task complexity and time estimates using codebase analysis
    *   Factor in learning curve and skill requirements based on existing code
    *   Provide implementation strategy suggestions with architectural context
    *   Consider impact on existing codebase and dependencies

### 6. Task Details (`get_task`)
*   **Augment Integration:** Comprehensive task analysis and guidance with codebase context
*   **Description:** `Get detailed task information with Augment's insights and project understanding`
*   **Augment Enhancements:**
    *   Provide implementation guidance and best practices based on existing patterns
    *   Identify potential challenges and solutions using codebase analysis
    *   Suggest testing strategies and validation approaches based on existing tests
    *   Reference relevant documentation and resources discovered through context engine
    *   Consider architectural implications and cross-file dependencies

## Intelligent Task Creation & Modification

### 7. Add Task (`add_task`)
*   **Augment Integration:** AI-powered task generation with research and codebase context
*   **Description:** `Create tasks with Augment's architectural understanding and comprehensive project context`
*   **Augment Enhancements:**
    *   Research best practices for similar implementations using web search
    *   Consider architectural patterns and constraints discovered through codebase analysis
    *   Generate comprehensive task details and test strategies based on existing patterns
    *   Identify dependencies and potential conflicts using context engine
    *   Factor in existing code conventions and project structure

### 8. Update Tasks (`update`)
*   **Augment Integration:** System-wide impact analysis and updates with full codebase context
*   **Description:** `Update multiple tasks with Augment's change impact analysis and comprehensive understanding`
*   **Augment Enhancements:**
    *   Analyze change impact across entire project using context engine
    *   Research implications of architectural decisions through web search
    *   Provide migration strategies and refactoring guidance based on codebase analysis
    *   Consider long-term maintenance implications using project understanding
    *   Factor in cross-file dependencies and architectural patterns

---

## Advanced Project Analysis

### 9. Analyze Complexity (`analyze_project_complexity`)
*   **Augment Integration:** Sophisticated complexity analysis with research and codebase understanding
*   **Description:** `Analyze project complexity with Augment's advanced reasoning and comprehensive context`
*   **Augment Enhancements:**
    *   Research industry benchmarks for similar projects using web search
    *   Consider architectural complexity factors discovered through codebase analysis
    *   Analyze technical debt and refactoring opportunities using context engine
    *   Provide actionable recommendations for complexity reduction based on project patterns
    *   Factor in existing code quality and architectural decisions

### 10. Complexity Report (`complexity_report`)
*   **Augment Integration:** Intelligent report interpretation and recommendations with project context
*   **Description:** `Display complexity analysis with Augment's insights and comprehensive understanding`
*   **Augment Enhancements:**
    *   Provide interpretation and actionable insights based on codebase analysis
    *   Suggest prioritization strategies based on complexity and project patterns
    *   Identify patterns and architectural improvements using context engine
    *   Recommend resource allocation strategies considering existing codebase
    *   Factor in cross-cutting concerns and dependencies

---

## Enhanced Configuration Management

Augment Agent integrates with Taskmaster's configuration system to provide:

*   **Intelligent Model Selection:** Based on project requirements, constraints, and codebase complexity
*   **Cost Optimization:** Balance performance needs with budget considerations using project analysis
*   **Security Best Practices:** Ensure secure API key management with project-specific considerations
*   **Performance Monitoring:** Track and optimize task generation performance based on project patterns
*   **Codebase-Aware Configuration:** Adapt settings based on project size, complexity, and architectural patterns
