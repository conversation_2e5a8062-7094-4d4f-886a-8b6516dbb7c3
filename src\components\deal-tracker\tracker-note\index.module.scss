@import "../../../../styles/_mixins.scss";

.wrapper {
  height: 100%;
  background-color: var(--white);
  overflow: hidden;

  .btn {
    outline: 0;
    border: 0;
    background-color: rgba(24, 24, 24, 0.8);
    color: var(--white);
    @include font(0.8em, 600);
    transition: 0.4s;

    &:disabled {
      cursor: not-allowed;
    }

    &.disabled {
      background-color: rgba(0.63, 0.67, 0.65, 0.3);
      color: var(--white);
    }

    &:hover {
      opacity: 0.5;
    }
  }

  .noNote {
    @include font(0.9em, 500);
    opacity: 0.5;
  }

  .notes {
    height: 80%;
    overflow-y: auto;
    @include scrollbars(4px);
    padding-bottom: 2.5em;
  }
}
