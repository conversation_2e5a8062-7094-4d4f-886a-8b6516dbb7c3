import React from "react";

import { Field } from "formik";

import styles from "./index.module.scss";

export default function SelectFieldProperty({ label, name, options }: any) {
  return (
    <div className="my-2">
      <span className={styles.label}>{label}</span>

      <Field as="select" name={name} className={`${styles.field}`}>
        <option className="text-danger"></option>
        {options.map((option: any) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </Field>
    </div>
  );
}
