export const getByID = (id: string) => document.getElementById(id);

export const appendScript = (script: Node) => {
  document.getElementsByTagName("head")[0].appendChild(script);
};

export const createScript = (src: string, id: string, onload: () => void) => {
  const script = document.createElement("script");

  script.setAttribute("id", id);

  script.src = src;

  script.onload = onload;

  script.defer = true;

  appendScript(script);
};
