---
description: Guidelines for creating and maintaining Claude rules to ensure consistency and effectiveness.
globs: .claude/rules/*.mdc
alwaysApply: true
---

- **Required Rule Structure:**
  ```markdown
  ---
  description: Clear, one-line description of what the rule enforces
  globs: path/to/files/*.ext, other/path/**/*
  alwaysApply: boolean
  ---

  - **Main Points in Bold**
    - Sub-points with details
    - Examples and explanations
  ```

- **File References:**
  - Use `[filename](mdc:path/to/file)` ([filename](mdc:filename)) to reference files
  - Example: [prisma.mdc](mdc:.claude/rules/prisma.mdc) for rule references
  - Example: [schema.prisma](mdc:prisma/schema.prisma) for code references
  - <PERSON> can read files directly using Desktop Commander tools

- **Code Examples:**
  - Use language-specific code blocks with clear DO/DON'T patterns
  ```typescript
  // ✅ DO: Show good examples
  const goodExample = true;
  
  // ❌ DON'T: Show anti-patterns
  const badExample = false;
  ```

- **Rule Content Guidelines:**
  - Start with high-level overview
  - Include specific, actionable requirements
  - Show examples of correct implementation
  - Reference existing code when possible
  - Keep rules DRY by referencing other rules
  - Leverage <PERSON>'s ability to analyze entire codebases

- **Rule Maintenance:**
  - Update rules when new patterns emerge
  - Add examples from actual codebase using Claude's file analysis
  - Remove outdated patterns
  - Cross-reference related rules
  - Use Claude's web search for latest best practices

- **Best Practices:**
  - Use bullet points for clarity
  - Keep descriptions concise
  - Include both DO and DON'T examples
  - Reference actual code over theoretical examples
  - Use consistent formatting across rules
  - Leverage Claude's superior code understanding for complex patterns

- **Claude-Specific Enhancements:**
  - Utilize Claude's ability to understand context across multiple files
  - Reference Claude's web search capabilities for up-to-date information
  - Use Claude's Desktop Commander integration for file operations
  - Leverage Claude's artifact creation for complex examples
  - Take advantage of Claude's superior reasoning for rule validation
  - Use Claude's analysis tool for complex calculations and data processing
