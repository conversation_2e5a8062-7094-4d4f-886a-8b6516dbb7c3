import React from "react";

import styles from "./index.module.scss";

import InfoSvg from "./../../svgs/Info";

import ProgressBar from "./../../resuable/progress";
import CustomModal from "../../resuable/modal";
import TrackerCheckList from "./../tracker-checklist/index";
import { checkListData } from "../../../data/tracker-form";

interface ITrackerStatus {
  formik?: any;
  isLoading?: boolean;
}

const TrackerStatus = ({ formik, isLoading }: ITrackerStatus) => {
  const [show, setShow] = React.useState(false);

  const handleValues = (v: any) => {
    const length = v.items.length;
    const percentage = v.header.percentage;

    const currentValues = v.items
      .map((v: any) => {
        return formik.values?.[v.name];
      })
      .filter(Boolean);

    const percentPerItem = +(+percentage / +length).toFixed(2);

    const attainablePercentage = percentPerItem * currentValues.length;

    return attainablePercentage;
  };

  const refdV = () => {
    const aggregate = checkListData.map((v: any) => {
      const answer = handleValues(v);
      return answer;
    });

    const summation = aggregate.reduce((a, b) => a + b, 0);

    return Math.floor(summation);
  };

  const retainedValue = refdV();

  return (
    <>
      <CustomModal
        {...{
          title: "Checklist",
          show,
          setShow,
          columnLayout: "col-12 col-md-7 col-lg-5 col-xl-4",
          align: "align-items-start pt-5",
        }}
      >
        <TrackerCheckList {...{ progress: retainedValue, formik, isLoading }} />
      </CustomModal>

      <section className={`${styles.wrapper} position-relative w-100`}>
        <div
          className={`${styles.info} position-absolute w-100 d-flex align-items-center justify-content-end`}
        >
          <InfoSvg />
          <span className={`${styles.checkList} mx-2`}>
            Click Status bar to view checklist
          </span>
        </div>

        <div className="">
          <ProgressBar
            {...{
              color: "success",
              progress: retainedValue,
              handleClick: setShow,
              height: "15px",
            }}
          />
        </div>
      </section>
    </>
  );
};

export default TrackerStatus;
