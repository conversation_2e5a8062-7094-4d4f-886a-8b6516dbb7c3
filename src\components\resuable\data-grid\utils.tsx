import React from "react";

import { cloneElement } from "react";

import type { ReactElement } from "react";

import { textEditor } from "react-data-grid";

import styles from "./index.module.scss";
import DeleteSvg from "../../svgs/Delete";
import CustomModal from "../modal";
import { usePost } from "../../../apis";

import { useParams } from "react-router-dom";

export const extractColumns = (data: Array<any>) => {
  const values = Object.values(data[0]);

  const columns = [];

  values.forEach((value: any) => {
    const { name, options, value: dataValue, label, order } = value || {};

    if (typeof value === "object" && !!options && options?.length > 0) {
      if (!!name && !!order) {
        columns.push({
          key: name,
          name: label,
          sortable: true,
          resizable: true,
          order_$op: order,
          frozen: name === "property_name" ? true : false,
          formatter(props: any) {
            return <>{props.row?.[name]}</>;
          },
          editor: ({ row, onRowChange }: any) => (
            <CustomDropdown
              row={row}
              onRowChange={onRowChange}
              value={dataValue}
              name={name}
              optionName={name + "_$op"}
            />
          ),
          editorOptions: {
            editOnClick: true,
          },
        });
      }
    }

    if (typeof value === "object" && !!!options) {
      if (!!name && !!order) {
        columns.push({
          key: name,
          name: label,
          editor: textEditor,
          sortable: true,
          resizable: true,
          order_$op: order,
          frozen: name === "property_name" ? true : false,
        });
      }
    }
  });

  columns.sort((a, b) => (a.order_$op > b.order_$op ? 1 : -1));

  return [
    ...columns,
    {
      key: "action",
      name: "",
      editor: ActionComponent,
      sortable: true,
      resizable: true,
      order_$op: 0,
      frozen: false,
    },
  ];
};

export const extractRows = (data: Array<any>) => {
  const rows = [];

  data?.forEach((item: any, i) => {
    const itemCopy = { ...item };

    let obj = {};

    obj["portfolio_property_id"] = itemCopy.portfolio_property_id;
    obj["deal_id"] = itemCopy.deal_id;
    obj["id"] = ++i;

    delete itemCopy.portfolio_property_id;
    delete itemCopy.deal_id;

    const values: any = Object.values(itemCopy);

    values.forEach((value: any) => {
      const { options, value: dataValue, name } = value;

      if (!!options && options?.length > 0) {
        obj[name] = dataValue;
        obj[name + "_$op"] =
          typeof options === "string"
            ? options.replace(/[{}]/g, "").split(",")
            : options;
      }

      obj[name] = dataValue;
    });

    obj["action"] = "delete";

    rows.push(obj);

    obj = {};
  });

  return rows;
};

const ActionComponent = ({ row }: any) => {
  const [show, setShow] = React.useState(false);

  const handleShow = () => setShow(true);

  const { id: deal_id }: { [key: string]: any } = useParams();

  const { mutate, isLoading } = usePost({
    path: "/order/portfolio/property",
    key: `/order/portfolio/property/${deal_id}`,
    showErr: true,
    message: "Portfolio property succcessfully deleted!",
    cb: setShow,
  });

  const handleDelete = () => {
    const id = row?.["portfolio_property_id"];

    if (!id || !deal_id) return;

    const payload: any = {
      is_deleted: true,
      portfolio_property_id: id,
      deal_id,
    };

    mutate(payload);
  };

  return (
    <>
      <CustomModal
        {...{
          title: "Delete Portfolio Property",
          show,
          setShow,
          columnLayout: "col-12 col-md-6 col-lg-5 col-xl-4",
          align: "align-items-center",
        }}
      >
        <div className="text-center my-3">
          <div className={styles.delete}>
            Do you want to delete this portfolio property?
          </div>

          <div className="mt-3">
            <button
              className={`${styles.buttonBorder} mr-2 py-2`}
              onClick={() => setShow(false)}
              type="button"
            >
              No
            </button>

            <button
              disabled={isLoading}
              type="button"
              onClick={handleDelete}
              className={`${styles.button} py-2`}
            >
              {isLoading ? "Deleting..." : "Yes"}
            </button>
          </div>
        </div>
      </CustomModal>

      <button
        className="w-100"
        aria-label="delete portfolio"
        style={{ outline: 0, border: 0 }}
        onClick={handleShow}
      >
        <DeleteSvg />
      </button>
    </>
  );
};

//const TextEditor = ({ row, column, onRowChange, onClose }: any) => {
//  return (
//      <div className={styles.hoverItem}>You can hover here man</div>
//      <input
//        className={styles.customInput}
//        //value={row[column.key]}
//        onChange={(event) =>
//          onRowChange({ ...row, [column.key]: event.target.value })
//        }
//        onBlur={() => onClose(true, false)}
//      />
//  );
//};

const CustomDropdown = ({ row, onRowChange, value, name, optionName }: any) => {
  return (
    <select
      value={""}
      onChange={(event) =>
        onRowChange({ ...row, [name]: event.target.value }, true)
      }
      className="w-100 border-none h-100"
      autoFocus
    >
      <option></option>
      {row?.[optionName].map((title) => (
        <option key={title} value={title}>
          {title}
        </option>
      ))}
    </select>
  );
};

interface DataGridProps {
  enableVirtualization: boolean;
}

export async function exportToCsv(
  gridElement: ReactElement<DataGridProps>,
  fileName: string
) {
  const { head, body, foot } = await getGridContent(gridElement);
  const content = [...head, ...body, ...foot]
    .map((cells) => cells.map(serialiseCellValue).join(","))
    .join("\n");

  downloadFile(
    fileName,
    new Blob([content], { type: "text/csv;charset=utf-8;" })
  );
}

export async function exportToXlsx(
  gridElement: ReactElement<DataGridProps>,
  fileName: string
) {
  const [{ utils, writeFile }, { head, body, foot }] = await Promise.all([
    import("xlsx"),
    getGridContent(gridElement),
  ]);
  const wb = utils.book_new();
  const ws = utils.aoa_to_sheet([...head, ...body, ...foot]);
  utils.book_append_sheet(wb, ws, "Sheet 1");
  writeFile(wb, fileName);
}

async function getGridContent(gridElement: ReactElement<DataGridProps>) {
  const { renderToStaticMarkup } = await import("react-dom/server");
  const grid = document.createElement("div");
  grid.innerHTML = renderToStaticMarkup(
    cloneElement(gridElement, {
      enableVirtualization: false,
    })
  );

  return {
    head: getRows(".rdg-header-row"),
    body: getRows(".rdg-row:not(.rdg-summary-row)"),
    foot: getRows(".rdg-summary-row"),
  };

  function getRows(selector: string) {
    return Array.from(grid.querySelectorAll<HTMLDivElement>(selector)).map(
      (gridRow) => {
        return Array.from(
          gridRow.querySelectorAll<HTMLDivElement>(".rdg-cell")
        ).map((gridCell) => gridCell.innerText);
      }
    );
  }
}

function serialiseCellValue(value: unknown) {
  if (typeof value === "string") {
    const formattedValue = value.replace(/"/g, '""');
    return formattedValue.includes(",")
      ? `"${formattedValue}"`
      : formattedValue;
  }
  return value;
}

function downloadFile(fileName: string, data: Blob) {
  const downloadLink = document.createElement("a");
  downloadLink.download = fileName;
  const url = URL.createObjectURL(data);
  downloadLink.href = url;
  downloadLink.click();
  URL.revokeObjectURL(url);
}
