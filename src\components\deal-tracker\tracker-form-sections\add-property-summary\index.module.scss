@import "../../../../../styles/_mixins.scss";

.wrapper {
  height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
  @include scrollbars(2px);
}

.buttonBorder {
  width: 30%;
  border-radius: 2px;
  outline: 0;
  color: var(--risk-primary);
  border: 1px solid var(--risk-primary);
  background-color: #ffffff;
  @include font(0.9em, 600);
  transition: 0.3s;
  &:hover {
    opacity: 0.7;
  }
}

.button {
  width: 30%;
  border-radius: 2px;
  outline: 0;
  border: 0;
  color: var(--white);
  background-color: var(--risk-primary);
  transition: 0.3s;
  @include font(0.9em, 600);

  &:hover {
    opacity: 0.7;
  }
}
