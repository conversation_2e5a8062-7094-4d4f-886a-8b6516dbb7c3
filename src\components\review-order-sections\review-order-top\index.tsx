import React from "react";

import {
  GoBackIcon,
  LastSaveIcon,
  OptionsIcon,
  PortfolioIcon,
} from "../../../exports";

import StatusButton from "./../resuable-status-btn";
import { ConnectionIndicator } from "../../connection-status";
import { useElectron } from "../../../hooks/useElectron";

import styles from "./index.module.css";

import { useHistory } from "react-router";

import dayjs from "dayjs";

import PropertyTitle from "../property-title";

interface IReviewOrderTopProps {
  // order?: string;
  // status?: string | boolean | undefined;
  // lastSaved?: string;
  handleAutoSave?: () => void;
  apiStatus?: boolean;
  isCreating?: boolean;
  // isCreatingError?: any;
  isCreatingData?: any;

  topData?: any;
}

const ReviewOrderTop = ({
  // order,
  // status,
  // lastSaved,
  apiStatus,
  isCreating,
  // isCreatingError,
  isCreatingData,
  topData,
}: IReviewOrderTopProps) => {
  const history = useHistory();
  const { isElectron } = useElectron();

  return (
    <>
      {!!apiStatus ? (
        <>
          <section
            className="container-fluid position-sticky bg-white pt-3 pb-3"
            style={{ top: 0, zIndex: 90 }}
          >
            <div className="d-flex flex-column flex-sm-column flex-md-column flex-lg-column flex-xl-row align-items-center justify-content-between">
              <div
                onClick={() => history.push("/deals")}
                style={{ cursor: "pointer" }}
                className="d-flex"
              >
                <img
                  src={GoBackIcon}
                  className="img-fluid mr-0 mr-sm-0 mr-md-2 mr-lg-3 mr-xl-5"
                  alt="previous page icon"
                />

                <aside className="d-flex align-items-center mt-2">
                  <div className="mr-3">
                    <span
                      className={`${styles.reviewOrder} font-weight-bold text-capitalize`}
                    >
                      {isCreatingData?.order_number || "New Deal"}
                    </span>
                  </div>
                  <StatusButton {...{ status: isCreatingData?.status }} />
                  {/* Connection indicator for Electron */}
                  {isElectron && (
                    <div className="ml-2">
                      <ConnectionIndicator
                        variant="dot"
                        size="small"
                        position="inline"
                      />
                    </div>
                  )}
                </aside>
              </div>

              {isCreatingData?.portfolio_name && (
                <aside className="d-flex align-items-center mt-2">
                  <img
                    src={PortfolioIcon}
                    className="img-fluid"
                    alt="portfolio icon"
                  />
                  <div className={`${styles.titleFont} mx-1`}>
                    <span className="border-right border-dark mr-1 pr-1">
                      portfolio
                    </span>
                    <span> {isCreatingData?.portfolio_name}</span>
                  </div>
                  <img
                    src={OptionsIcon}
                    className="img-fluid"
                    alt="options icon"
                    style={{ marginTop: "2px" }}
                  />
                </aside>
              )}

              <aside className="d-flex align-items-center mt-2">
                {isCreatingData && (
                  <>
                    {isCreatingData?.last_saved_date && (
                      <>
                        {" "}
                        <div>
                          <img
                            src={LastSaveIcon}
                            className="img-fluid"
                            alt="last saved icon"
                          />
                        </div>
                        <div className={`${styles.titleFont} mx-2`}>
                          Last saved:{" "}
                          <span className="font-weight-bold">
                            {
                              // eslint-disable-next-line
                              dayjs(isCreatingData?.last_saved_date)
                            }
                          </span>
                        </div>
                        <div>
                          <img
                            src={OptionsIcon}
                            className="img-fluid d-flex align-self-center"
                            alt="options icon"
                            style={{ marginTop: "2px" }}
                          />
                        </div>
                      </>
                    )}
                  </>
                )}
                <button
                  type="submit"
                  className={`${styles.saveButton} px-3 ml-2`}
                >
                  {isCreating ? (
                    <div className="spinner-border spinner-border-sm text-success"></div>
                  ) : (
                    "Save"
                  )}
                </button>
              </aside>
            </div>
            {topData?.property_name && topData?.mortgage_lender && (
              <PropertyTitle
                {...{
                  mortgageLender: topData?.mortgage_lender,
                  propertyName: topData?.property_name,
                }}
              />
            )}
          </section>
        </>
      ) : (
        <section
          className="container-fluid position-sticky bg-white pt-3 pb-3"
          style={{ top: 0, zIndex: 90 }}
        >
          <div className="d-flex flex-column">
            <div className="d-flex flex-column flex-sm-column flex-md-column flex-lg-column flex-xl-row align-items-center justify-content-between">
              <div
                className="mr-5"
                onClick={() => history.goBack()}
                style={{ cursor: "pointer" }}
              >
                <img
                  src={GoBackIcon}
                  className="img-fluid"
                  alt="previous page icon"
                />
              </div>
              {topData && (
                <>
                  <aside
                    className="d-flex align-items-center mt-2"
                    style={{ flex: 2 }}
                  >
                    <div className="mr-3">
                      <span
                        className={`${styles.reviewOrder} font-weight-bold`}
                      >
                        {topData?.order_number}
                      </span>
                    </div>
                    <StatusButton {...{ status: topData?.status }} />
                  </aside>

                  <aside
                    className="d-flex align-items-center mt-2"
                    style={{ flex: 2 }}
                  >
                    <img
                      src={PortfolioIcon}
                      className="img-fluid"
                      alt="portfolio icon"
                    />
                    <div className={`${styles.titleFont} mx-1`}>
                      <span className="border-right border-dark mr-1 pr-1">
                        portfolio
                      </span>
                      <span> {topData?.portfolio_name || "N/A"}</span>
                    </div>
                    <img
                      src={OptionsIcon}
                      className="img-fluid"
                      alt="options icon"
                      style={{ marginTop: "2px" }}
                    />
                  </aside>

                  <aside
                    className="d-flex align-items-center mt-2"
                    style={{ flex: 2 }}
                  >
                    <div>
                      <img
                        src={LastSaveIcon}
                        className="img-fluid"
                        alt="last saved icon"
                      />
                    </div>
                    <div className={`${styles.titleFont} mx-2`}>
                      Last saved:{" "}
                      <span className="font-weight-bold">
                        {dayjs(new Date(topData?.last_saved_date)).format(
                          "MMMM/DD/YYYY"
                        )}
                      </span>
                    </div>
                    <div>
                      <img
                        src={OptionsIcon}
                        className="img-fluid d-flex align-self-center"
                        alt="options icon"
                      />
                    </div>
                    <button
                      type="submit"
                      className={`${styles.saveButton} px-3 ml-2  `}
                    >
                      {isCreating ? (
                        <div className="spinner-border spinner-border-sm text-success"></div>
                      ) : (
                        "Save"
                      )}
                    </button>
                  </aside>
                </>
              )}
            </div>
            <div>
              {topData?.mortgage_lender && topData?.property_name && (
                <PropertyTitle
                  {...{
                    mortgageLender: topData?.mortgage_lender,
                    propertyName: topData?.property_name,
                  }}
                />
              )}
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default ReviewOrderTop;
