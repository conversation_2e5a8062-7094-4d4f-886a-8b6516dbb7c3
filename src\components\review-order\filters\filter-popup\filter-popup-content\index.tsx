import React from "react";

import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";

import MultiSelectFieldProperty from "../../multi-select";
import SelectFieldProperty from "../../select-field-property";
import DateRangeField from "../../date-range-field";

import styles from "./index.module.scss";

import PortfolioField from "../../porfolio-field";

import { buildFilterFields } from "../../../../../utils/filtersUtil";

const switchField = ({ type, ...props }) => {
  switch (type) {
    case "multiSelect":
      return <MultiSelectFieldProperty {...props} />;
    case "select":
      return <SelectFieldProperty {...props} />;
    case "dateRange":
      return <DateRangeField {...props} />;

    case "portfolio":
      return <PortfolioField {...props} />;
    default:
      throw new Error("Invalid type");
  }
};

export const FilterPopupContent = ({
  formik,
  setShow,
  data,
  handleReset,
}: any) => {
  const handleSubmitForm = (e: React.SyntheticEvent<HTMLButtonElement>) => {
    e.preventDefault();

    console.log(formik.values, "man");

    formik.submitForm();
    setShow(false);
  };

  return (
    <section className="container px-4">
      <div className="row my-4 align-items-center">
        {buildFilterFields(data?.dropdown || []).map((field, index) => {
          return (
            <div key={index} className="col-12 col-lg-6 col-xl-6">
              {switchField({ ...field, formik })}
            </div>
          );
        })}
      </div>

      <aside className="mt-5 mb-3 text-center">
        <aside className="d-flex justify-content-center">
          <button
            className={`${styles.buttonStyle} ${styles.buttonBorder} mr-2 py-2`}
            type="button"
            onClick={() => setShow(false)}
          >
            Cancel
          </button>

          <button
            type="submit"
            onClick={handleSubmitForm}
            className={`${styles.buttonStyle} ${styles.buttonGreen} py-2`}
          >
            Apply
          </button>
        </aside>

        <button
          onClick={handleReset}
          className={`btn ${styles.reset} bg-none py-2 mt-4`}
        >
          Reset filters
        </button>
      </aside>
    </section>
  );
};

export default FilterPopupContent;
