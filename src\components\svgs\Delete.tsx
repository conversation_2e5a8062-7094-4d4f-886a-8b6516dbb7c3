import React from "react";

const DeleteSvg = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.0001 4C10.4488 3.99997 10.8805 4.1725 11.2067 4.48219C11.5329 4.79189 11.7287 5.21527 11.7541 5.66548L11.7568 5.76543H14.5947C14.6974 5.76546 14.7963 5.80468 14.8714 5.87515C14.9464 5.94562 14.9921 6.04209 14.9991 6.14507C15.0061 6.24805 14.9739 6.34987 14.9091 6.42994C14.8443 6.51001 14.7516 6.56237 14.6498 6.57644L14.5947 6.58025H14.1644L13.4725 13.6528C13.4379 14.0054 13.2796 14.3342 13.026 14.5804C12.7725 14.8265 12.4401 14.9741 12.0882 14.9967L11.9931 15H8.00706C7.65434 15 7.31311 14.8739 7.04435 14.6444C6.7756 14.4148 6.59684 14.0967 6.54002 13.7468L6.52759 13.6523L5.83515 6.58025H5.40541C5.30744 6.58024 5.21279 6.54459 5.13896 6.47988C5.06513 6.41517 5.01711 6.32578 5.00378 6.22825L5 6.17284C5 6.07439 5.03548 5.97927 5.09988 5.90507C5.16427 5.83088 5.25322 5.78262 5.35027 5.76923L5.40541 5.76543H8.24328C8.24328 5.29721 8.42837 4.84817 8.75783 4.51708C9.08729 4.186 9.53414 4 10.0001 4ZM13.3498 6.58025H6.64975L7.33462 13.5724C7.34979 13.7285 7.4182 13.8744 7.52826 13.9855C7.63832 14.0966 7.78323 14.166 7.93841 14.1819L8.00706 14.1852H11.9931C12.3174 14.1852 12.5925 13.9543 12.6552 13.6409L12.666 13.5724L13.3493 6.58025H13.3498Z"
        fill="#DB4655"
      />
      <path
        d="M10.7 8.3104C10.7725 8.3104 10.8425 8.34661 10.8972 8.41234C10.9518 8.47806 10.9873 8.56885 10.9972 8.66792L11 8.7242V12.5863C11 12.6912 10.9711 12.7921 10.9192 12.8687C10.8673 12.9453 10.7963 12.9919 10.7204 12.999C10.6446 13.0062 10.5696 12.9734 10.5107 12.9072C10.4517 12.841 10.4132 12.7465 10.4028 12.6426L10.4 12.5863V8.7242C10.4 8.61445 10.4316 8.5092 10.4879 8.4316C10.5441 8.35399 10.6204 8.3104 10.7 8.3104ZM9.3 8.3104C9.3725 8.3104 9.44254 8.34661 9.49717 8.41234C9.55181 8.47806 9.58734 8.56885 9.5972 8.66792L9.6 8.7242V12.5863C9.59998 12.6912 9.5711 12.7921 9.51921 12.8687C9.46732 12.9453 9.39628 12.9919 9.32045 12.999C9.24462 13.0062 9.16964 12.9734 9.11068 12.9072C9.05172 12.841 9.01316 12.7465 9.0028 12.6426L9 12.5863V8.7242C9 8.61445 9.03161 8.5092 9.08787 8.4316C9.14413 8.35399 9.22044 8.3104 9.3 8.3104ZM10 5C9.82432 5.00001 9.65507 5.09113 9.52584 5.25528C9.39661 5.41942 9.31686 5.64459 9.3024 5.88608L9.3 5.96553H10.7C10.7 5.70946 10.6263 5.46387 10.495 5.2828C10.3637 5.10173 10.1857 5 10 5Z"
        fill="#DB4655"
      />
    </svg>
  );
};

export default DeleteSvg;
