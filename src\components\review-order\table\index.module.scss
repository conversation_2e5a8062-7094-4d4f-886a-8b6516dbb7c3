@import "../../../../styles/_mixins.scss";

.wrapper {
  background-color: rgba(248, 248, 248, 1);
}

.tableSpace {
  width: 100%;
  height: 76vh;

  @include scrollbars(5px);
}

.btn {
  cursor: pointer;
}

.paginatedFont {
  font-size: 1em;
}

.tableHeader {
  border-radius: 30px;
}

.tableTh {
  font-size: 0.8em;
  color: #ffffff;
}

.btnTable {
  cursor: pointer;
  outline: 0;
  border: 0;
}

.tableItems {
  font-size: 0.9em;

  .client {
    font-weight: bold !important;
    color: var(--risk-primary);
  }
  .header {
    //background-color: black !important;
    font-weight: bold !important;
  }
  .footer {
    color: rgba(0, 0, 0, 0.5);
  }
}

.tableOptionsFloat {
  background-color: rgba(248, 248, 248, 1);
  box-shadow: 0px -2px 5px rgba(0, 0, 0, 0.07);
}

.profilestyle {
  height: 35px;
  width: 35px;
  border-radius: 100%;
}
.assigned {
  @extend .profilestyle;
  color: rgba(99, 121, 104, 1);
  border: 1px solid rgba(99, 121, 104, 1);
  @include font(0.9em, 600);
}

.profile {
  @extend .profilestyle;
  background-color: rgba(99, 121, 104, 1);
}

.status {
  @include font(0.9em, 600);
}

.active {
  @extend .status;
  color: var(--risk-primary);
}
.paid {
  @extend .status;
  color: var(--risk-primary);
}
.on-hold {
  @extend .status;
  color: #d59c00;
}
.dead {
  @extend .status;
  color: #910000;
}
.archive {
  @extend .status;
  color: #e8e8e8;
}

.closed {
  @extend .status;
  color: #595959;
}
