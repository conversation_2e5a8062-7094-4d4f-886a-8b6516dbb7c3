import React from "react";

import styles from "./index.module.css";

interface IProperyTitleProps {
  mortgageLender: string;
  propertyName: string;
}

const PropertyTitle = ({
  mortgageLender,
  propertyName,
}: IProperyTitleProps) => {
  return (
    <>
      <section className="d-flex mt-4">
        <div className={styles.flex1}></div>
        <h4 className={`${styles.flex2} text-lg-left text-xl-left`}>
          {mortgageLender} - {propertyName}
        </h4>
      </section>
    </>
  );
};

export default PropertyTitle;
