/**
 * Test script for JSON-based CRUD operations
 * Run this to verify that the new JSON schema and operations work correctly
 */

const { DatabaseManager } = require('./database');
const { ModelFactory } = require('./models');

async function testJsonCrud() {
  console.log('🧪 Starting JSON-based CRUD tests...\n');

  const dbManager = new DatabaseManager();
  
  try {
    // Initialize database
    await dbManager.initialize(true); // Development mode
    const db = dbManager.getDatabase();
    const models = new ModelFactory(db);

    console.log('✅ Database initialized successfully\n');

    // Test 1: Form Fields CRUD
    console.log('📝 Testing Form Fields CRUD...');
    await testFormFields(models.formFields);

    // Test 2: Deals with JSON CRUD
    console.log('\n🏢 Testing Deals with JSON CRUD...');
    await testDealsJson(models.deals);

    // Test 3: Tracker Data operations
    console.log('\n📊 Testing Tracker Data operations...');
    await testTrackerData(models.trackerData, models.deals);

    // Test 4: Complex field processing
    console.log('\n⚙️ Testing Complex Field Processing...');
    await testComplexFieldProcessing(models.trackerData, models.formFields);

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    dbManager.close();
  }
}

async function testFormFields(formFieldsModel) {
  // Test creating a custom form field
  const customField = await formFieldsModel.create({
    name: 'test_custom_field',
    label: 'Test Custom Field',
    type: 'text',
    form_section: 'testing',
    display_order: 1,
    required: true
  });

  console.log('  ✅ Created custom form field:', customField.name);

  // Test getting fields by section
  const orderSummaryFields = formFieldsModel.getFieldsBySection('orderSummary');
  console.log(`  ✅ Found ${orderSummaryFields.length} order summary fields`);

  // Test getting all active fields
  const allFields = formFieldsModel.getAllActiveFieldsBySection();
  console.log(`  ✅ Found ${Object.keys(allFields).length} form sections`);

  // Test updating field
  const updatedField = formFieldsModel.update(customField.id, {
    label: 'Updated Test Field'
  });
  console.log('  ✅ Updated form field label');

  // Test field validation
  try {
    formFieldsModel.validateFieldData({
      name: '',
      label: 'Invalid Field'
    });
    console.log('  ❌ Should have failed validation');
  } catch (error) {
    console.log('  ✅ Field validation working correctly');
  }
}

async function testDealsJson(dealsModel) {
  // Test creating deal with JSON structure
  const dealData = {
    name: 'Test JSON Deal',
    deal: {
      order_id: 'TEST001',
      name: 'Test JSON Deal',
      status: 'Active',
      loan_closing_date: '2024-12-31',
      property_name: 'Test Property',
      loan_amount: '1000000'
    },
    tracker_data: {
      property_name: 'Test Property',
      loan_number: 'LN001',
      status: 'In Progress'
    }
  };

  const createdDeal = dealsModel.createWithJson(dealData);
  console.log('  ✅ Created deal with JSON structure:', createdDeal.id);

  // Test finding by order ID
  const foundDeal = dealsModel.findByOrderId('TEST001');
  console.log('  ✅ Found deal by order ID:', foundDeal ? foundDeal.id : 'Not found');

  // Test updating deal JSON
  const updatedDealJson = {
    ...foundDeal.dealData,
    status: 'On-hold',
    updated_field: 'New value'
  };

  const updatedDeal = dealsModel.updateDealJson(foundDeal.id, updatedDealJson);
  console.log('  ✅ Updated deal JSON data');

  // Test updating tracker data
  const newTrackerData = {
    loan_execution: '2024-01-15',
    sponsor: 'Test Sponsor',
    completion_percentage: 75
  };

  const dealWithUpdatedTracker = dealsModel.updateTrackerData(foundDeal.id, newTrackerData);
  console.log('  ✅ Updated tracker data');

  // Test merging tracker data
  const additionalTrackerData = {
    notes: 'Additional notes',
    last_updated: new Date().toISOString()
  };

  const dealWithMergedTracker = dealsModel.mergeTrackerData(foundDeal.id, additionalTrackerData);
  console.log('  ✅ Merged tracker data');

  // Test JSON search
  const searchResults = dealsModel.searchDealsJson({
    searchTerm: 'Test',
    status: 'On-hold',
    jsonFields: {
      property_name: 'Test Property'
    }
  });
  console.log(`  ✅ JSON search found ${searchResults.length} deals`);

  // Test parsing JSON fields
  const dealWithParsedJson = dealsModel.findById(foundDeal.id);
  console.log('  ✅ Deal JSON parsing:', {
    hasDealData: !!dealWithParsedJson.dealData,
    hasTrackerData: !!dealWithParsedJson.trackerData
  });
}

async function testTrackerData(trackerDataModel, dealsModel) {
  // Get a deal to work with
  const deals = dealsModel.findAll({ limit: 1 });
  if (deals.length === 0) {
    console.log('  ⚠️ No deals found for tracker data testing');
    return;
  }

  const dealId = deals[0].id;

  // Test getting tracker data
  const trackerData = trackerDataModel.getTrackerData(dealId);
  console.log('  ✅ Retrieved tracker data');

  // Test updating specific field
  const updatedDeal = trackerDataModel.updateTrackerField(dealId, 'test_field', 'test_value');
  console.log('  ✅ Updated specific tracker field');

  // Test getting specific field
  const fieldValue = trackerDataModel.getTrackerField(dealId, 'test_field');
  console.log('  ✅ Retrieved specific field value:', fieldValue);

  // Test tracker data validation
  const testTrackerData = {
    loan_closing_date: '2024-12-31',
    status: 'Active',
    invalid_field: 'should cause warning'
  };

  const validationResult = trackerDataModel.validateTrackerData(testTrackerData, 'orderSummary');
  console.log('  ✅ Tracker data validation:', {
    isValid: validationResult.isValid,
    errorCount: validationResult.errors.length,
    warningCount: validationResult.warnings.length
  });

  // Test tracker summary
  const summary = trackerDataModel.getTrackerSummary(dealId);
  console.log('  ✅ Tracker summary:', {
    completionPercentage: summary.completionPercentage,
    totalFields: summary.totalFields,
    completedFields: summary.completedFields
  });
}

async function testComplexFieldProcessing(trackerDataModel, formFieldsModel) {
  // Test field processing with different types
  const testData = {
    loan_amount: '1,000,000.50', // Should be converted to number
    loan_closing_date: '2024-12-31T00:00:00Z', // Should be normalized to date
    requested_items: 'Flood Zone Determination', // Should be converted to array for multiSelect
    notes: '  Some notes with whitespace  ' // Should be trimmed
  };

  const processedData = trackerDataModel.processTrackerData(testData);
  console.log('  ✅ Field processing completed');

  // Test custom validation rules
  const fieldWithRules = await formFieldsModel.create({
    name: 'test_validation_field',
    label: 'Test Validation Field',
    type: 'text',
    form_section: 'testing',
    validation_rules: JSON.stringify({
      minLength: 5,
      maxLength: 50,
      pattern: '^[A-Za-z0-9]+$'
    })
  });

  // Test validation with custom rules
  const validationTests = [
    { value: 'abc', shouldFail: true, reason: 'too short' },
    { value: 'ValidValue123', shouldFail: false, reason: 'valid' },
    { value: 'Invalid Value!', shouldFail: true, reason: 'invalid characters' }
  ];

  for (const test of validationTests) {
    const errors = trackerDataModel.validateField(fieldWithRules, test.value);
    const hasFailed = errors.length > 0;
    const result = hasFailed === test.shouldFail ? '✅' : '❌';
    console.log(`  ${result} Validation test (${test.reason}): expected ${test.shouldFail ? 'fail' : 'pass'}, got ${hasFailed ? 'fail' : 'pass'}`);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testJsonCrud().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { testJsonCrud };
