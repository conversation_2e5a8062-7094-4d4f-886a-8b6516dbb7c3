import React from "react";

import { AddIcon } from "../../../../exports";

import styles from "./index.module.scss";
import { setLastLocation } from "./../../../../utils/setLastLocation";
import CustomModal from "../../../resuable/modal";
import { useGet, usePostOrder } from "../../../../apis";
import { Formik } from "formik";
import { Form } from "formik";
import MapTrackerForm from "../../../deal-tracker/map-tracker";
import TrackerFormWrapper from "../../../resuable/tracker-form-wrapper";
import { extractNameValue } from "../../../../utils/groupBy";

export default function CreateReview() {
  const { data, isLoading, isError } = useGet(`/order/new/fields`);

  const [show, setShow] = React.useState(false);

  const { mutate, isLoading: isCreating } = usePostOrder("/order", "new");

  const handleSubmit = (values: any, { resetForm }) => {
    mutate(values, {
      onSuccess: () => {
        resetForm();
      },
    });
  };

  return (
    <>
      <CustomModal
        {...{
          title: "Add new deal",
          show,
          setShow,
          columnLayout: "col-12 col-md-9 col-lg-8 col-xl-6",
          align: "align-items-start pt-5",
        }}
      >
        <TrackerFormWrapper>
          <section className={`${styles.wrapper} px-2 py-4`}>
            {isLoading ? (
              <section className="d-flex justify-content-center align-items-center my-5">
                <div className="spinner-grow spinner-grow-sm text-success"></div>
              </section>
            ) : isError ? (
              <section className="d-flex justify-content-center align-items-center my-5">
                <div className="text-danger text-center">
                  something went wrong...
                </div>
              </section>
            ) : data?.fields?.length <= 0 ? (
              <div>Fields not available. Try again later.</div>
            ) : (
              <Formik
                enableReinitialize={true}
                initialValues={extractNameValue(data?.fields || [])}
                onSubmit={handleSubmit}
              >
                {(formik) => {
                  return (
                    <Form>
                      <MapTrackerForm
                        {...{
                          data: data?.fields,
                          formik,
                        }}
                      />

                      <div className="text-center mt-5">
                        <button
                          className={`${styles.buttonBorder} py-2`}
                          onClick={() => setShow(false)}
                          type="button"
                        >
                          Cancel
                        </button>

                        <button
                          type="submit"
                          className={`${styles.button} ml-2 py-2`}
                          disabled={isCreating}
                        >
                          {isCreating ? "Adding Deal..." : "Save"}
                        </button>
                      </div>
                    </Form>
                  );
                }}
              </Formik>
            )}
          </section>
        </TrackerFormWrapper>
      </CustomModal>

      <div
        onClick={() => {
          setShow(true);
          setLastLocation("Order Summary");
        }}
        className={`${styles.createReview} d-flex align-items-center`}
      >
        <aside className="mx-2">
          <img src={AddIcon} className="img-fluid" alt="add icon" />
        </aside>
        <aside>
          <span className={`${styles.reviewOrder} text-white`}>Deals</span>
        </aside>
      </div>
    </>
  );
}
