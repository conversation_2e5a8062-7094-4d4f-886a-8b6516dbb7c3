.container {
  border: 1px solid #f1f3f2;
  border-radius: 0px;
}

/*.search {
  background-color: #f1f3f2;
  border-radius: 18px;
}*/

.input {
  width: 100%;
  border: 0;
  outline: 0;
}

.input:focus {
  outline: 0 !important;
  box-shadow: none !important;
  outline-style: none !important;
  border-color: transparent !important;
}
input::placeholder {
  color: #bebebe;
}

.cancelIcon {
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    opacity: 0.5;
  }
}
