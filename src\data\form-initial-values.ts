export const initInitialValues = (data: any) => {
  const initialValues = {
    //order summary
    loan_closing_date: data?.order?.order_summary?.loan_closing_date || "",
    reviewed_needed_date:
      data?.order?.order_summary?.reviewed_needed_date || "",
    requested_items: data?.order?.order_summary?.requested_items || "",
    status: data?.order?.order_summary?.status || "",
    revenue: data?.order?.order_summary?.revenue || "",
    is_portfolio: data?.order?.order_summary?.is_portfolio || "",
    portfolio_name: data?.order?.order_summary?.portfolio_name || "",
    // portfolio_id: data?.order_summary?.portfolio_id || "",
    portfolio_property_count:
      data?.order?.order_summary?.portfolio_property_count || "",
    loan_per_property: data?.order?.order_summary?.loan_per_property || "",
    is_portfolio_review_one_report:
      data?.order?.order_summary?.is_portfolio_review_one_report || "",
    //loan summary

    loan_execution: data?.order?.loan_summary?.loan_execution || "",
    loan_type: data?.order?.loan_summary?.loan_type || "",
    loan_number: data?.order?.loan_summary?.loan_number || "",
    mortgage_lender: data?.order?.loan_summary?.mortgage_lender || "",
    mortgage_loan_amount:
      data?.order?.loan_summary?.mortgage_loan_amount || "",
    has_upb_on_existing_lien:
      data?.order?.loan_summary?.has_upb_on_existing_lien || "",
    upb_amount: data?.order?.loan_summary?.upb_amount || "",
    has_mezz_lender: data?.order?.loan_summary?.has_mezz_lender || "",
    mezz_lender: data?.order?.loan_summary?.mezz_lender || "",
    mezz_loan_amount: data?.order?.loan_summary?.mezz_loan_amount || "",
    has_repo_whse_lender:
      data?.order?.loan_summary?.has_repo_whse_lender || "",
    repo_whse_lender: data?.order?.loan_summary?.repo_whse_lender || "",
    is_repo_reps_provided:
      data?.order?.loan_summary?.is_repo_reps_provided || "",
    repo_reps_upload_url:
      data?.order?.loan_summary?.repo_reps_upload_url || "",
    borrower_sponsor_name:
      data?.order?.loan_summary?.borrower_sponsor_name || "",
    is_repeat_borrower_sponsor:
      data?.order?.loan_summary?.is_repeat_borrower_sponsor || "",
    recently_reviewed_deal:
      data?.order?.loan_summary?.recently_reviewed_deal || "",
    borrowing_entity_name:
      data?.order?.loan_summary?.borrowing_entity_name || "",
    is_load_agreement_attached:
      data?.order?.loan_summary?.is_load_agreement_attached || "",
    loan_agreement_upload_url:
      data?.order?.loan_summary?.loan_agreement_upload_url || "",

    //property summary

    property_name: data?.order?.property_summary?.property_name || "",
    property_street: data?.order?.property_summary?.property_street || "",
    property_city: data?.order?.property_summary?.property_city || "",
    property_state: data?.order?.property_summary?.property_state || "",
    property_zipcode: data?.order?.property_summary?.property_zipcode || "",
    property_country: data?.order?.property_summary?.property_country || "",
    property_type: data?.order?.property_summary?.property_type || "",
    is_coop: data?.order?.property_summary?.is_coop || "",
    insurable_value: data?.order?.property_summary?.insurable_value || "",
    land_value: data?.order?.property_summary?.land_value || "",
    uw_annual_egi: data?.order?.property_summary?.uw_annual_egi || "",
    year_built: data?.order?.property_summary?.year_built || "",
    age_of_roofs: data?.order?.property_summary?.age_of_roofs || "",
    number_of_buildings:
      data?.order?.property_summary?.number_of_buildings || "",
    number_of_stories: data?.order?.property_summary?.number_of_stories || "",
    number_of_units: data?.order?.property_summary?.number_of_units || "",
    number_of_licensed_beds:
      data?.order?.property_summary?.number_of_licensed_beds || "",
    problematic_bldg_materials:
      data?.order?.property_summary?.problematic_bldg_materials || "",
    has_elevators: data?.order?.property_summary?.has_elevators || "",
    has_boilers: data?.order?.property_summary?.has_boilers || "",
    has_high_pressure: data?.order?.property_summary?.has_high_pressure || "",
    is_centralized: data?.order?.property_summary?.is_centralized || "",
    is_state_regulated:
      data?.order?.property_summary?.is_state_regulated || "",
    flood_zone: data?.order?.property_summary?.flood_zone || "",
    flood_zone_specify:
      data?.order?.property_summary?.flood_zone_specify || "",

    seismic_pml_sel: data?.order?.property_summary?.seismic_pml_sel || "",
    localized_regional_perils:
      data?.order?.property_summary?.localized_regional_perils || "",
    zoning_status: data?.order?.property_summary?.zoning_status || "",
    be_has_emp_at_property:
      data?.order?.property_summary?.be_has_emp_at_property || "",
    be_operates_auto_at_property:
      data?.order?.property_summary?.be_operates_auto_at_property || "",
    is_env_insurance_req:
      data?.order?.property_summary?.is_env_insurance_req || "",
    any_constr_at_property:
      data?.order?.property_summary?.any_constr_at_property || "",
    is_property_part_of_condo:
      data?.order?.property_summary?.is_property_part_of_condo || "",
    is_borrower_tenant_under_lease:
      data?.order?.property_summary?.is_borrower_tenant_under_lease || "",
    is_tenant_req_to_insure_collateral:
      data?.order?.property_summary?.is_tenant_req_to_insure_collateral || "",

    //due diligence
    is_appraisal_attached:
      data?.order?.due_diligence?.is_appraisal_attached || "",
    appraisal_upload_url:
      data?.order?.due_diligence?.appraisal_upload_url || "",
    // is_esa_attached: data?.due_diligence?.is_esa_attached || "",
    // esa_upload_url: data?.due_diligence?.esa_upload_url || "",
    is_fzd_attached: data?.order?.due_diligence?.is_fzd_attached || "",
    fzd_upload_url: data?.order?.due_diligence?.fzd_upload_url || "",
    is_pca_attached: data?.order?.due_diligence?.is_pca_attached || "",
    pca_upload_url: data?.order?.due_diligence?.pca_upload_url || "",
    is_sra_attached: data?.order?.due_diligence?.is_sra_attached || "",
    sra_upload_url: data?.order?.due_diligence?.sra_upload_url || "",
    is_pzr_attached: data?.order?.due_diligence?.is_pzr_attached || "",
    pzr_upload_url: data?.order?.due_diligence?.pzr_upload_url || "",

    //contacts
    lender_cc_name: data?.order?.contacts?.lender_cc_name || "",
    lender_cc_email: data?.order?.contacts?.lender_cc_email || "",
    lender_cc_phone: data?.order?.contacts?.lender_cc_phone || "",
    lender_uw_name: data?.order?.contacts?.lender_uw_name || "",
    lender_uw_email: data?.order?.contacts?.lender_uw_email || "",
    lender_uw_phone: data?.order?.contacts?.lender_uw_phone || "",
    counsel_contact_name: data?.order?.contacts?.counsel_contact_name || "",
    counsel_contact_email: data?.order?.contacts?.counsel_contact_email || "",
    counsel_contact_phone: data?.order?.contacts?.counsel_contact_phone || "",
    borrower_contact_name: data?.order?.contacts?.borrower_contact_name || "",
    borrower_contact_email:
      data?.order?.contacts?.borrower_contact_email || "",
    borrower_contact_phone:
      data?.order?.contacts?.borrower_contact_phone || "",
    borrower_ic_name: data?.order?.contacts?.borrower_ic_name || "",
    borrower_ic_email: data?.order?.contacts?.borrower_ic_email || "",
    borrower_ic_phone: data?.order?.contacts?.borrower_ic_phone || "",

    //condo
    does_borr_entity_control_board:
      data?.order?.suppl_condo?.does_borr_entity_control_board || "",
    is_site_plan_attached:
      data?.order?.suppl_condo?.is_site_plan_attached || "",
    site_plan_upload_url:
      data?.order?.suppl_condo?.site_plan_upload_url || "",
    is_condo_bylaws_attached:
      data?.order?.suppl_condo?.is_condo_bylaws_attached || "",
    condo_byloaws_upload_url:
      data?.order?.suppl_condo?.condo_byloaws_upload_ur || "",
    total_condo_units: data?.order?.suppl_condo?.total_condo_units || "",
    structure_sqft: data?.order?.suppl_condo?.structure_sqft || "",
    structure_insurable_val:
      data?.order?.suppl_condo?.structure_insurable_val || "",
    total_borr_owned_units:
      data?.order?.suppl_condo?.total_borr_owned_units || "",
    borr_owned_units_sqft:
      data?.order?.suppl_condo?.borr_owned_units_sqft || "",
    borr_owned_units_insurable_val:
      data?.order?.suppl_condo?.borr_owned_units_insurable_val || "",
    condo_ac_name: data?.order?.suppl_condo?.condo_ac_name || "",
    condo_ac_email: data?.order?.suppl_condo?.condo_ac_email || "",
    condo_ac_phone: data?.order?.suppl_condo?.condo_ac_phone || "",
    condo_aic_name: data?.order?.suppl_condo?.condo_aic_name || "",
    condo_aic_email: data?.order?.suppl_condo?.condo_aic_email || "",
    condo_aic_phone: data?.order?.suppl_condo?.condo_aic_phone || "",

    //construction

    is_proj_schedule_attached:
      data?.order?.suppl_construction?.is_proj_schedule_attached || "",
    proj_schedule_upload_url:
      data?.order?.suppl_construction?.proj_schedule_upload_url || "",
    is_gmp_contract_attached:
      data?.order?.suppl_construction?.is_gmp_contract_attached || "",
    gmp_contract_upload_url:
      data?.order?.suppl_construction?.gmp_contract_upload_url || "",
    is_hsc_breakdown_attached:
      data?.order?.suppl_construction?.is_hsc_breakdown_attached || "",
    hsc_upload_url: data?.order?.suppl_construction?.hsc_upload_url || "",
    has_demo_abatement:
      data?.order?.suppl_construction?.has_demo_abatement || "",
    existing_structures_insurable_val:
      data?.order?.suppl_construction?.existing_structures_insurable_val ||
      "",
    est_annual_inc_on_completion:
      data?.order?.suppl_construction?.est_annual_inc_on_completion || "",
    gc_contact_name: data?.order?.suppl_construction?.gc_contact_name || "",
    gc_contact_email: data?.order?.suppl_construction?.gc_contact_email || "",
    gc_contact_phone: data?.order?.suppl_construction?.gc_contact_phone || "",
    gci_contact_name: data?.order?.suppl_construction?.gci_contact_name || "",
    gci_contact_email:
      data?.order?.suppl_construction?.gci_contact_email || "",
    gci_contact_phone:
      data?.order?.suppl_construction?.gci_contact_phone || "",
    architect_contact_name:
      data?.order?.suppl_construction?.architect_contact_name || "",
    architect_contact_email:
      data?.order?.suppl_construction?.architect_contact_email || "",
    architect_contact_phone:
      data?.order?.suppl_construction?.architect_contact_phone || "",
    architect_ic_name:
      data?.order?.suppl_construction?.architect_ic_name || "",
    architect_ic_email:
      data?.order?.suppl_construction?.architect_ic_email || "",
    architect_ic_phone:
      data?.order?.suppl_construction?.architect_ic_phone || "",

    //environmental
    is_esa_attached: data?.order?.suppl_environmental?.is_esa_attached || "",
    esa_upload_url: data?.order?.suppl_environmental?.esa_upload_url || "",
    loan_term_incl_ext_option:
      data?.order?.suppl_environmental?.loan_term_incl_ext_option || "",
    worst_case_cleanup_cost_est:
      data?.order?.suppl_environmental?.worst_case_cleanup_cost_est || "",
    is_coverage_req_in_lieu:
      data?.order?.suppl_environmental?.is_coverage_req_in_lieu || "",
    is_sponsor_env_ind:
      data?.order?.suppl_environmental?.is_sponsor_env_ind || "",
    is_add_security_req:
      data?.order?.suppl_environmental?.is_add_security_req || "",
    add_resaon_for_coverage:
      data?.order?.suppl_environmental?.add_resaon_for_coverage || "",

    //tenant

    is_borr_lease_attached:
      data?.order?.suppl_tenant?.is_borr_lease_attached || "",
    upload_borr_lease_url:
      data?.order?.suppl_tenant?.upload_borr_lease_url || "",
    type_of_lease: data?.order?.suppl_tenant?.type_of_lease || "",
    is_tenant_lease_attached:
      data?.order?.suppl_tenant?.is_tenant_lease_attached || "",
    tenant_lease_upload_url:
      data?.order?.suppl_tenant?.tenant_lease_upload_url || "",
    tenant_occ_insurable_val:
      data?.order?.suppl_tenant?.tenant_occ_insurable_val || "",

    tenant_occ_address: data?.order?.suppl_tenant?.tenant_occ_address || "",
    tenant_occ_city: data?.order?.suppl_tenant?.tenant_occ_city || "",
    tenant_occ_state: data?.order?.suppl_tenant?.tenant_occ_state || "",
    tenant_occ_zipcode: data?.order?.suppl_tenant?.tenant_occ_zipcode || "",
    tenant_occ_country: data?.order?.suppl_tenant?.tenant_occ_country || "",

    annual_inc_from_tenant:
      data?.order?.suppl_tenant?.annual_inc_from_tenant || "",
    tenant_contact_name: data?.order?.suppl_tenant?.tenant_contact_name || "",
    tenant_contact_email:
      data?.order?.suppl_tenant?.tenant_contact_email || "",
    tenant_contact_phone:
      data?.order?.suppl_tenant?.tenant_contact_phone || "",
    tenant_ic_name: data?.order?.suppl_tenant?.tenant_ic_name || "",
    tenant_ic_email: data?.order?.suppl_tenant?.tenant_ic_email || "",
    tenant_ic_phone: data?.order?.suppl_tenant?.tenant_ic_phone || "",
  };

  return initialValues;
};
