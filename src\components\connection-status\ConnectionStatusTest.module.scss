@import "../../../styles/_mixins.scss";

.testToggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;

  .toggleButton {
    padding: 8px 16px;
    background-color: var(--risk-primary);
    color: white;
    border: none;
    border-radius: 6px;
    @include font(0.8em, 500);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--risk-primary-light);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
  }
}

.testContainer {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: white;
  border: 1px solid rgba(46, 113, 111, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow-y: auto;
  font-family: inherit;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(46, 113, 111, 0.1);
    background: linear-gradient(135deg, rgba(46, 113, 111, 0.05), rgba(46, 113, 111, 0.1));

    h3 {
      margin: 0;
      @include font(1.1em, 600);
      color: var(--risk-primary);
    }

    .closeButton {
      background: none;
      border: none;
      @include font(1.5em, 400);
      color: rgba(24, 24, 24, 0.5);
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(24, 24, 24, 0.1);
        color: rgba(24, 24, 24, 0.8);
      }
    }
  }

  .controls {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(46, 113, 111, 0.1);
    display: flex;
    flex-direction: column;
    gap: 8px;

    .controlButton {
      padding: 8px 12px;
      border: 1px solid rgba(46, 113, 111, 0.3);
      border-radius: 6px;
      background: white;
      @include font(0.8em, 500);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(46, 113, 111, 0.05);
        border-color: rgba(46, 113, 111, 0.5);
      }

      &.online {
        background-color: rgba(39, 174, 96, 0.1);
        border-color: var(--success);
        color: var(--success);
      }

      &.offline {
        background-color: rgba(235, 172, 11, 0.1);
        border-color: var(--warning);
        color: var(--warning);
      }
    }
  }

  .section {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(46, 113, 111, 0.1);

    &:last-child {
      border-bottom: none;
    }

    h4 {
      margin: 0 0 12px 0;
      @include font(0.9em, 600);
      color: rgba(24, 24, 24, 0.8);
    }
  }

  .indicatorGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    .indicatorItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px;
      background-color: rgba(241, 243, 242, 0.5);
      border-radius: 6px;
      border: 1px solid rgba(46, 113, 111, 0.1);

      span {
        @include font(0.75em, 500);
        color: rgba(24, 24, 24, 0.7);
      }
    }
  }

  .electronStatusGrid {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .electronStatusItem {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 12px;
      background-color: rgba(241, 243, 242, 0.5);
      border-radius: 6px;
      border: 1px solid rgba(46, 113, 111, 0.1);

      span {
        @include font(0.75em, 600);
        color: rgba(24, 24, 24, 0.7);
      }
    }
  }

  .positionedContainer {
    .positionedBox {
      position: relative;
      width: 100%;
      height: 80px;
      background-color: rgba(241, 243, 242, 0.3);
      border: 2px dashed rgba(46, 113, 111, 0.3);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      @include font(0.8em, 500);
      color: rgba(24, 24, 24, 0.6);
    }
  }

  .footer {
    padding: 16px 20px;
    background-color: rgba(241, 243, 242, 0.3);
    border-radius: 0 0 12px 12px;

    p {
      margin: 0;
      @include font(0.75em, 400);
      color: rgba(24, 24, 24, 0.6);
      line-height: 1.4;

      strong {
        color: rgba(24, 24, 24, 0.8);
      }
    }
  }
}

// Responsive adjustments
@media screen and (max-width: 768px) {
  .testContainer {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
    max-height: 70vh;
  }

  .indicatorGrid {
    grid-template-columns: 1fr;
  }
}

@media screen and (max-width: 480px) {
  .testToggle {
    bottom: 10px;
    right: 10px;

    .toggleButton {
      padding: 6px 12px;
      @include font(0.75em, 500);
    }
  }

  .testContainer {
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    max-height: 80vh;

    .header {
      padding: 12px 16px;

      h3 {
        @include font(1em, 600);
      }
    }

    .controls,
    .section {
      padding: 12px 16px;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .testContainer {
    background: rgba(24, 24, 24, 0.95);
    border-color: rgba(255, 255, 255, 0.2);

    .header {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.1));
      border-bottom-color: rgba(255, 255, 255, 0.1);

      h3 {
        color: rgba(255, 255, 255, 0.9);
      }

      .closeButton {
        color: rgba(255, 255, 255, 0.5);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    .controls {
      border-bottom-color: rgba(255, 255, 255, 0.1);

      .controlButton {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }
    }

    .section {
      border-bottom-color: rgba(255, 255, 255, 0.1);

      h4 {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .indicatorGrid .indicatorItem,
    .electronStatusGrid .electronStatusItem {
      background-color: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);

      span {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    .positionedContainer .positionedBox {
      background-color: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.3);
      color: rgba(255, 255, 255, 0.6);
    }

    .footer {
      background-color: rgba(255, 255, 255, 0.05);

      p {
        color: rgba(255, 255, 255, 0.6);

        strong {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}
