import React from "react";
import { EsectionHeaderTitles } from "../../../data/sectionHeader";
import { dynamicFormData } from "./../../../data/dynamic-form";
import ReusableSummary from "../resuable-summary";

interface IContactsProps {
  formik?: any;
  // setFieldValue: (arg1: string, arg2: string) => void;
}

const Contacts = React.forwardRef(({ formik }: IContactsProps, ref: any) => {
  return (
    <section
      ref={ref}
      className="container-fluid px-0 px-md-0 px-lg-0 px-xl-5 pb-3 pb-md-3 pb-lg-3 pb-xl-5"
    >
      <ReusableSummary
        {...{
          data: dynamicFormData.contactsSummary,
          title: EsectionHeaderTitles.contacts,
          formik: formik,
        }}
      />
    </section>
  );
});

export default Contacts;
