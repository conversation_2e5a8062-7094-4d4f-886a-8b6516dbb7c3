---
description: Guide for using Task Master with <PERSON> to manage task-driven development workflows
globs: **/*
alwaysApply: true
---
# Task Master Development Workflow for <PERSON>

This guide outlines the optimal process for using Task Master with <PERSON>'s advanced capabilities to manage software development projects.

## Primary Interaction: MCP Server Integration

Task Master offers seamless integration with <PERSON> through the MCP server:

1.  **MCP Server (Primary Method for Claude)**:
    - <PERSON> interacts via the **MCP server as the preferred method** for optimal performance
    - The MCP server exposes Task Master functionality through structured tools
    - <PERSON> can leverage superior error handling and data exchange
    - Refer to [`mcp.mdc`](mdc:.claude/rules/mcp.mdc) for MCP architecture details
    - Comprehensive tool reference available in [`taskmaster.mdc`](mdc:.claude/rules/taskmaster.mdc)
    - **Restart the MCP server** if core logic in `scripts/modules` changes

2.  **Desktop Commander Integration**:
    - <PERSON> can use Desktop Commander for file operations and command execution
    - Provides direct access to project files and system operations
    - Enables sophisticated code analysis and file manipulation
    - Superior to traditional CLI parsing for complex operations

## Claude-Enhanced Development Workflow Process

-   **Project Initialization**: Use `initialize_project` tool or analyze existing project structure
-   **Intelligent Task Analysis**: Leverage `get_tasks` with <PERSON>'s superior reasoning
-   **Context-Aware Task Selection**: Use `next_task` with comprehensive project understanding
-   **Advanced Complexity Analysis**: Run `analyze_project_complexity` with research capabilities
-   **Multi-File Code Analysis**: Use Desktop Commander to understand codebase structure

-   **Smart Task Breakdown**: Use `expand_task` with Claude's enhanced reasoning
-   **Comprehensive Implementation**: Follow task details with full codebase awareness
-   **Advanced Testing**: Verify tasks using Claude's understanding of test strategies
-   **Intelligent Status Updates**: Use `set_task_status` with context awareness
-   **Adaptive Task Updates**: Leverage `update` and `update_task` with research capabilities
-   **Dynamic Task Creation**: Use `add_task` with Claude's project understanding
-   **Iterative Subtask Management**: Employ `add_subtask` and `update_subtask` effectively
-   **Automated File Generation**: Use `generate` with Desktop Commander integration
-   **Dependency Management**: Maintain valid structures with validation tools
-   **Progress Reporting**: Provide detailed progress analysis using Claude's capabilities

## Claude-Specific Task Analysis Capabilities

-   **Multi-File Context**: Analyze task requirements across entire codebase
-   **Research Integration**: Use web search for latest best practices and solutions
-   **Pattern Recognition**: Identify code patterns and architectural decisions
-   **Dependency Analysis**: Understand complex inter-task relationships
-   **Risk Assessment**: Evaluate implementation challenges and alternatives

## Enhanced Task Complexity Analysis

-   Run `analyze_project_complexity` with research flag for comprehensive analysis
-   Use Claude's reasoning to interpret complexity scores beyond simple numbers
-   Leverage web search for industry-standard complexity benchmarks
-   Cross-reference with similar projects and best practices
-   Provide detailed recommendations based on comprehensive analysis

## Advanced Task Breakdown Process

-   Use `expand_task` with Claude's superior understanding of software architecture
-   Apply research capabilities for informed subtask generation
-   Consider cross-cutting concerns and architectural patterns
-   Factor in testing, documentation, and deployment requirements
-   Provide implementation guidance based on industry best practices

## Implementation Drift Handling with Claude

-   **Intelligent Change Detection**: Use Claude's ability to understand implementation impact
-   **Contextual Updates**: Apply `update` with comprehensive project knowledge
-   **Architecture-Aware Modifications**: Consider system-wide implications
-   **Research-Backed Decisions**: Use web search for validating architectural choices
-   **Proactive Risk Mitigation**: Identify potential issues before they occur

## Enhanced Task Status Management

-   Use status transitions that reflect Claude's understanding of task complexity
-   Provide detailed status reasoning and next steps
-   Consider dependencies and blocking factors intelligently
-   Track progress with comprehensive context awareness

## Claude-Optimized Iterative Subtask Implementation

1.  **Deep Understanding Phase**:
    *   Use `get_task` combined with codebase analysis via Desktop Commander
    *   Understand not just the task but its architectural context
    *   Research best practices and potential pitfalls

2.  **Comprehensive Planning Phase**:
    *   Analyze entire codebase for relevant patterns and structures
    *   Consider cross-cutting concerns and system architecture
    *   Plan implementation with full context awareness
    *   Document findings using `update_subtask` with detailed insights

3.  **Intelligent Implementation**:
    *   Set status using `set_task_status` with reasoning
    *   Implement with full system understanding
    *   Consider testing, error handling, and edge cases
    *   Use Desktop Commander for sophisticated file operations

4.  **Continuous Learning and Adaptation**:
    *   Use `update_subtask` to log implementation learnings
    *   Update rules based on emerging patterns
    *   Share insights across similar tasks
    *   Build institutional knowledge within the project
