/**
 * Mock database implementation for development/testing
 * This provides the same interface as the real database but stores data in memory
 * Use this when better-sqlite3 compilation issues occur
 */

const fs = require('fs');
const path = require('path');
const { app } = require('electron');

class MockDatabase {
  constructor() {
    this.data = {
      users: [],
      deals: [],
      reports: [],
      consultants: [],
      operation_queue: [],
      sync_metadata: [],
      migrations: []
    };
    this.nextId = 1;
    this.isInitialized = false;
    this.dbPath = null;
  }

  /**
   * Initialize mock database
   */
  async initialize(isDev = false) {
    try {
      // Determine database path
      const userDataPath = app ? app.getPath('userData') : './data';
      const dbDir = path.join(userDataPath, 'database');

      // Ensure database directory exists
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      this.dbPath = path.join(dbDir, 'risk-app-mock.json');

      // Load existing data if available
      await this.loadData();

      // Run mock migrations
      await this.runMockMigrations();

      this.isInitialized = true;
      console.log(`✅ Mock Database initialized at: ${this.dbPath}`);

      return this;
    } catch (error) {
      console.error('❌ Mock Database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Load data from file
   */
  async loadData() {
    try {
      if (fs.existsSync(this.dbPath)) {
        const fileData = fs.readFileSync(this.dbPath, 'utf8');
        const loadedData = JSON.parse(fileData);
        this.data = { ...this.data, ...loadedData };

        // Find the highest ID to continue sequence
        let maxId = 0;
        Object.values(this.data).forEach(table => {
          if (Array.isArray(table)) {
            table.forEach(record => {
              if (record.id && record.id > maxId) {
                maxId = record.id;
              }
            });
          }
        });
        this.nextId = maxId + 1;
      }
    } catch (error) {
      console.warn('Could not load existing data:', error.message);
    }
  }

  /**
   * Save data to file
   */
  async saveData() {
    try {
      fs.writeFileSync(this.dbPath, JSON.stringify(this.data, null, 2));
    } catch (error) {
      console.error('Failed to save data:', error);
    }
  }

  /**
   * Run mock migrations
   */
  async runMockMigrations() {
    // Initialize sync metadata
    if (!this.data.sync_metadata.find(m => m.table_name === 'users')) {
      this.data.sync_metadata.push({
        table_name: 'users',
        last_sync_timestamp: null,
        total_records: 0,
        pending_operations: 0
      });
    }

    if (!this.data.sync_metadata.find(m => m.table_name === 'deals')) {
      this.data.sync_metadata.push({
        table_name: 'deals',
        last_sync_timestamp: null,
        total_records: 0,
        pending_operations: 0
      });
    }

    if (!this.data.sync_metadata.find(m => m.table_name === 'reports')) {
      this.data.sync_metadata.push({
        table_name: 'reports',
        last_sync_timestamp: null,
        total_records: 0,
        pending_operations: 0
      });
    }

    if (!this.data.sync_metadata.find(m => m.table_name === 'consultants')) {
      this.data.sync_metadata.push({
        table_name: 'consultants',
        last_sync_timestamp: null,
        total_records: 0,
        pending_operations: 0
      });
    }

    await this.saveData();
  }

  /**
   * Mock prepare method that returns a mock statement
   */
  prepare(sql) {
    return new MockStatement(this, sql);
  }

  /**
   * Mock exec method
   */
  exec(sql) {
    console.log('Mock exec:', sql);
    return { changes: 1 };
  }

  /**
   * Mock pragma method
   */
  pragma(pragma) {
    console.log('Mock pragma:', pragma);
    return true;
  }

  /**
   * Mock transaction method
   */
  transaction(callback) {
    return () => {
      try {
        const result = callback();
        this.saveData();
        return result;
      } catch (error) {
        console.error('Transaction failed:', error);
        throw error;
      }
    };
  }

  /**
   * Close mock database
   */
  close() {
    if (this.isInitialized) {
      this.saveData();
      this.isInitialized = false;
      console.log('✅ Mock Database connection closed');
    }
  }

  /**
   * Get database path
   */
  getDatabasePath() {
    return this.dbPath;
  }
}

class MockStatement {
  constructor(db, sql) {
    this.db = db;
    this.sql = sql;
  }

  /**
   * Mock run method for INSERT/UPDATE/DELETE
   */
  run(...params) {
    const sql = this.sql.toLowerCase();

    if (sql.includes('insert into users')) {
      return this.insertRecord('users', params);
    } else if (sql.includes('insert into deals')) {
      return this.insertRecord('deals', params);
    } else if (sql.includes('insert into reports')) {
      return this.insertRecord('reports', params);
    } else if (sql.includes('insert into consultants')) {
      return this.insertRecord('consultants', params);
    } else if (sql.includes('insert into operation_queue')) {
      return this.insertRecord('operation_queue', params);
    } else if (sql.includes('insert into migrations')) {
      return this.insertRecord('migrations', params);
    } else if (sql.includes('update')) {
      return this.updateRecord(params);
    } else if (sql.includes('delete')) {
      return this.deleteRecord(params);
    }

    console.log('Mock run - unhandled SQL:', sql);
    return { changes: 1, lastInsertRowid: this.db.nextId++ };
  }

  /**
   * Mock get method for single record
   */
  get(...params) {
    const sql = this.sql.toLowerCase();

    if (sql.includes('count(*)')) {
      const tableName = this.extractTableName();
      const count = this.db.data[tableName]?.length || 0;
      console.log(`Mock count for ${tableName}: ${count}`);
      return { count };
    } else if (sql.includes('select') && sql.includes('users')) {
      return this.getRecord('users', params);
    } else if (sql.includes('select') && sql.includes('deals')) {
      return this.getRecord('deals', params);
    } else if (sql.includes('select') && sql.includes('reports')) {
      return this.getRecord('reports', params);
    } else if (sql.includes('select') && sql.includes('consultants')) {
      return this.getRecord('consultants', params);
    } else if (sql.includes('max(version)')) {
      return { version: 3 }; // Mock migration version
    }

    console.log('Mock get - unhandled SQL:', sql);
    return null;
  }

  /**
   * Mock all method for multiple records
   */
  all(...params) {
    const sql = this.sql.toLowerCase();

    if (sql.includes('select') && sql.includes('users')) {
      return this.getAllRecords('users', params);
    } else if (sql.includes('select') && sql.includes('deals')) {
      return this.getAllRecords('deals', params);
    } else if (sql.includes('select') && sql.includes('reports')) {
      return this.getAllRecords('reports', params);
    } else if (sql.includes('select') && sql.includes('consultants')) {
      return this.getAllRecords('consultants', params);
    }

    return [];
  }

  insertRecord(tableName, params) {
    const id = this.db.nextId++;
    const now = new Date().toISOString();

    // Create a basic record structure
    const record = {
      id,
      created_at: now,
      updated_at: now,
      sync_status: 'pending'
    };

    // Add to table
    if (!this.db.data[tableName]) {
      this.db.data[tableName] = [];
    }

    this.db.data[tableName].push(record);
    this.db.saveData();

    return { changes: 1, lastInsertRowid: id };
  }

  updateRecord(params) {
    return { changes: 1 };
  }

  deleteRecord(params) {
    return { changes: 1 };
  }

  getRecord(tableName, params) {
    if (!this.db.data[tableName]) return null;

    // Simple ID-based lookup for mock
    const id = params[0];
    return this.db.data[tableName].find(record => record.id === id) || null;
  }

  getAllRecords(tableName, params) {
    if (!this.db.data[tableName]) return [];

    // Return all records for mock
    return [...this.db.data[tableName]];
  }

  extractTableName() {
    const sql = this.sql.toLowerCase();
    if (sql.includes('users')) return 'users';
    if (sql.includes('deals')) return 'deals';
    if (sql.includes('reports')) return 'reports';
    if (sql.includes('consultants')) return 'consultants';
    return 'unknown';
  }
}

// Mock database manager
class MockDatabaseManager {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  async initialize(isDev = false) {
    this.db = new MockDatabase();
    await this.db.initialize(isDev);
    this.isInitialized = true;
    return this.db;
  }

  getDatabase() {
    if (!this.isInitialized) {
      throw new Error('Mock Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  close() {
    if (this.db) {
      this.db.close();
      this.isInitialized = false;
    }
  }

  getDatabasePath() {
    return this.db ? this.db.getDatabasePath() : null;
  }
}

const mockDatabaseManager = new MockDatabaseManager();

module.exports = {
  MockDatabase,
  MockDatabaseManager,
  mockDatabaseManager
};
