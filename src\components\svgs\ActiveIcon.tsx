import React from "react";

interface IActiveIconProps {
  color: string;
}

export default function ActiveIcon({ color }: IActiveIconProps) {
  return (
    <svg
      width="9"
      height="9"
      viewBox="0 0 9 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="4.5"
        cy="4.5"
        r="3.5"
        fill="#F1F3F2"
        stroke={color}
        strokeWidth="2"
      />
    </svg>
  );
}
