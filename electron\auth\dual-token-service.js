/**
 * Dual-Expiration Token Service
 * Handles JWT token validation and management for online/offline modes
 */

const jwt = require('jsonwebtoken');
const SecureTokenStorage = require('./secure-token-storage');

class DualTokenService {
  constructor() {
    this.tokenStorage = new SecureTokenStorage();
    this.isOnline = true; // Will be updated by connectivity detection
    
    // Token configuration
    this.config = {
      onlineExpirationMinutes: 60, // 1 hour for online mode
      offlineExpirationDays: 30,   // 30 days for offline mode
      refreshThresholdMinutes: 10, // Refresh when less than 10 minutes remaining
      maxOfflineDays: 30          // Maximum offline period before re-auth required
    };

    console.log('🔐 Dual token service initialized');
  }

  /**
   * Update online status
   * @param {boolean} online - Whether the app is online
   */
  setOnlineStatus(online) {
    const wasOnline = this.isOnline;
    this.isOnline = online;
    
    if (!wasOnline && online) {
      console.log('🌐 Back online - checking for token refresh');
      this.handleOnlineReconnection();
    } else if (wasOnline && !online) {
      console.log('📴 Gone offline - switching to offline token validation');
    }
  }

  /**
   * Process login response and store tokens with dual expiration
   * @param {Object} loginResponse - Response from login API
   * @returns {Object} Processed token data
   */
  processLoginResponse(loginResponse) {
    try {
      const { jwt: jwtData, user, ...otherData } = loginResponse;
      
      if (!jwtData || !jwtData.token) {
        throw new Error('Invalid login response: missing JWT token');
      }

      // Decode token to get standard expiration
      const decodedToken = jwt.decode(jwtData.token);
      if (!decodedToken) {
        throw new Error('Invalid JWT token format');
      }

      const currentTime = Math.floor(Date.now() / 1000);
      
      // Calculate expiration times
      const onlineExpiresIn = this.config.onlineExpirationMinutes * 60;
      const offlineExpiresIn = this.config.offlineExpirationDays * 24 * 60 * 60;

      // Prepare token data for storage
      const tokenData = {
        access_token: jwtData.token,
        refresh_token: jwtData.refreshToken,
        expires_in: onlineExpiresIn,
        offline_expires_in: offlineExpiresIn,
        user: {
          id: user?.id || decodedToken.sub,
          email: user?.email || decodedToken.email,
          username: user?.username || user?.email,
          role: user?.role || decodedToken.role,
          permissions: user?.permissions || decodedToken.permissions || []
        },
        original_exp: decodedToken.exp,
        issued_at: currentTime
      };

      // Store tokens securely
      const stored = this.tokenStorage.storeTokens(tokenData);
      
      if (!stored) {
        throw new Error('Failed to store tokens securely');
      }

      console.log('✅ Login tokens processed and stored with dual expiration');
      
      return {
        success: true,
        tokens: {
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token
        },
        user: tokenData.user,
        expiresAt: currentTime + onlineExpiresIn,
        offlineExpiresAt: currentTime + offlineExpiresIn
      };
    } catch (error) {
      console.error('❌ Failed to process login response:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate current authentication status
   * @returns {Object} Authentication validation result
   */
  validateAuthentication() {
    try {
      // Check if tokens exist
      if (!this.tokenStorage.hasTokens()) {
        return {
          isAuthenticated: false,
          reason: 'no_tokens',
          requiresLogin: true
        };
      }

      // Validate tokens based on online/offline status
      const validation = this.tokenStorage.validateTokens(this.isOnline);
      
      if (!validation.isValid) {
        return {
          isAuthenticated: false,
          reason: validation.reason,
          requiresLogin: validation.reason === 'offline_expired',
          canRefresh: validation.reason === 'online_expired' && this.isOnline
        };
      }

      // Check if token needs refresh
      const needsRefresh = this.shouldRefreshToken(validation.tokens);

      return {
        isAuthenticated: true,
        mode: validation.mode,
        tokens: validation.tokens,
        user: validation.tokens.user,
        expiresIn: validation.expires_in,
        needsRefresh,
        canRefresh: this.isOnline && needsRefresh
      };
    } catch (error) {
      console.error('❌ Authentication validation failed:', error);
      return {
        isAuthenticated: false,
        reason: 'validation_error',
        error: error.message,
        requiresLogin: true
      };
    }
  }

  /**
   * Check if token should be refreshed
   * @param {Object} tokens - Token data
   * @returns {boolean} Whether token should be refreshed
   */
  shouldRefreshToken(tokens) {
    if (!this.isOnline) {
      return false; // Can't refresh when offline
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = tokens.expires_at - currentTime;
    const refreshThreshold = this.config.refreshThresholdMinutes * 60;

    return timeUntilExpiry <= refreshThreshold;
  }

  /**
   * Get current access token for API requests
   * @returns {string|null} Access token or null if not authenticated
   */
  getAccessToken() {
    const auth = this.validateAuthentication();
    
    if (!auth.isAuthenticated) {
      return null;
    }

    return auth.tokens.access_token;
  }

  /**
   * Get current refresh token
   * @returns {string|null} Refresh token or null if not available
   */
  getRefreshToken() {
    const tokens = this.tokenStorage.getTokens();
    return tokens?.refresh_token || null;
  }

  /**
   * Get current user information
   * @returns {Object|null} User data or null if not authenticated
   */
  getCurrentUser() {
    const auth = this.validateAuthentication();
    return auth.isAuthenticated ? auth.user : null;
  }

  /**
   * Handle token refresh
   * @param {Object} refreshResponse - Response from refresh API
   * @returns {Object} Refresh result
   */
  handleTokenRefresh(refreshResponse) {
    try {
      if (!refreshResponse || !refreshResponse.jwt || !refreshResponse.jwt.token) {
        throw new Error('Invalid refresh response');
      }

      const currentTime = Math.floor(Date.now() / 1000);
      
      // Update tokens with new expiration
      const updateData = {
        access_token: refreshResponse.jwt.token,
        refresh_token: refreshResponse.jwt.refreshToken || this.getRefreshToken(),
        expires_in: this.config.onlineExpirationMinutes * 60,
        // Keep existing offline expiration unless explicitly provided
        offline_expires_in: null
      };

      const updated = this.tokenStorage.updateTokens(updateData);
      
      if (!updated) {
        throw new Error('Failed to update tokens');
      }

      console.log('🔄 Tokens refreshed successfully');
      
      return {
        success: true,
        access_token: updateData.access_token,
        expiresAt: currentTime + updateData.expires_in
      };
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle reconnection to online mode
   */
  async handleOnlineReconnection() {
    const auth = this.validateAuthentication();
    
    if (auth.isAuthenticated && auth.needsRefresh) {
      console.log('🔄 Attempting automatic token refresh after reconnection');
      // This would trigger a refresh request through the main authentication service
      return { shouldRefresh: true, refreshToken: this.getRefreshToken() };
    }
    
    return { shouldRefresh: false };
  }

  /**
   * Logout and clear all tokens
   * @returns {boolean} Success status
   */
  logout() {
    try {
      const cleared = this.tokenStorage.clearTokens();
      console.log('👋 User logged out, tokens cleared');
      return cleared;
    } catch (error) {
      console.error('❌ Logout failed:', error);
      return false;
    }
  }

  /**
   * Get authentication statistics
   * @returns {Object} Authentication statistics
   */
  getAuthStats() {
    const stats = this.tokenStorage.getStorageStats();
    const auth = this.validateAuthentication();
    
    return {
      ...stats,
      isOnline: this.isOnline,
      isAuthenticated: auth.isAuthenticated,
      authMode: auth.mode,
      needsRefresh: auth.needsRefresh,
      canRefresh: auth.canRefresh,
      config: this.config
    };
  }

  /**
   * Update token configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Token configuration updated:', this.config);
  }

  /**
   * Cleanup expired tokens
   * @returns {boolean} Whether cleanup was performed
   */
  cleanupExpiredTokens() {
    return this.tokenStorage.cleanupExpiredTokens();
  }

  /**
   * Check if user has specific permission
   * @param {string} permission - Permission to check
   * @returns {boolean} Whether user has permission
   */
  hasPermission(permission) {
    const user = this.getCurrentUser();
    if (!user || !user.permissions) {
      return false;
    }

    return user.permissions.includes(permission) || user.permissions.includes('*');
  }

  /**
   * Check if user has specific role
   * @param {string} role - Role to check
   * @returns {boolean} Whether user has role
   */
  hasRole(role) {
    const user = this.getCurrentUser();
    if (!user || !user.role) {
      return false;
    }

    return user.role === role || user.role === 'admin';
  }

  /**
   * Get token expiration info
   * @returns {Object} Expiration information
   */
  getExpirationInfo() {
    const tokens = this.tokenStorage.getTokens();
    if (!tokens) {
      return null;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    
    return {
      onlineExpiresIn: Math.max(0, tokens.expires_at - currentTime),
      offlineExpiresIn: Math.max(0, tokens.offline_expires_at - currentTime),
      onlineExpiresAt: tokens.expires_at,
      offlineExpiresAt: tokens.offline_expires_at,
      isOnlineExpired: currentTime > tokens.expires_at,
      isOfflineExpired: currentTime > tokens.offline_expires_at
    };
  }
}

module.exports = DualTokenService;
