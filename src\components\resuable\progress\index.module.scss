@import "../../../../styles/_mixins.scss";

.wrapper {
  cursor: pointer;
  background-color: #f1f3f2;
  color: var(--white);
  @include font(0.8em, 500);
  transition: 0.1s;

  &.roundFull {
    border-radius: 30px;
  }

  &.roundHalf {
    border-radius: 0 30px 30px 0;
  }

  &:hover {
    opacity: 0.8;
  }

  > div {
    transition: all 0.7s ease;
  }

  .innerWrapper {
    height: 100vh;
  }

  .zero {
    background-color: none;
    color: black;
    text-align: center;
    @include font(0.85em, 600);
  }

  .success {
    background-color: var(--risk-primary);

    &.roundFull {
      border-radius: 30px;
    }

    &.roundHalf {
      border-radius: 0 30px 30px 0;
    }
  }

  .danger {
    background-color: #ff0000;
  }
}
