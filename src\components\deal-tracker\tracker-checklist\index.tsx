import React from "react";

import styles from "./index.module.scss";

import { checkListData } from "../../../data/tracker-form";

import Checkbox from "../../resuable/checkbox";

import ProgressBar from "./../../resuable/progress/";

import { useParams } from "react-router";

const TrackerCheckList = ({
  progress,
  formik,
  isLoading,
}: {
  progress: number;
  formik?: any;
  isLoading: boolean;
}) => {
  const params: any = useParams();

  const handleSubmit = () => {
    formik.handleSubmit({ ...formik.values, id: params?.id });
  };

  return (
    <section className={`${styles.wrapper} position-relative pb-5`}>
      <div className={`px-5 position-sticky ${styles.stickyBar} pt-1 px-5`}>
        <ProgressBar
          {...{
            color: "success",
            progress,
          }}
          rounded
        />
      </div>
      <div>
        {checkListData.map((list: any, index) => {
          return (
            <React.Fragment key={index}>
              <div
                className={`d-flex justify-content-center align-items-center mt-4`}
              >
                <div
                  className={`text-right ${styles.title} `}
                  style={{ flex: 8 }}
                >
                  {list.header.title}
                </div>

                <div style={{ flex: 2 }}></div>
                <div className={`${styles.percentage}`} style={{ flex: 2 }}>
                  ({list.header.percentage}%)
                </div>
              </div>
              <div>
                {list.items.map((item: any) => {
                  return (
                    <div key={item.name}>
                      <Checkbox
                        {...{
                          formik,
                          name: item.name,
                          label: item.label,
                        }}
                      />
                    </div>
                  );
                })}
              </div>
            </React.Fragment>
          );
        })}
      </div>

      <div className="text-center mt-4">
        <button
          onClick={handleSubmit}
          type="button"
          className={`${styles.button} py-2`}
          disabled={isLoading}
        >
          {isLoading ? "Saving" : "Save"}
        </button>
      </div>
    </section>
  );
};

export default TrackerCheckList;
