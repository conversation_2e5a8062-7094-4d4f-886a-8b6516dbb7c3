import React from 'react';
import { useDualAuth } from '../../hooks/useDualAuth';
import styles from './SessionWarning.module.scss';

interface SessionWarningProps {
  className?: string;
  position?: 'top' | 'bottom' | 'inline';
  showDetails?: boolean;
  autoHide?: boolean;
}

/**
 * Session warning component that displays offline session status and warnings
 */
const SessionWarning: React.FC<SessionWarningProps> = ({
  className = '',
  position = 'top',
  showDetails = true,
  autoHide = false
}) => {
  const {
    isAuthenticated,
    isOfflineMode,
    hasActiveOfflineSession,
    offlineTimeRemaining,
    offlineTimeUsedPercent,
    isNearOfflineLimit,
    isOfflineSessionCritical,
    isOfflineSessionExpired,
    offlineDaysRemaining,
    sessionStats
  } = useDualAuth();

  // Don't show if not authenticated or no session data
  if (!isAuthenticated || !sessionStats) {
    return null;
  }

  // Don't show if online and auto-hide is enabled
  if (autoHide && !isOfflineMode && !isNearOfflineLimit) {
    return null;
  }

  // Don't show if session is expired (user should be logged out)
  if (isOfflineSessionExpired) {
    return null;
  }

  const formatTimeRemaining = (seconds: number): string => {
    const days = Math.floor(seconds / (24 * 60 * 60));
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((seconds % (60 * 60)) / 60);

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const getWarningLevel = () => {
    if (isOfflineSessionCritical) return 'critical';
    if (isNearOfflineLimit) return 'warning';
    if (hasActiveOfflineSession) return 'info';
    return 'normal';
  };

  const getWarningMessage = () => {
    if (isOfflineSessionCritical) {
      return `Critical: Session expires in ${formatTimeRemaining(offlineTimeRemaining)}`;
    }
    if (isNearOfflineLimit) {
      return `Warning: Session expires in ${Math.ceil(offlineDaysRemaining)} days`;
    }
    if (hasActiveOfflineSession) {
      return `Offline session active: ${Math.ceil(offlineDaysRemaining)} days remaining`;
    }
    return 'Session status normal';
  };

  const getProgressBarColor = () => {
    if (isOfflineSessionCritical) return '#dc3545'; // Red
    if (isNearOfflineLimit) return '#ffc107'; // Yellow
    return '#28a745'; // Green
  };

  const warningLevel = getWarningLevel();
  const message = getWarningMessage();

  return (
    <div className={`${styles.sessionWarning} ${styles[position]} ${styles[warningLevel]} ${className}`}>
      <div className={styles.content}>
        <div className={styles.header}>
          <span className={styles.icon}>
            {isOfflineSessionCritical ? '🔴' : isNearOfflineLimit ? '🟡' : hasActiveOfflineSession ? '📴' : '🟢'}
          </span>
          <span className={styles.message}>{message}</span>
        </div>

        {showDetails && (hasActiveOfflineSession || isNearOfflineLimit) && (
          <div className={styles.details}>
            <div className={styles.progressContainer}>
              <div className={styles.progressLabel}>
                <span>Offline time used</span>
                <span>{Math.round(offlineTimeUsedPercent)}%</span>
              </div>
              <div className={styles.progressBar}>
                <div 
                  className={styles.progressFill}
                  style={{ 
                    width: `${Math.min(100, offlineTimeUsedPercent)}%`,
                    backgroundColor: getProgressBarColor()
                  }}
                />
              </div>
            </div>

            <div className={styles.stats}>
              <div className={styles.stat}>
                <span className={styles.statLabel}>Time Remaining:</span>
                <span className={styles.statValue}>{formatTimeRemaining(offlineTimeRemaining)}</span>
              </div>
              <div className={styles.stat}>
                <span className={styles.statLabel}>Days Left:</span>
                <span className={styles.statValue}>{Math.ceil(offlineDaysRemaining)}</span>
              </div>
              {hasActiveOfflineSession && (
                <div className={styles.stat}>
                  <span className={styles.statLabel}>Status:</span>
                  <span className={styles.statValue}>Offline Mode</span>
                </div>
              )}
            </div>

            {isOfflineSessionCritical && (
              <div className={styles.criticalWarning}>
                <strong>⚠️ Action Required:</strong> Please reconnect to the internet soon to avoid being logged out.
              </div>
            )}

            {isNearOfflineLimit && !isOfflineSessionCritical && (
              <div className={styles.reminder}>
                <strong>💡 Reminder:</strong> Consider reconnecting to refresh your session.
              </div>
            )}
          </div>
        )}
      </div>

      {(isOfflineSessionCritical || isNearOfflineLimit) && (
        <div className={styles.actions}>
          <button 
            className={styles.actionButton}
            onClick={() => window.location.reload()}
            title="Refresh to check connection"
          >
            Check Connection
          </button>
        </div>
      )}
    </div>
  );
};

export default SessionWarning;
