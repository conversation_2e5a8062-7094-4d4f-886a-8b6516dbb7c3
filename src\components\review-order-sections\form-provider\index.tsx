import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { initInitialValues } from "../../../data/form-initial-values";
import { useParams } from "react-router";
import { usePostOrder } from "./../../../apis/index";

interface IFormProvider {
  children: React.ReactNode;
  data?: any;
}

const FormProvider: React.FC<IFormProvider> = ({ children, data }) => {
  const param = useParams<any>();

  const {
    mutate,
    isLoading: isCreating,
    isError: isCreatingError,
    data: isCreatingData,
  } = usePostOrder("/order");

  //const { data, isLoading, error, isError } = useGetSingleOrder(
  //  "/order",
  //  param?.id === "create" ? param?.id : Number(param?.id), dispatch
  //);

  //if (isLoading) {
  //  return <p>loading...</p>;
  //}
  //if (!data && param?.id !== "create")
  //  return <p>Oops, something went wrong.</p>;

  //if (isError || error) return <p>Oops, something went wrong.</p>;

  const initialValues = initInitialValues(data);

  const validationSchema = Yup.object({
    mortgage_lender: Yup.string().typeError("Please enter mortgage lender"),
    mortgage_loan_amount: Yup.string().typeError(
      "Please enter mortgage loan amount"
    ),
    property_name: Yup.string().typeError("Please, enter a property name"),
  });

  const handleSubmit = (v: any) => {
    let formData;
    if (isCreatingData?.order_id && param?.id === "create") {
      formData = { id: Number(isCreatingData?.order_id), ...v };
    } else if (param?.id === "create" && isCreatingData?.order_id === false) {
      formData = v;
    } else {
      formData = { id: Number(param?.id), ...v };
    }

    mutate(formData);
  };

  // const renderError = (message: string) => (
  //   <p className="bg-danger text-white"> {message}</p>
  // );

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={(v) => {
        handleSubmit(v);
      }}
      validationSchema={validationSchema}
    >
      {(formik) => {
        return (
          <Form>
            {React.Children.map(children, (child: any, i) => {
              if (i === 0) {
                return React.cloneElement(
                  child,
                  {
                    apiStatus: param?.id === "create" ? true : false,
                    isCreating,
                    isCreatingError,
                    isCreatingData,
                    topData: data,
                  },
                  null
                );
              }
              return React.cloneElement(child, { formik }, null);
            })}
          </Form>
        );
      }}
    </Formik>
  );
};

export default FormProvider;
