export interface orderSummary {
  orderSubmission: string;
  requestedItems: string;
  loanClosingDate: string;
  insuranceReview: string;
  reviewNeedByDate: string;

  loanExecution: string;
  mortgageLender: string;
  mezzLender: string;
  borrowerSponsorName: string;
  repeatBorrower: string;
  sponsor: string;
  loanNumberId: string;
  repoWarehouseLender: string;
  repoRepsAttached: File;

  loanType: string;
  mortgageLoanAmount: string;
  mezzLoanAmount: string;
  borrowingEntityName: string;
  recentlyReviewedDeals: string;
  loanAgreementAttached: File;
  UPBOnExistingLoans: string;

  propertyName: string;
  propertyAddress: string;
  propertyType: string;
  insurableValue: string;
  UWAnnualEGI: string;
  coOp: string;
  landValue: string;
  ageOfRoofs: string;
  numberOfStories: string;
  numberOfListedBeds: string;
  elevators: string;
  floodZone: string;
  localizedRegionalPerils: string;
  beHasEmployedAtTheProperty: string;
  environmentalInsurance: string;
  required: string;
  isPropertyParOfACondo: string;
  isAnTenantRequiredToInsureCollateral: string;
  yearBuilt: string;
  numberOfBuildings: string;
  numberOfUnits: string;
  problematicBldgMaterials: string;
  boilers: string;
  seismicPMLSEL: string;
  zoningStatus: string;
  beOperatesAutosAtTheProperty: string;
  anyConstructionAtTheProperty: string;
  isBorrowerTenantUnderAnyLease: string;

  appraisalAttached: File;
  floodZoneDeterminationAttached: File;
  seismicRiskAssessmentAttached: File;
  environmentalSiteAssessmentAttached: File;
  propertyConditionAssessmentAttached: File;
  zoningReportAttached: File;

  lenderClosingContactName: String;
  lenderClosingContactEmail: string;
  lenderClosingContactPhone: number;
  lenderUnderwritingName: string;
  lenderUnderwritingEmail: string;
  lenderUnderwritingPhone: number;

  counselContactName: string;
  counselContactEmail: string;
  counselContactPhone: number;
}
