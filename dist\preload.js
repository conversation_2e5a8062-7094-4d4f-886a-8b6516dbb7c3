/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "electron":
/*!***************************!*\
  !*** external "electron" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("electron");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!*************************************!*\
  !*** ./electron/preload/preload.js ***!
  \*************************************/
const {
  contextBridge,
  ipcRenderer
} = __webpack_require__(/*! electron */ "electron");

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getVersion: () => ipcRenderer.invoke('app-version'),
  // Dialog methods
  showMessageBox: options => ipcRenderer.invoke('show-message-box', options),
  // Menu events (listen only)
  onMenuNew: callback => ipcRenderer.on('menu-new', callback),
  onMenuOpen: callback => ipcRenderer.on('menu-open', callback),
  onMenuSave: callback => ipcRenderer.on('menu-save', callback),
  // Database operations (will be added later)
  database: {
    // Placeholder for database operations
  },
  // Network status
  onOnline: callback => {
    window.addEventListener('online', callback);
    return () => window.removeEventListener('online', callback);
  },
  onOffline: callback => {
    window.addEventListener('offline', callback);
    return () => window.removeEventListener('offline', callback);
  },
  // File system operations (will be added later)
  fileSystem: {
    // Placeholder for file system operations
  },
  // Authentication (will be added later)
  auth: {
    // Placeholder for authentication operations
  },
  // Sync operations (will be added later)
  sync: {
    // Placeholder for sync operations
  }
});

// Security: Remove access to Node.js APIs in the renderer process
delete window.require;
delete window.exports;
delete window.module;
})();

/******/ })()
;
//# sourceMappingURL=preload.js.map