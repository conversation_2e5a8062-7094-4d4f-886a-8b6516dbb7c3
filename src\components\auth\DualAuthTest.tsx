import React, { useState, useEffect } from 'react';
import { useDualAuth } from '../../hooks/useDualAuth';
import styles from './DualAuthTest.module.scss';

/**
 * Test component for dual-expiration authentication system
 * This component is for development and testing purposes
 */
const DualAuthTest: React.FC = () => {
  const {
    isAuthenticated,
    user,
    isLoading,
    error,
    mode,
    needsRefresh,
    canRefresh,
    expiresIn,
    isOnline,
    isElectron,
    isOfflineMode,
    isOnlineMode,
    sessionExpiresSoon,
    login,
    logout,
    refreshToken,
    hasPermission,
    getAuthStats,
    clearError,
    updateAuthStatus
  } = useDualAuth();

  const [showTestPanel, setShowTestPanel] = useState(false);
  const [loginForm, setLoginForm] = useState({ email: '', pass: '' });
  const [authStats, setAuthStats] = useState<any>(null);
  const [permissionTest, setPermissionTest] = useState('');
  const [permissionResult, setPermissionResult] = useState<boolean | null>(null);

  // Update auth stats periodically
  useEffect(() => {
    const updateStats = async () => {
      const stats = await getAuthStats();
      setAuthStats(stats);
    };

    updateStats();
    const interval = setInterval(updateStats, 5000);
    return () => clearInterval(interval);
  }, [getAuthStats]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!loginForm.email || !loginForm.pass) {
      return;
    }

    const result = await login(loginForm);
    if (result.success) {
      setLoginForm({ email: '', pass: '' });
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  const handleRefreshToken = async () => {
    await refreshToken();
  };

  const handlePermissionTest = async () => {
    if (!permissionTest) return;
    const result = await hasPermission(permissionTest);
    setPermissionResult(result);
  };

  const formatTime = (seconds: number) => {
    if (seconds <= 0) return 'Expired';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  if (!isElectron) {
    return (
      <div className={styles.notElectron}>
        <p>Dual-expiration authentication is only available in Electron environment.</p>
      </div>
    );
  }

  if (!showTestPanel) {
    return (
      <div className={styles.testToggle}>
        <button 
          onClick={() => setShowTestPanel(true)}
          className={styles.toggleButton}
        >
          Show Dual Auth Test Panel
        </button>
      </div>
    );
  }

  return (
    <div className={styles.testContainer}>
      <div className={styles.header}>
        <h3>Dual-Expiration Authentication Test Panel</h3>
        <button 
          onClick={() => setShowTestPanel(false)}
          className={styles.closeButton}
        >
          ×
        </button>
      </div>

      {/* Authentication Status */}
      <div className={styles.section}>
        <h4>Authentication Status</h4>
        <div className={styles.statusGrid}>
          <div className={styles.statusItem}>
            <span>Authenticated:</span>
            <span className={isAuthenticated ? styles.success : styles.error}>
              {isAuthenticated ? 'Yes' : 'No'}
            </span>
          </div>
          <div className={styles.statusItem}>
            <span>Mode:</span>
            <span className={isOnlineMode ? styles.success : isOfflineMode ? styles.warning : styles.neutral}>
              {mode || 'None'}
            </span>
          </div>
          <div className={styles.statusItem}>
            <span>Online:</span>
            <span className={isOnline ? styles.success : styles.warning}>
              {isOnline ? 'Yes' : 'No'}
            </span>
          </div>
          <div className={styles.statusItem}>
            <span>Loading:</span>
            <span>{isLoading ? 'Yes' : 'No'}</span>
          </div>
          <div className={styles.statusItem}>
            <span>Needs Refresh:</span>
            <span className={needsRefresh ? styles.warning : styles.success}>
              {needsRefresh ? 'Yes' : 'No'}
            </span>
          </div>
          <div className={styles.statusItem}>
            <span>Can Refresh:</span>
            <span className={canRefresh ? styles.success : styles.neutral}>
              {canRefresh ? 'Yes' : 'No'}
            </span>
          </div>
          <div className={styles.statusItem}>
            <span>Expires In:</span>
            <span className={sessionExpiresSoon ? styles.warning : styles.neutral}>
              {formatTime(expiresIn)}
            </span>
          </div>
        </div>
      </div>

      {/* User Information */}
      {user && (
        <div className={styles.section}>
          <h4>User Information</h4>
          <div className={styles.userInfo}>
            <div><strong>ID:</strong> {user.id}</div>
            <div><strong>Email:</strong> {user.email}</div>
            <div><strong>Username:</strong> {user.username}</div>
            <div><strong>Role:</strong> {user.role}</div>
            <div><strong>Permissions:</strong> {user.permissions?.join(', ') || 'None'}</div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className={styles.section}>
          <div className={styles.error}>
            <strong>Error:</strong> {error}
            <button onClick={clearError} className={styles.clearErrorButton}>
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Login Form */}
      {!isAuthenticated && (
        <div className={styles.section}>
          <h4>Login</h4>
          <form onSubmit={handleLogin} className={styles.loginForm}>
            <input
              type="email"
              placeholder="Email"
              value={loginForm.email}
              onChange={(e) => setLoginForm(prev => ({ ...prev, email: e.target.value }))}
              className={styles.input}
              required
            />
            <input
              type="password"
              placeholder="Password"
              value={loginForm.pass}
              onChange={(e) => setLoginForm(prev => ({ ...prev, pass: e.target.value }))}
              className={styles.input}
              required
            />
            <button type="submit" disabled={isLoading} className={styles.loginButton}>
              {isLoading ? 'Logging in...' : 'Login'}
            </button>
          </form>
        </div>
      )}

      {/* Actions */}
      {isAuthenticated && (
        <div className={styles.section}>
          <h4>Actions</h4>
          <div className={styles.actions}>
            <button onClick={handleLogout} className={styles.actionButton}>
              Logout
            </button>
            <button 
              onClick={handleRefreshToken} 
              disabled={!canRefresh}
              className={styles.actionButton}
            >
              Refresh Token
            </button>
            <button onClick={updateAuthStatus} className={styles.actionButton}>
              Update Status
            </button>
          </div>
        </div>
      )}

      {/* Permission Testing */}
      {isAuthenticated && (
        <div className={styles.section}>
          <h4>Permission Testing</h4>
          <div className={styles.permissionTest}>
            <input
              type="text"
              placeholder="Permission to test (e.g., 'admin', 'read', 'write')"
              value={permissionTest}
              onChange={(e) => setPermissionTest(e.target.value)}
              className={styles.input}
            />
            <button onClick={handlePermissionTest} className={styles.actionButton}>
              Test Permission
            </button>
            {permissionResult !== null && (
              <div className={`${styles.permissionResult} ${permissionResult ? styles.success : styles.error}`}>
                Permission '{permissionTest}': {permissionResult ? 'Granted' : 'Denied'}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Authentication Statistics */}
      {authStats && (
        <div className={styles.section}>
          <h4>Authentication Statistics</h4>
          <div className={styles.statsGrid}>
            <div className={styles.statItem}>
              <span>Has Tokens:</span>
              <span>{authStats.hasTokens ? 'Yes' : 'No'}</span>
            </div>
            <div className={styles.statItem}>
              <span>Online Expires In:</span>
              <span>{formatTime(authStats.onlineExpiresIn || 0)}</span>
            </div>
            <div className={styles.statItem}>
              <span>Offline Expires In:</span>
              <span>{formatTime(authStats.offlineExpiresIn || 0)}</span>
            </div>
            <div className={styles.statItem}>
              <span>Last Login:</span>
              <span>{authStats.lastLogin ? new Date(authStats.lastLogin * 1000).toLocaleString() : 'Never'}</span>
            </div>
            <div className={styles.statItem}>
              <span>Last Refresh:</span>
              <span>{authStats.lastRefresh ? new Date(authStats.lastRefresh * 1000).toLocaleString() : 'Never'}</span>
            </div>
          </div>
        </div>
      )}

      <div className={styles.footer}>
        <p><strong>Note:</strong> This test panel demonstrates the dual-expiration authentication system. 
        In production, authentication will be handled automatically by the application.</p>
      </div>
    </div>
  );
};

export default DualAuthTest;
