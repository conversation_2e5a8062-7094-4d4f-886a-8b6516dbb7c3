@import "../../../styles/_mixins.scss";

.statusBar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(46, 113, 111, 0.05), rgba(46, 113, 111, 0.1));
  border: 1px solid rgba(46, 113, 111, 0.15);
  border-radius: 8px;
  font-family: inherit;
  transition: all 0.3s ease;

  &.top {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-top: none;
    background: linear-gradient(135deg, rgba(46, 113, 111, 0.95), rgba(46, 113, 111, 0.98));
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  &.bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-bottom: none;
    background: linear-gradient(135deg, rgba(46, 113, 111, 0.95), rgba(46, 113, 111, 0.98));
    backdrop-filter: blur(10px);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

  &.inline {
    position: relative;
    width: 100%;
  }

  .statusItem {
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;

    .icon {
      font-size: 0.9em;
      transition: transform 0.2s ease;

      &.spinning {
        animation: spin 1s linear infinite;
      }
    }

    .text {
      @include font(0.8em, 500);
      color: rgba(24, 24, 24, 0.8);
      white-space: nowrap;
      transition: color 0.2s ease;
    }

    &.online {
      .text {
        color: var(--success);
      }
      
      .icon {
        animation: pulse 2s infinite;
      }
    }

    &.offline {
      .text {
        color: var(--warning);
      }
      
      .icon {
        animation: blink 1s infinite;
      }
    }

    &.syncing {
      .text {
        color: var(--primary-light);
      }
      
      .icon {
        animation: spin 1s linear infinite;
      }
    }

    &.success {
      .text {
        color: var(--success);
      }
    }

    &.error {
      .text {
        color: var(--danger);
      }
      
      .icon {
        animation: shake 0.5s infinite;
      }
    }

    &.idle {
      .text {
        color: rgba(24, 24, 24, 0.6);
      }
    }

    &.pending {
      .text {
        color: var(--warning);
        @include font(0.75em, 600);
      }
    }
  }

  .divider {
    width: 1px;
    height: 16px;
    background-color: rgba(46, 113, 111, 0.2);
    flex-shrink: 0;
  }

  .offlineInfo {
    .infoText {
      @include font(0.75em, 400);
      color: rgba(24, 24, 24, 0.6);
      font-style: italic;
    }
  }

  .retryButton {
    padding: 4px 8px;
    background-color: var(--risk-primary);
    color: white;
    border: none;
    border-radius: 4px;
    @include font(0.7em, 500);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background-color: var(--risk-primary-light);
      transform: translateY(-1px);
    }

    &:disabled {
      background-color: rgba(46, 113, 111, 0.3);
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  .dismissButton {
    margin-left: auto;
    padding: 2px 6px;
    background: none;
    border: none;
    color: rgba(24, 24, 24, 0.5);
    @include font(1.2em, 400);
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(24, 24, 24, 0.1);
      color: rgba(24, 24, 24, 0.8);
    }
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.4;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

// Responsive design
@media screen and (max-width: 768px) {
  .statusBar {
    padding: 6px 12px;
    gap: 8px;

    .statusItem .text {
      @include font(0.75em, 500);
    }

    .offlineInfo .infoText {
      @include font(0.7em, 400);
    }

    .retryButton {
      padding: 3px 6px;
      @include font(0.65em, 500);
    }
  }
}

@media screen and (max-width: 480px) {
  .statusBar {
    padding: 4px 8px;
    gap: 6px;

    .statusItem .text {
      @include font(0.7em, 500);
    }

    .divider {
      height: 12px;
    }

    // Hide some elements on very small screens
    .offlineInfo {
      display: none;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .statusBar {
    border-width: 2px;
    
    .statusItem {
      &.online .text {
        color: #008000;
      }
      
      &.offline .text {
        color: #ff6600;
      }
      
      &.error .text {
        color: #cc0000;
      }
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .statusBar .statusItem .icon {
    animation: none !important;
  }
  
  .retryButton:hover:not(:disabled) {
    transform: none;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .statusBar {
    background: linear-gradient(135deg, rgba(24, 24, 24, 0.9), rgba(24, 24, 24, 0.95));
    border-color: rgba(255, 255, 255, 0.15);

    .statusItem .text {
      color: rgba(255, 255, 255, 0.8);
    }

    .statusItem.idle .text {
      color: rgba(255, 255, 255, 0.6);
    }

    .divider {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .offlineInfo .infoText {
      color: rgba(255, 255, 255, 0.6);
    }

    .dismissButton {
      color: rgba(255, 255, 255, 0.5);

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}
