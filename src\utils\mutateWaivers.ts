export const mutateWaivers = (values: { [key: string]: any }) => {
  const waivers = values["waivers"];

  const listOf: Array<string> = [
    "type",
    "waiver_exeception",
    "status",
    "comment",
  ];

  let ob = Object.create({});

  const newWaivers = [];

  waivers.forEach((waiver: { [key: string]: any }) => {
    Object.entries(waiver).forEach(([key, value]: any) => {
      if (listOf.indexOf(key) >= 0) {
        ob[key] = { value };
      } else {
        ob[key] = value;
      }
    });

    newWaivers.push(ob);
    ob = Object.create({});
  });
};
