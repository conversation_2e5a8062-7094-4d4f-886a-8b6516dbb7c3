---
description: Guidelines for leveraging <PERSON>'s advanced capabilities in the risk-app development project
globs: **/*
alwaysApply: true
---

# Claude-Specific Development Guidelines

## Desktop Commander Integration

- **File Operations:**
  - Use Desktop Commander for all file system operations
  - Leverage `read_file`, `write_file`, and `edit_block` for precise modifications
  - Use `search_files` and `search_code` for intelligent code discovery
  - Employ `list_directory` for project structure analysis

- **Code Analysis:**
  - Use multi-file reading to understand architectural patterns
  - Leverage code search to identify similar implementations
  - Employ file metadata analysis for understanding project structure
  - Use intelligent editing for surgical code modifications

## Web Search Integration

- **Research Best Practices:**
  - Search for latest Electron development patterns
  - Research offline-first architecture examples
  - Find SQLite synchronization strategies
  - Discover security best practices for desktop applications

- **Technology Updates:**
  - Stay current with Electron security recommendations
  - Research latest better-sqlite3 features and optimizations
  - Find React integration patterns with Electron
  - Discover cross-platform compatibility solutions

## Artifact Creation for Complex Examples

- **Documentation:**
  - Create comprehensive setup guides
  - Generate configuration examples
  - Build architectural diagrams and flow charts
  - Develop troubleshooting guides

- **Code Templates:**
  - Generate boilerplate code for common patterns
  - Create reusable component templates
  - Build database schema examples
  - Develop testing templates and utilities

## Analysis Tool Usage

- **Complex Calculations:**
  - Use for database schema analysis and optimization
  - Calculate sync performance metrics and benchmarks
  - Analyze dependency graphs and complexity metrics
  - Process configuration data and validation

- **Data Processing:**
  - Parse and analyze PRD requirements
  - Process task complexity reports
  - Analyze project structure and dependencies
  - Generate metrics and reporting data

## Enhanced Task Management

- **Intelligent Task Breakdown:**
  - Consider Electron-specific architectural constraints
  - Factor in cross-platform compatibility requirements
  - Account for offline-first design principles
  - Include security considerations from the start

- **Context-Aware Updates:**
  - Understand impact of changes on sync mechanisms
  - Consider database schema evolution requirements
  - Factor in authentication system implications
  - Account for UI/UX consistency across platforms

## Project-Specific Patterns

- **Electron Architecture:**
  - Main process handles database and native operations
  - Renderer process focuses on React UI components
  - IPC communication follows security best practices
  - Context isolation maintained throughout

- **Offline-First Design:**
  - Local SQLite database as primary data store
  - Sync mechanisms handle conflict resolution
  - Queue system manages offline operations
  - Network detection drives application behavior

For comprehensive tool references, see [taskmaster.mdc](mdc:.claude/rules/taskmaster.mdc) and [dev_workflow.mdc](mdc:.claude/rules/dev_workflow.mdc).
