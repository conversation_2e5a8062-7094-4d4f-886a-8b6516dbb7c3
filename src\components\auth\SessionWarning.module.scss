.sessionWarning {
  border-radius: 8px;
  padding: 12px 16px;
  margin: 8px 0;
  border-left: 4px solid;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &.top {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    min-width: 400px;
    max-width: 600px;
  }

  &.bottom {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    min-width: 400px;
    max-width: 600px;
  }

  &.inline {
    position: relative;
    width: 100%;
  }

  // Warning levels
  &.normal {
    border-left-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
  }

  &.info {
    border-left-color: #17a2b8;
    background: rgba(23, 162, 184, 0.05);
  }

  &.warning {
    border-left-color: #ffc107;
    background: rgba(255, 193, 7, 0.05);
  }

  &.critical {
    border-left-color: #dc3545;
    background: rgba(220, 53, 69, 0.05);
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
    50% { box-shadow: 0 4px 16px rgba(220, 53, 69, 0.3); }
    100% { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.icon {
  font-size: 16px;
  flex-shrink: 0;
}

.message {
  flex: 1;
  color: #333;
  font-size: 14px;
}

.details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-left: 24px;
}

.progressContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progressLabel {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.progressBar {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
  border-radius: 3px;
}

.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.statLabel {
  font-size: 11px;
  color: #666;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.statValue {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.criticalWarning {
  padding: 8px 12px;
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 4px;
  font-size: 12px;
  color: #721c24;
}

.reminder {
  padding: 8px 12px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.2);
  border-radius: 4px;
  font-size: 12px;
  color: #856404;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.actionButton {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #0056b3;
  }

  &:active {
    transform: translateY(1px);
  }
}

// Responsive design
@media (max-width: 768px) {
  .sessionWarning {
    &.top,
    &.bottom {
      left: 10px;
      right: 10px;
      transform: none;
      min-width: auto;
      max-width: none;
    }
  }

  .stats {
    grid-template-columns: 1fr 1fr;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .message {
    font-size: 13px;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .sessionWarning {
    background: rgba(33, 37, 41, 0.95);
    color: #f8f9fa;

    &.normal {
      background: rgba(40, 167, 69, 0.1);
    }

    &.info {
      background: rgba(23, 162, 184, 0.1);
    }

    &.warning {
      background: rgba(255, 193, 7, 0.1);
    }

    &.critical {
      background: rgba(220, 53, 69, 0.1);
    }
  }

  .message {
    color: #f8f9fa;
  }

  .statValue {
    color: #f8f9fa;
  }

  .progressBar {
    background: #495057;
  }

  .criticalWarning {
    background: rgba(220, 53, 69, 0.2);
    color: #f5c6cb;
  }

  .reminder {
    background: rgba(255, 193, 7, 0.2);
    color: #fff3cd;
  }
}
