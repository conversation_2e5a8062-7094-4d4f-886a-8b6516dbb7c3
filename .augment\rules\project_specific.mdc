---
description: Guidelines for leveraging Augment Agent's advanced capabilities in the risk-app Electron desktop development project
globs: **/*
alwaysApply: true
---

# Augment Agent-Specific Development Guidelines

## Codebase-Retrieval Integration

- **Comprehensive Code Analysis:**
  - Use codebase-retrieval for understanding entire project architecture
  - Leverage real-time indexing to understand code relationships
  - Employ natural language queries to find relevant code patterns
  - Use context engine to identify cross-cutting concerns and dependencies

- **Architectural Understanding:**
  - Analyze existing React web app structure before Electron migration
  - Understand current authentication patterns and database interactions
  - Identify reusable components and patterns for desktop adaptation
  - Map out data flow and state management approaches

## Web Search Integration

- **Research Best Practices:**
  - Search for latest Electron development patterns and security recommendations
  - Research offline-first architecture examples and implementation strategies
  - Find SQLite synchronization strategies and conflict resolution approaches
  - Discover security best practices for desktop applications with JWT authentication

- **Technology Updates:**
  - Stay current with Electron security recommendations and updates
  - Research latest better-sqlite3 features and performance optimizations
  - Find React integration patterns with Electron and IPC communication
  - Discover cross-platform compatibility solutions and packaging strategies

## File Operations for Precise Modifications

- **Surgical Code Changes:**
  - Use str-replace-editor for precise code modifications
  - Leverage save-file for creating new components and modules
  - Employ view tool for understanding existing code structure
  - Use remove-files for cleaning up obsolete code

- **Project Structure Management:**
  - Create organized folder structures for Electron main/renderer processes
  - Maintain separation of concerns between web and desktop code
  - Organize database-related code in dedicated modules
  - Structure authentication and sync logic appropriately

## Package Management Excellence

- **Dependency Management:**
  - Always use npm/yarn for installing Electron and related packages
  - Use codebase-retrieval to understand existing dependency patterns
  - Consider impact of new dependencies on bundle size and security
  - Maintain consistency with existing package management approaches

- **Electron-Specific Dependencies:**
  - Use package managers for electron, electron-builder, better-sqlite3
  - Research and install appropriate IPC and security packages
  - Consider auto-updater and packaging dependencies
  - Evaluate offline-first and sync-related packages

## Enhanced Task Management

- **Intelligent Task Breakdown:**
  - Consider Electron-specific architectural constraints discovered through codebase analysis
  - Factor in cross-platform compatibility requirements based on existing patterns
  - Account for offline-first design principles and existing data patterns
  - Include security considerations based on current authentication implementation

- **Context-Aware Updates:**
  - Understand impact of changes on sync mechanisms through codebase analysis
  - Consider database schema evolution requirements based on existing data models
  - Factor in authentication system implications using current JWT implementation
  - Account for UI/UX consistency across platforms based on existing React components

## Project-Specific Patterns

- **Electron Architecture:**
  - Main process handles database operations, file system access, and native OS integration
  - Renderer process focuses on existing React UI components with minimal modifications
  - IPC communication follows security best practices and existing patterns
  - Context isolation maintained throughout with proper security configuration

- **Offline-First Design:**
  - Local SQLite database as primary data store, syncing with existing PostgreSQL
  - Sync mechanisms handle conflict resolution based on existing data patterns
  - Queue system manages offline operations using existing business logic
  - Network detection drives application behavior and user experience

- **Authentication Integration:**
  - Leverage existing JWT authentication system with dual-expiration tokens
  - Integrate with current PostgreSQL user management
  - Maintain security standards from existing web application
  - Implement secure token storage for desktop environment

## Codebase Context Utilization

- **Pattern Recognition:**
  - Identify existing React component patterns for reuse in Electron renderer
  - Understand current API integration patterns for desktop adaptation
  - Analyze existing error handling and validation approaches
  - Discover reusable utility functions and helper modules

- **Architecture Analysis:**
  - Map existing data flow for offline-first adaptation
  - Understand current state management for desktop integration
  - Analyze existing routing patterns for Electron adaptation
  - Identify security patterns for desktop enhancement

For comprehensive tool references, see [taskmaster.mdc](mdc:.augment/rules/taskmaster.mdc) and [dev_workflow.mdc](mdc:.augment/rules/dev_workflow.mdc).
