import React from "react";

import styles from "./index.module.scss";

const sortIcon = (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.977191 2.42042C0.680202 2.70916 0.673514 3.18399 0.962254 3.48098C1.25099 3.77797 1.72582 3.78466 2.02281 3.49592L0.977191 2.42042ZM3.6 0.916504H4.35C4.35 0.615079 4.16955 0.342941 3.89188 0.225633C3.61422 0.108324 3.29331 0.168641 3.07719 0.378758L3.6 0.916504ZM2.85 9.08317C2.85 9.49739 3.18579 9.83317 3.6 9.83317C4.01421 9.83317 4.35 9.49739 4.35 9.08317H2.85ZM9.02281 7.57925C9.3198 7.29051 9.32649 6.81568 9.03775 6.5187C8.74901 6.22171 8.27418 6.21502 7.97719 6.50376L9.02281 7.57925ZM6.4 9.08317H5.65C5.65 9.3846 5.83045 9.65673 6.10811 9.77404C6.38578 9.89135 6.70669 9.83103 6.92281 9.62092L6.4 9.08317ZM7.15 0.916504C7.15 0.50229 6.81421 0.166504 6.4 0.166504C5.98579 0.166504 5.65 0.50229 5.65 0.916504H7.15ZM2.02281 3.49592L4.12281 1.45425L3.07719 0.378758L0.977191 2.42042L2.02281 3.49592ZM2.85 0.916504V9.08317H4.35V0.916504H2.85ZM7.97719 6.50376L5.87719 8.54542L6.92281 9.62092L9.02281 7.57925L7.97719 6.50376ZM7.15 9.08317V0.916504H5.65V9.08317H7.15Z"
      fill="var(--risk-primary)"
    />
  </svg>
);

interface IOrderStatusProps {
  setSortKey: (arg: string) => void;
  sortKey: string;
}

const sortOptions: Array<string> = [
  "Order Number Oldest",
  "Order Number Newest",
  "Est Closing Date Soonest",
  "Est Closing Date Furthest",
  "Client",
  "Deal Name",
  "Sponsor",
];

const sortFn = (a: string, b: string) =>
  a.toLocaleLowerCase().localeCompare(b.toLocaleLowerCase());

export default function Sort({ setSortKey, sortKey }: IOrderStatusProps) {
  const handleChange = (e: React.ChangeEvent<any>) => {
    e.persist();
    setSortKey(e.target.value);
  };
  return (
    <section className="d-flex align-items-center">
      <div className="d-flex align-items-center mx-2">
        <aside className="mx-2 d-flex align-self-center">{sortIcon}</aside>
        <span className={styles.sort}>Sort</span>
      </div>
      <select
        className={`text-dark border-0 outline-0 ${styles.select}`}
        onChange={(e) => handleChange(e)}
        value={sortKey}
      >
        {sortOptions.sort(sortFn).map((name) => (
          <option className="bg-white outline-0 border-0" value={name}>
            {name}
          </option>
        ))}
      </select>
    </section>
  );
}
