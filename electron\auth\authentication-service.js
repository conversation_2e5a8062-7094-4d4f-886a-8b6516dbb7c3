/**
 * Authentication Service
 * Main service that integrates dual-token system with existing authentication
 */

const axios = require('axios');
const DualTokenService = require('./dual-token-service');
const { ElectronNotifications } = require('../../src/utils/electronNotifications');

class AuthenticationService {
  constructor() {
    this.dualTokenService = new DualTokenService();
    this.isOnline = true;
    this.refreshInProgress = false;
    this.refreshPromise = null;

    // API configuration
    this.apiConfig = {
      baseURL: process.env.REACT_APP_BASEURL || 'http://localhost:3000',
      timeout: 10000
    };

    // Create axios instance for auth requests
    this.authAxios = axios.create(this.apiConfig);

    // Setup axios interceptors
    this.setupAxiosInterceptors();

    // Setup session warning handlers
    this.setupSessionWarnings();

    console.log('🔐 Authentication service initialized with session management');
  }

  /**
   * Setup axios interceptors for automatic token handling
   */
  setupAxiosInterceptors() {
    // Request interceptor - add token to requests
    this.authAxios.interceptors.request.use(
      (config) => {
        const token = this.dualTokenService.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor - handle token refresh
    this.authAxios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle 401/403 errors with token refresh
        if (
          (error.response?.status === 401 || error.response?.status === 403) &&
          !originalRequest._retry &&
          this.isOnline
        ) {
          originalRequest._retry = true;

          try {
            const refreshed = await this.refreshTokenIfNeeded();
            if (refreshed.success) {
              // Update authorization header and retry
              originalRequest.headers.Authorization = `Bearer ${refreshed.access_token}`;
              return this.authAxios(originalRequest);
            }
          } catch (refreshError) {
            console.error('Token refresh failed during request retry:', refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Setup session warning handlers
   */
  setupSessionWarnings() {
    this.dualTokenService.sessionManager.onSessionChange((type, data) => {
      this.handleSessionWarning(type, data);
    });
  }

  /**
   * Handle session warnings and notifications
   * @param {string} type - Type of session change
   * @param {Object} data - Session data
   */
  handleSessionWarning(type, data) {
    switch (type) {
      case 'session_expired':
        if (typeof ElectronNotifications !== 'undefined') {
          ElectronNotifications.error(
            'Session Expired',
            'Maximum offline period exceeded. Please log in again.'
          );
        }
        break;
      case 'grace_period':
        if (typeof ElectronNotifications !== 'undefined') {
          const remaining = this.formatDuration(data.gracePeriodRemaining);
          ElectronNotifications.warning(
            'Session Grace Period',
            `You have ${remaining} to reconnect before being logged out.`
          );
        }
        break;
      case 'critical_warning':
        if (typeof ElectronNotifications !== 'undefined') {
          const remaining = this.formatDuration(data.timeRemaining);
          ElectronNotifications.warning(
            'Session Expiring Soon',
            `Your offline session expires in ${remaining}. Please reconnect to avoid logout.`
          );
        }
        break;
      case 'warning':
        if (typeof ElectronNotifications !== 'undefined') {
          const remaining = this.formatDuration(data.timeRemaining);
          ElectronNotifications.info(
            'Session Warning',
            `Your offline session expires in ${remaining}. Consider reconnecting soon.`
          );
        }
        break;
    }
  }

  /**
   * Format duration for user display
   * @param {number} seconds - Duration in seconds
   * @returns {string} Formatted duration
   */
  formatDuration(seconds) {
    const days = Math.floor(seconds / (24 * 60 * 60));
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return 'less than an hour';
    }
  }

  /**
   * Update online status
   * @param {boolean} online - Whether the app is online
   */
  setOnlineStatus(online) {
    const wasOnline = this.isOnline;
    this.isOnline = online;
    this.dualTokenService.setOnlineStatus(online);

    if (!wasOnline && online) {
      this.handleOnlineReconnection();
    }
  }

  /**
   * Authenticate user with credentials
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.pass - User password
   * @returns {Object} Authentication result
   */
  async authenticateUser(credentials) {
    try {
      console.log('🔐 Attempting user authentication...');

      if (!this.isOnline) {
        throw new Error('Cannot authenticate while offline. Please connect to the internet.');
      }

      // Make login request
      const response = await this.authAxios.post('/login', {
        email: credentials.email,
        password: credentials.pass
      });

      if (response.status !== 200 || !response.data) {
        throw new Error('Invalid login response from server');
      }

      // Process login response with dual-token system
      const tokenResult = this.dualTokenService.processLoginResponse(response.data);

      if (!tokenResult.success) {
        throw new Error(tokenResult.error);
      }

      console.log('✅ User authenticated successfully');

      // Show success notification
      if (typeof ElectronNotifications !== 'undefined') {
        ElectronNotifications.success(
          'Login Successful',
          `Welcome back, ${tokenResult.user.username || tokenResult.user.email}!`
        );
      }

      return {
        success: true,
        user: tokenResult.user,
        tokens: tokenResult.tokens,
        expiresAt: tokenResult.expiresAt,
        offlineExpiresAt: tokenResult.offlineExpiresAt
      };
    } catch (error) {
      console.error('❌ Authentication failed:', error);

      // Show error notification
      if (typeof ElectronNotifications !== 'undefined') {
        ElectronNotifications.error(
          'Login Failed',
          error.response?.data?.message || error.message || 'Authentication failed'
        );
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Authentication failed'
      };
    }
  }

  /**
   * Check current authentication status
   * @returns {Object} Authentication status
   */
  getAuthenticationStatus() {
    return this.dualTokenService.validateAuthentication();
  }

  /**
   * Get current user information
   * @returns {Object|null} Current user or null
   */
  getCurrentUser() {
    return this.dualTokenService.getCurrentUser();
  }

  /**
   * Refresh token if needed
   * @returns {Object} Refresh result
   */
  async refreshTokenIfNeeded() {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshInProgress) {
      return this.refreshPromise;
    }

    const auth = this.getAuthenticationStatus();

    if (!auth.isAuthenticated) {
      return { success: false, error: 'Not authenticated' };
    }

    if (!auth.needsRefresh) {
      return { success: true, access_token: auth.tokens.access_token };
    }

    if (!this.isOnline) {
      return { success: false, error: 'Cannot refresh token while offline' };
    }

    this.refreshInProgress = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.refreshInProgress = false;
      this.refreshPromise = null;
    }
  }

  /**
   * Perform actual token refresh
   * @returns {Object} Refresh result
   */
  async performTokenRefresh() {
    try {
      console.log('🔄 Refreshing authentication token...');

      const refreshToken = this.dualTokenService.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      // Make refresh request
      const response = await this.authAxios.post('/value', {
        refreshToken: refreshToken
      });

      if (response.status !== 200 || !response.data) {
        throw new Error('Invalid refresh response from server');
      }

      // Handle refresh response
      const refreshResult = this.dualTokenService.handleTokenRefresh(response.data);

      if (!refreshResult.success) {
        throw new Error(refreshResult.error);
      }

      console.log('✅ Token refreshed successfully');

      return {
        success: true,
        access_token: refreshResult.access_token,
        expiresAt: refreshResult.expiresAt
      };
    } catch (error) {
      console.error('❌ Token refresh failed:', error);

      // If refresh fails, user needs to re-authenticate
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('🚪 Refresh token expired, requiring re-authentication');
        this.logout();

        if (typeof ElectronNotifications !== 'undefined') {
          ElectronNotifications.warning(
            'Session Expired',
            'Please log in again to continue.'
          );
        }
      }

      return {
        success: false,
        error: error.message,
        requiresLogin: error.response?.status === 401 || error.response?.status === 403
      };
    }
  }

  /**
   * Handle reconnection to online mode
   */
  async handleOnlineReconnection() {
    try {
      console.log('🌐 Handling online reconnection...');

      const reconnectionResult = await this.dualTokenService.handleOnlineReconnection();

      if (reconnectionResult.shouldRefresh) {
        const refreshResult = await this.refreshTokenIfNeeded();

        if (refreshResult.success) {
          if (typeof ElectronNotifications !== 'undefined') {
            ElectronNotifications.success(
              'Connection Restored',
              'Authentication refreshed successfully.'
            );
          }
        }
      }
    } catch (error) {
      console.error('❌ Online reconnection handling failed:', error);
    }
  }

  /**
   * Logout user and clear tokens
   * @returns {Object} Logout result
   */
  async logout() {
    try {
      console.log('👋 Logging out user...');

      // Clear tokens from dual-token service
      const cleared = this.dualTokenService.logout();

      if (cleared) {
        console.log('✅ User logged out successfully');

        if (typeof ElectronNotifications !== 'undefined') {
          ElectronNotifications.info(
            'Logged Out',
            'You have been logged out successfully.'
          );
        }

        return { success: true };
      } else {
        throw new Error('Failed to clear authentication tokens');
      }
    } catch (error) {
      console.error('❌ Logout failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if user has specific permission
   * @param {string} permission - Permission to check
   * @returns {boolean} Whether user has permission
   */
  hasPermission(permission) {
    return this.dualTokenService.hasPermission(permission);
  }

  /**
   * Check if user has specific role
   * @param {string} role - Role to check
   * @returns {boolean} Whether user has role
   */
  hasRole(role) {
    return this.dualTokenService.hasRole(role);
  }

  /**
   * Get session statistics
   * @returns {Object} Session statistics
   */
  getSessionStats() {
    return this.dualTokenService.getSessionStats();
  }

  /**
   * Get authentication statistics
   * @returns {Object} Authentication statistics
   */
  getAuthStats() {
    return this.dualTokenService.getAuthStats();
  }

  /**
   * Get token expiration information
   * @returns {Object|null} Expiration info or null
   */
  getExpirationInfo() {
    return this.dualTokenService.getExpirationInfo();
  }

  /**
   * Update authentication configuration
   * @param {Object} config - New configuration
   */
  updateConfig(config) {
    if (config.api) {
      this.apiConfig = { ...this.apiConfig, ...config.api };
      this.authAxios.defaults.baseURL = this.apiConfig.baseURL;
      this.authAxios.defaults.timeout = this.apiConfig.timeout;
    }

    if (config.tokens) {
      this.dualTokenService.updateConfig(config.tokens);
    }

    console.log('⚙️ Authentication configuration updated');
  }

  /**
   * Cleanup expired tokens
   * @returns {boolean} Whether cleanup was performed
   */
  cleanupExpiredTokens() {
    return this.dualTokenService.cleanupExpiredTokens();
  }

  /**
   * Get axios instance configured with authentication
   * @returns {Object} Configured axios instance
   */
  getAuthenticatedAxios() {
    return this.authAxios;
  }
}

module.exports = AuthenticationService;
