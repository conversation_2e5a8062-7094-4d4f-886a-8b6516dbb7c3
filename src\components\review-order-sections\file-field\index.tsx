import React from "react";
import { ErrorMessage } from "formik";
import styles from "./index.module.css";
import {
  AddUploadIcon,
  PdfIcon,
  UploadIcon,
  CancelImg,
} from "../../../exports";
import { showFile } from "../../../store/slices/showFileSlice";
import { setFile } from "../../../store/slices/fileHandlerSlice";
import { useAppDispatch } from "../../../store/storeConfig";

import { toast } from "react-toastify";

interface IFileFieldProps {
  label: string;
  name: string;
  type: string;
  placeholder?: string;
  formik: any;
}

function getBase64(file: any) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

const setNullFile = (formik: any, name: string) => {
  formik.setFieldValue(name, null);
};

const nameParser = (formik: any, name: string) => {
  if (
    Boolean(formik?.values?.[name]) === true &&
    formik?.values?.[name]?.includes("name")
  ) {
    let d: any = JSON?.parse(formik?.values?.[name]);
    return d?.name;
  }
};

const base64Parser = (formik: any, name: string) => {
  if (
    Boolean(formik?.values?.[name]) === true &&
    formik?.values?.[name]?.includes("base64")
  ) {
    let d: any = JSON.parse(formik?.values?.[name]);
    return d?.base64;
  }
};

const base64Creator = async (event: any, formik: any, name: string) => {
  event?.persist();
  let file = event.currentTarget?.files?.[0];

  if (checkSize(event, 1)) {
    try {
      const base64data = await getBase64(file);
      formik.setFieldValue(
        name,
        JSON.stringify({
          name: file?.name,
          base64: base64data,
        })
      );
    } catch (err) {
    }
  } else {
    toast.error("Sorry, upload a smaller file size.", {
      position: "bottom-center",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: false,
      draggable: true,
      progress: undefined,
    });
  }
};

const checkSize = (e: any, n: number) => {
  let size = Math.ceil(e?.target?.files[0]?.size / (1024 * 1024));
  if (size > n) return false;
  return true;
};

export default function FileField({
  label,
  name,
  type,
  placeholder,
  formik,
  ...rest
}: IFileFieldProps) {
  const dispatch = useAppDispatch();

  return (
    <div className="d-flex align-items-center-center my-2 flex-column">
      {formik?.values?.[name] && (
        <>
          <div className="d-flex" style={{ cursor: "pointer" }}>
            <div style={{ flex: 5 }} className="mr-2">
              <span className={styles.label}>{label}</span>
            </div>
            <div
              style={{ flex: 7, height: "5vh" }}
              className="d-flex align-items-start justify-content-center"
            >
              <div
                className={`${styles.field} position-relative d-flex align-items-center justify-content-center`}
                style={{ flex: 1, height: "5vh" }}
              >
                <div
                  className="position-absolute"
                  style={{ top: "-15px", right: "-5px" }}
                >
                  <div
                    style={{
                      width: "25px",
                      height: "25px",
                      borderRadius: "50%",
                      cursor: "pointer",
                      background: "#fff",
                    }}
                    className="d-flex align-items-center justify-content-center border border-muted"
                    onClick={() => setNullFile(formik, name)}
                  >
                    <img
                      src={CancelImg}
                      className="img "
                      alt="pdf icon"
                      width="10px"
                      height="10px"
                    />
                  </div>
                </div>
                <div
                  className=""
                  style={{ color: "var(--risk-primary)" }}
                  onClick={() => {
                    dispatch(showFile(true));
                    dispatch(
                      setFile({
                        data: base64Parser(formik, name),
                        filename: nameParser(formik, name),
                      })
                    );
                  }}
                >
                  <small
                    className={`d-flex align-self-center justify-content-center px-2 ${styles.attachmentName}`}
                  >
                    {nameParser(formik, name)}
                    {nameParser(formik, name)?.includes("pdf") && (
                      <img
                        src={PdfIcon}
                        className="img-fluid ml-1"
                        alt="pdf icon"
                      />
                    )}
                  </small>
                </div>
              </div>
              <label
                className="d-flex align-self-end"
                style={{ cursor: "pointer" }}
              >
                <input
                  type="file"
                  name={name}
                  placeholder={placeholder}
                  onChange={async (event: React.ChangeEvent<any>) => {
                    base64Creator(event, formik, name);
                  }}
                  className={`${styles.field} d-none py-2 px-1`}
                  accept=".png,.jpg,.jpeg,.pdf"
                  {...rest}
                />
                <img
                  src={UploadIcon}
                  className="img-fluid ml-3"
                  alt="add a file"
                />
              </label>
            </div>
          </div>
        </>
      )}

      {!formik?.values?.[name] && (
        <label
          className="d-flex align-items-center w-100"
          style={{ cursor: "pointer" }}
        >
          <div style={{ flex: 5 }} className="mr-2">
            <span className={styles.label}>{label}</span>
          </div>
          <div
            className={`${styles.field} d-flex align-items-center justify-content-center`}
            style={{ flex: 7, height: "5vh" }}
          >
            <aside>
              <img src={AddUploadIcon} className="img-fluid" alt="add a file" />
            </aside>
            <aside className="mx-2">
              <span className={styles.attachment}>Attach a document</span>
            </aside>
            <aside>
              <img src={UploadIcon} className="img-fluid" alt="add a file" />
            </aside>
            <input
              type="file"
              name={name}
              placeholder={placeholder}
              onChange={async (event: React.ChangeEvent<any>) => {
                base64Creator(event, formik, name);
              }}
              className={`${styles.field} d-none py-2 px-1`}
              accept=".png,.jpg,.jpeg,.pdf"
              {...rest}
            />
          </div>
        </label>
      )}

      <ErrorMessage name={name} />
    </div>
  );
}

// {
//   formik?.values?.[name] && nameParser(formik, name)?.includes("pdf") ? (
//     <>
//       <div
//         className="overflow-auto w-100"
//         style={{ height: "10vh", cursor: "pointer" }}
//       >
//         <iframe
//           src={`${base64Parser(formik, name)}#view=fitH`}
//           title={nameParser(formik, name)}
//           name={nameParser(formik, name)}
//           height="90%"
//           width="100%"
//           className="overflow-none"
//         />

//         <Filer filer={base64Parser(formik, name)} />
//       </div>
//       <div className="text-center">
//         <button
//           type="button"
//           className="btn btn-sm btn-primary py-0"
//           onClick={() => {
//             dispatch(showFile(true));
//             dispatch(
//               setFile({
//                 data: base64Parser(formik, name),
//                 filename: nameParser(formik, name),
//               })
//             );
//           }}
//         >
//           view document
//         </button>
//       </div>
//     </>
//   ) : formik?.values?.[name] &&
//     /\.(jpe?g|png)$/i.test(nameParser(formik, name)) ? (
//     <div className="d-flex align-items-end">
//       <div className="mr-3">
//         <img
//           src={base64Parser(formik, name)}
//           width="50px"
//           height="50px"
//           className="img-fluid"
//           alt="uploaded file"
//         />
//       </div>
//     </div>
//   ) : null;
// }
