export const groupBy = (fields: Array<any>, key: string, by: number) => {
  if (fields?.length <= 0) return [];

  const ar = [];

  for (const field of fields) {
    if (field[key] === by) {
      ar.push(field);
    }
  }

  return ar;
};

export const extractNameValue = (data: Array<any>) => {
  if (data?.length <= 0) return null;

  const obj = {};

  for (const { name, value } of data) {
    obj[name] = value || "";
  }

  return obj;
};

export const extractFieldValue = (data: Array<any>) => {
  if (data?.length <= 0) return null;

  const obj = {};

  for (const item of data) {
    if (Array.isArray(item.children) && item?.children.length > 0) {
      for (const subItem of item?.children) {
        const { name, value } = subItem;

        obj[name] = value || "";
      }
    } else {
      const { name, value } = item;
      obj[name] = value || "";
    }
  }

  return obj;
};
