import React from "react";
import { ErrorMessage, Field } from "formik";
import styles from "./index.module.css";

interface ISelectProps {
  label: string;
  name: string;
  type: string;
  options: any;
  relay?: boolean;
  formik: any;
}

export default function SelectField({
  label,
  name,
  type,
  options,
  relay,
  formik,
  ...rest
}: ISelectProps) {
  const setter = () => {
    if (name === "flood_zone") {
      if (Boolean(formik?.values?.["flood_zone"]?.includes("s"))) {
        formik.setFieldValue("flood_zone_specify", "");
      }
    }
  };

  return (
    <div className="d-flex align-items-center-center my-2">
      <div className="d-flex align-self-center mr-2" style={{ flex: 5 }}>
        <label className="">
          <span className={styles.label}>{label}</span>
        </label>
      </div>

      <div className="" style={{ flex: 7 }} onChange={setter}>
        <Field as="select" name={name} className={`${styles.field} py-2 px-1`}>
          <option value="" className="text-danger">
            __
          </option>
          {options.map((option: any) => (
            <React.Fragment key={option}>
              <option value={option} label={option} />
            </React.Fragment>
          ))}
        </Field>
      </div>

      <ErrorMessage name={name} />
    </div>
  );
}
