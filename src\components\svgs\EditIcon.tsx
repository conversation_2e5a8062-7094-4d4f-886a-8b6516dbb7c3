import React from "react";

const EditIconSvg = () => {
  return (
    <svg
      width="13"
      height="13"
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.28446 2.44472L10.5553 3.71494L9.28446 2.44472ZM10.1017 1.3257L6.66541 4.76196C6.48786 4.93926 6.36677 5.16516 6.31741 5.41117L6 7L7.58883 6.682C7.83483 6.63279 8.06044 6.51219 8.23804 6.33459L11.6743 2.89833C11.7776 2.79507 11.8595 2.67248 11.9154 2.53757C11.9712 2.40265 12 2.25805 12 2.11202C12 1.96598 11.9712 1.82138 11.9154 1.68647C11.8595 1.55155 11.7776 1.42896 11.6743 1.3257C11.571 1.22244 11.4485 1.14053 11.3135 1.08465C11.1786 1.02876 11.034 1 10.888 1C10.742 1 10.5974 1.02876 10.4624 1.08465C10.3275 1.14053 10.2049 1.22244 10.1017 1.3257V1.3257Z"
        stroke="var(--risk-primary)"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 9V10.8C10 11.1183 9.87357 11.4235 9.64853 11.6485C9.42348 11.8736 9.11826 12 8.8 12H2.2C1.88174 12 1.57652 11.8736 1.35147 11.6485C1.12643 11.4235 1 11.1183 1 10.8V4.2C1 3.88174 1.12643 3.57652 1.35147 3.35147C1.57652 3.12643 1.88174 3 2.2 3H4"
        stroke="var(--risk-primary)"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default EditIconSvg;
