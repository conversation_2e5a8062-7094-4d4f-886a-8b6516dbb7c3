/**
 * Secure Token Storage Manager
 * Handles encrypted storage of authentication tokens using electron-store
 */

const Store = require('electron-store');
const crypto = require('crypto');
const { app } = require('electron');

class SecureTokenStorage {
  constructor() {
    // Generate or retrieve encryption key
    this.encryptionKey = this.getOrCreateEncryptionKey();
    
    // Initialize encrypted store
    this.store = new Store({
      name: 'secure-auth-tokens',
      encryptionKey: this.encryptionKey,
      cwd: app.getPath('userData'),
      fileExtension: 'dat', // Use .dat extension for security
      clearInvalidConfig: true
    });

    // Initialize metadata store (non-encrypted for basic info)
    this.metaStore = new Store({
      name: 'auth-metadata',
      cwd: app.getPath('userData')
    });

    console.log('🔐 Secure token storage initialized');
  }

  /**
   * Generate or retrieve encryption key
   * @returns {string} Encryption key
   */
  getOrCreateEncryptionKey() {
    const keyStore = new Store({
      name: 'encryption-key',
      cwd: app.getPath('userData')
    });

    let key = keyStore.get('authKey');
    
    if (!key) {
      // Generate new 32-byte encryption key
      key = crypto.randomBytes(32).toString('hex');
      keyStore.set('authKey', key);
      console.log('🔑 Generated new encryption key for token storage');
    }

    return key;
  }

  /**
   * Store authentication tokens with dual expiration
   * @param {Object} tokenData - Token data object
   * @param {string} tokenData.access_token - Access token
   * @param {string} tokenData.refresh_token - Refresh token
   * @param {number} tokenData.expires_in - Standard expiration in seconds
   * @param {number} tokenData.offline_expires_in - Offline expiration in seconds
   * @param {Object} tokenData.user - User information
   */
  storeTokens(tokenData) {
    try {
      const currentTime = Math.floor(Date.now() / 1000);
      
      const tokenRecord = {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        expires_at: currentTime + (tokenData.expires_in || 3600), // Default 1 hour
        offline_expires_at: currentTime + (tokenData.offline_expires_in || 2592000), // Default 30 days
        stored_at: currentTime,
        user: tokenData.user || {},
        token_version: 1 // For future migration support
      };

      // Store encrypted tokens
      this.store.set('auth.tokens', tokenRecord);

      // Store non-sensitive metadata
      this.metaStore.set('auth.metadata', {
        user_id: tokenData.user?.id,
        username: tokenData.user?.username || tokenData.user?.email,
        last_login: currentTime,
        token_stored: true
      });

      console.log('✅ Tokens stored securely with dual expiration');
      return true;
    } catch (error) {
      console.error('❌ Failed to store tokens:', error);
      return false;
    }
  }

  /**
   * Retrieve stored tokens
   * @returns {Object|null} Token data or null if not found
   */
  getTokens() {
    try {
      const tokens = this.store.get('auth.tokens');
      if (!tokens) {
        return null;
      }

      return tokens;
    } catch (error) {
      console.error('❌ Failed to retrieve tokens:', error);
      return null;
    }
  }

  /**
   * Validate token expiration based on online/offline status
   * @param {boolean} isOnline - Whether the app is online
   * @returns {Object} Validation result
   */
  validateTokens(isOnline = true) {
    try {
      const tokens = this.getTokens();
      if (!tokens) {
        return { isValid: false, reason: 'no_tokens' };
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const expirationTime = isOnline ? tokens.expires_at : tokens.offline_expires_at;

      if (currentTime > expirationTime) {
        return { 
          isValid: false, 
          reason: isOnline ? 'online_expired' : 'offline_expired',
          expired_at: expirationTime
        };
      }

      return { 
        isValid: true, 
        tokens,
        expires_in: expirationTime - currentTime,
        mode: isOnline ? 'online' : 'offline'
      };
    } catch (error) {
      console.error('❌ Failed to validate tokens:', error);
      return { isValid: false, reason: 'validation_error', error };
    }
  }

  /**
   * Update token expiration times (for refresh scenarios)
   * @param {Object} newTokenData - New token data
   */
  updateTokens(newTokenData) {
    try {
      const existingTokens = this.getTokens();
      if (!existingTokens) {
        return this.storeTokens(newTokenData);
      }

      const currentTime = Math.floor(Date.now() / 1000);
      
      const updatedTokens = {
        ...existingTokens,
        access_token: newTokenData.access_token || existingTokens.access_token,
        refresh_token: newTokenData.refresh_token || existingTokens.refresh_token,
        expires_at: currentTime + (newTokenData.expires_in || 3600),
        offline_expires_at: newTokenData.offline_expires_in 
          ? currentTime + newTokenData.offline_expires_in 
          : existingTokens.offline_expires_at,
        updated_at: currentTime
      };

      this.store.set('auth.tokens', updatedTokens);
      
      // Update metadata
      this.metaStore.set('auth.metadata.last_refresh', currentTime);

      console.log('✅ Tokens updated successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to update tokens:', error);
      return false;
    }
  }

  /**
   * Clear all stored tokens
   */
  clearTokens() {
    try {
      this.store.delete('auth.tokens');
      this.metaStore.delete('auth.metadata');
      console.log('🗑️ All tokens cleared');
      return true;
    } catch (error) {
      console.error('❌ Failed to clear tokens:', error);
      return false;
    }
  }

  /**
   * Get authentication metadata (non-sensitive)
   * @returns {Object|null} Metadata or null
   */
  getMetadata() {
    try {
      return this.metaStore.get('auth.metadata');
    } catch (error) {
      console.error('❌ Failed to get metadata:', error);
      return null;
    }
  }

  /**
   * Check if tokens exist
   * @returns {boolean} Whether tokens exist
   */
  hasTokens() {
    try {
      return this.store.has('auth.tokens');
    } catch (error) {
      console.error('❌ Failed to check token existence:', error);
      return false;
    }
  }

  /**
   * Get token storage statistics
   * @returns {Object} Storage statistics
   */
  getStorageStats() {
    try {
      const tokens = this.getTokens();
      const metadata = this.getMetadata();
      
      if (!tokens) {
        return { hasTokens: false };
      }

      const currentTime = Math.floor(Date.now() / 1000);
      
      return {
        hasTokens: true,
        storedAt: tokens.stored_at,
        updatedAt: tokens.updated_at,
        onlineExpiresIn: Math.max(0, tokens.expires_at - currentTime),
        offlineExpiresIn: Math.max(0, tokens.offline_expires_at - currentTime),
        user: {
          id: metadata?.user_id,
          username: metadata?.username
        },
        lastLogin: metadata?.last_login,
        lastRefresh: metadata?.last_refresh
      };
    } catch (error) {
      console.error('❌ Failed to get storage stats:', error);
      return { hasTokens: false, error: error.message };
    }
  }

  /**
   * Cleanup expired tokens
   * @returns {boolean} Whether cleanup was performed
   */
  cleanupExpiredTokens() {
    try {
      const validation = this.validateTokens(false); // Check offline expiration
      
      if (!validation.isValid && validation.reason === 'offline_expired') {
        this.clearTokens();
        console.log('🧹 Cleaned up expired tokens');
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('❌ Failed to cleanup tokens:', error);
      return false;
    }
  }

  /**
   * Export tokens for backup (encrypted)
   * @returns {string|null} Encrypted token backup
   */
  exportTokensBackup() {
    try {
      const tokens = this.getTokens();
      if (!tokens) {
        return null;
      }

      // Create backup with timestamp
      const backup = {
        tokens,
        exported_at: Math.floor(Date.now() / 1000),
        version: 1
      };

      // Encrypt backup
      const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
      let encrypted = cipher.update(JSON.stringify(backup), 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return encrypted;
    } catch (error) {
      console.error('❌ Failed to export tokens:', error);
      return null;
    }
  }

  /**
   * Import tokens from backup
   * @param {string} encryptedBackup - Encrypted backup string
   * @returns {boolean} Success status
   */
  importTokensBackup(encryptedBackup) {
    try {
      // Decrypt backup
      const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
      let decrypted = decipher.update(encryptedBackup, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      const backup = JSON.parse(decrypted);
      
      if (backup.version === 1 && backup.tokens) {
        this.store.set('auth.tokens', backup.tokens);
        console.log('📥 Tokens imported from backup');
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Failed to import tokens:', error);
      return false;
    }
  }
}

module.exports = SecureTokenStorage;
