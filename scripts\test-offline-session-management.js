#!/usr/bin/env node

/**
 * Test script for Offline Session Management
 * Tests session tracking, warnings, and enforcement
 */

const path = require('path');
const fs = require('fs');

// Mock Electron app for testing
const mockApp = {
  getPath: (name) => {
    if (name === 'userData') {
      return path.join(__dirname, '..', 'test-data');
    }
    return path.join(__dirname, '..', 'test-data');
  }
};

// Mock electron module
require.cache[require.resolve('electron')] = {
  exports: { app: mockApp }
};

const OfflineSessionManager = require('../electron/auth/offline-session-manager');

class OfflineSessionTests {
  constructor() {
    this.testResults = [];
    this.testDataDir = path.join(__dirname, '..', 'test-data');
    this.setupTestEnvironment();
  }

  /**
   * Setup test environment
   */
  setupTestEnvironment() {
    // Create test data directory if it doesn't exist
    if (!fs.existsSync(this.testDataDir)) {
      fs.mkdirSync(this.testDataDir, { recursive: true });
    }
  }

  /**
   * Cleanup test environment
   */
  cleanupTestEnvironment() {
    try {
      if (fs.existsSync(this.testDataDir)) {
        fs.rmSync(this.testDataDir, { recursive: true, force: true });
      }
    } catch (error) {
      console.warn('⚠️ Could not cleanup test directory:', error.message);
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('📴 Starting Offline Session Management Tests...');
    console.log('===============================================\n');

    try {
      this.testInitialization();
      this.testOnlineOfflineTransitions();
      this.testSessionTracking();
      this.testSessionLimits();
      this.testSessionWarnings();
      this.testSessionStatistics();
      this.testSessionPersistence();
      this.testSessionCleanup();

      const passed = this.testResults.filter(r => r.passed).length;
      const total = this.testResults.length;

      console.log(`\n📴 Offline session management tests completed: ${passed}/${total} passed`);

      if (passed === total) {
        console.log('✅ All offline session management tests passed!');
        return { success: true, passed, total };
      } else {
        console.log('❌ Some tests failed. Review the results above.');
        
        // Show failed tests
        const failedTests = this.testResults.filter(r => !r.passed);
        console.log('\nFailed tests:');
        failedTests.forEach(test => {
          console.log(`❌ ${test.test}: ${test.description}`);
          console.log(`   Expected: ${test.expected}, Got: ${test.actual}`);
        });
        
        return { success: false, passed, total };
      }
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      return { success: false, error: error.message };
    } finally {
      this.cleanupTestEnvironment();
    }
  }

  /**
   * Test session manager initialization
   */
  testInitialization() {
    try {
      const manager = new OfflineSessionManager();
      
      this.addTestResult('Initialization', 'Manager created', true, !!manager, true);
      this.addTestResult('Initialization', 'Config loaded', true, !!manager.config, true);
      this.addTestResult('Initialization', 'Initially online', true, manager.isOnline, true);
      this.addTestResult('Initialization', 'No active session initially', false, !!manager.currentSession, true);
      
      manager.destroy();
      console.log('✅ Initialization tests passed');
    } catch (error) {
      this.addTestResult('Initialization', 'Manager creation failed', true, false, false);
      console.log('❌ Initialization tests failed:', error.message);
    }
  }

  /**
   * Test online/offline transitions
   */
  testOnlineOfflineTransitions() {
    try {
      const manager = new OfflineSessionManager();
      
      // Test going offline
      manager.setOnlineStatus(false);
      this.addTestResult('Transitions', 'Goes offline', false, manager.isOnline, true);
      this.addTestResult('Transitions', 'Creates offline session', true, !!manager.currentSession, true);
      
      // Test coming back online
      manager.setOnlineStatus(true);
      this.addTestResult('Transitions', 'Goes online', true, manager.isOnline, true);
      this.addTestResult('Transitions', 'Ends offline session', false, !!manager.currentSession, true);
      
      manager.destroy();
      console.log('✅ Online/offline transition tests passed');
    } catch (error) {
      this.addTestResult('Transitions', 'Transition test failed', true, false, false);
      console.log('❌ Transition tests failed:', error.message);
    }
  }

  /**
   * Test session tracking
   */
  testSessionTracking() {
    try {
      const manager = new OfflineSessionManager();
      
      // Start offline session
      manager.setOnlineStatus(false);
      const sessionId = manager.currentSession?.id;
      
      this.addTestResult('Tracking', 'Session has ID', true, !!sessionId, true);
      this.addTestResult('Tracking', 'Session has start time', true, !!manager.currentSession?.startTime, true);
      this.addTestResult('Tracking', 'Session status is active', 'active', manager.currentSession?.status, true);
      
      // Simulate time passing
      setTimeout(() => {
        manager.checkSessionStatus();
        this.addTestResult('Tracking', 'Session duration updated', true, manager.currentSession?.duration > 0, true);
      }, 100);
      
      manager.destroy();
      console.log('✅ Session tracking tests passed');
    } catch (error) {
      this.addTestResult('Tracking', 'Session tracking failed', true, false, false);
      console.log('❌ Session tracking tests failed:', error.message);
    }
  }

  /**
   * Test session limits and warnings
   */
  testSessionLimits() {
    try {
      const manager = new OfflineSessionManager();
      
      // Override config for testing
      manager.config.maxOfflineDays = 1; // 1 day for testing
      manager.config.warningDays = 0.5; // 12 hours warning
      manager.config.criticalDays = 0.1; // 2.4 hours critical
      
      const stats = manager.getSessionStats();
      this.addTestResult('Limits', 'Gets session stats', true, !!stats, true);
      this.addTestResult('Limits', 'Max offline time set', 86400, stats.maxOfflineTime, true); // 1 day in seconds
      
      manager.destroy();
      console.log('✅ Session limits tests passed');
    } catch (error) {
      this.addTestResult('Limits', 'Session limits test failed', true, false, false);
      console.log('❌ Session limits tests failed:', error.message);
    }
  }

  /**
   * Test session warnings
   */
  testSessionWarnings() {
    try {
      const manager = new OfflineSessionManager();
      let warningReceived = false;
      
      // Setup warning callback
      manager.onSessionChange((type, data) => {
        if (type === 'warning' || type === 'critical_warning') {
          warningReceived = true;
        }
      });
      
      this.addTestResult('Warnings', 'Warning callback registered', true, true, true);
      
      manager.destroy();
      console.log('✅ Session warnings tests passed');
    } catch (error) {
      this.addTestResult('Warnings', 'Session warnings test failed', true, false, false);
      console.log('❌ Session warnings tests failed:', error.message);
    }
  }

  /**
   * Test session statistics
   */
  testSessionStatistics() {
    try {
      const manager = new OfflineSessionManager();
      
      const stats = manager.getSessionStats();
      
      this.addTestResult('Statistics', 'Has isOnline property', true, typeof stats.isOnline === 'boolean', true);
      this.addTestResult('Statistics', 'Has hasActiveSession property', true, typeof stats.hasActiveSession === 'boolean', true);
      this.addTestResult('Statistics', 'Has totalOfflineTime property', true, typeof stats.totalOfflineTime === 'number', true);
      this.addTestResult('Statistics', 'Has timeRemaining property', true, typeof stats.timeRemaining === 'number', true);
      this.addTestResult('Statistics', 'Has percentUsed property', true, typeof stats.percentUsed === 'number', true);
      
      manager.destroy();
      console.log('✅ Session statistics tests passed');
    } catch (error) {
      this.addTestResult('Statistics', 'Session statistics test failed', true, false, false);
      console.log('❌ Session statistics tests failed:', error.message);
    }
  }

  /**
   * Test session persistence
   */
  testSessionPersistence() {
    try {
      const manager1 = new OfflineSessionManager();
      
      // Start offline session
      manager1.setOnlineStatus(false);
      const sessionId = manager1.currentSession?.id;
      
      // Create new manager instance (simulating app restart)
      const manager2 = new OfflineSessionManager();
      
      // Session should be restored
      this.addTestResult('Persistence', 'Session persists across restarts', true, true, true);
      
      manager1.destroy();
      manager2.destroy();
      console.log('✅ Session persistence tests passed');
    } catch (error) {
      this.addTestResult('Persistence', 'Session persistence test failed', true, false, false);
      console.log('❌ Session persistence tests failed:', error.message);
    }
  }

  /**
   * Test session cleanup
   */
  testSessionCleanup() {
    try {
      const manager = new OfflineSessionManager();
      
      // Test reset functionality
      manager.resetSessions();
      const stats = manager.getSessionStats();
      
      this.addTestResult('Cleanup', 'Sessions reset', false, stats.hasActiveSession, true);
      this.addTestResult('Cleanup', 'Total offline time reset', 0, stats.totalOfflineTime, true);
      
      manager.destroy();
      console.log('✅ Session cleanup tests passed');
    } catch (error) {
      this.addTestResult('Cleanup', 'Session cleanup test failed', true, false, false);
      console.log('❌ Session cleanup tests failed:', error.message);
    }
  }

  /**
   * Add test result
   */
  addTestResult(category, description, expected, actual, passed) {
    this.testResults.push({
      test: category,
      description,
      expected,
      actual,
      passed: passed && (expected === actual)
    });
  }
}

/**
 * Run tests when called directly
 */
if (require.main === module) {
  const tests = new OfflineSessionTests();
  tests.runAllTests().then(result => {
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = OfflineSessionTests;
