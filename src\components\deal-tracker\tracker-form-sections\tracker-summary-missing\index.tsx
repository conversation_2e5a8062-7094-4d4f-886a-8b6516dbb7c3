import React from "react";

import TrackerFormWrapper from "../../../resuable/tracker-form-wrapper";
import MapTrackerForm from "../../map-tracker";

import { groupBy } from "../../../../utils/groupBy";

import MissingInfo from "./../../../resuable/missing-info/";

interface ITrackerPropertySummary {
  formik?: any;
  data?: any;
}

const TrackerSummaryMissingFields = ({
  formik,
  data,
}: ITrackerPropertySummary) => {
  const totalSection = data?.section_count;

  const isFieldEmpty = data?.missing_field_count === 0;

  return (
    <>
      {isFieldEmpty && <MissingInfo />}
      {Array(totalSection)
        .fill(0)
        .map((_, i) => {
          const number = ++i;

          const currentGroup = groupBy(data?.fields, "section", number).filter(
            (v) => {
              const childrenItems = v?.children;

              if (childrenItems?.length > 0) {
                return childrenItems?.some((v: any) => !v.value);
              }
              return !v.value;
            }
          );

          return (
            <React.Fragment key={i}>
              {currentGroup.length > 0 && (
                <TrackerFormWrapper withMargin={number === 1 ? false : true}>
                  <MapTrackerForm
                    {...{
                      data: currentGroup,
                      formik,
                    }}
                  />
                </TrackerFormWrapper>
              )}
            </React.Fragment>
          );
        })}
    </>
  );
};

export default TrackerSummaryMissingFields;
