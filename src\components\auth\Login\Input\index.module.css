.inputBorder {
  background-color: #ffffff;
  border: 1px solid #a1aaa6;
  border-radius: 20px;
  min-width: 368px;
}

.inputBorder:focus {
  border: 1px solid rgb(16, 56, 55);
  /*background-color: #a1aaa6;*/
  /*box-shadow: 5px 3px 5px #a1aaa6;*/
  /*outline: 0;*/
}

.inputBorderError {
  background-color: #f1f3f2;
  border: 1px solid #db4655;
  border-radius: 20px;
  min-width: 368px;
}

.stylePassword {
  background-color: #ffffff;
  outline: 0;
  border: 0;
  border-radius: 20px;
}

.stylePassword:focus {
  border: 1px solid rgb(16, 56, 55);
}

.stylePassword:focus {
  outline: 0;
  border: 0;
}

.label {
  color: #181818;
  font-size: 0.97em;
  font-weight: 500;
}

@media screen and (max-width: 375px) {
  .inputBorder {
    min-width: 95vw;
  }
}
