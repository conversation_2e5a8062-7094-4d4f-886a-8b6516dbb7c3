import React from "react";
import <PERSON>Header from "../section-header";
import { IDynamicForm } from "../../../models/dynamicForm";
import TextField from "../text-field";
import SelectField from "../select-field/index";
import FileField from "./../file-field";
import MultiSelectField from "../multi-select-field";
import LinearTextField from "../linear-text-field";

interface IReusableSummaryProps {
  formik?: any;
  title: string;
  data: IDynamicForm[];
}

const ReusableSummary = React.forwardRef(
  (sectionInfo: IReusableSummaryProps, ref: any) => {
    return (
      <>
        <SectionHeader title={sectionInfo.title} />
        <div className="row mt-3 mt-md-3 mt-lg-3 mt-xl-5">
          {sectionInfo?.data?.map((data: IDynamicForm) => {
            if (data?.display === "linear") {
              return (
                <section key={data.label} className={`col-12`}>
                  <LinearTextField {...{ data }} />
                </section>
              );
            }
            if (
              data?.type === "text" ||
              data?.type === "number" ||
              data?.type === "email" ||
              data?.type === "date" ||
              data?.type === "tel"
            ) {
              return (
                <section
                  key={data.label}
                  className={`${
                    Boolean(data.occupy)
                      ? "col-12"
                      : "col-12 col-sm-12 col-md-12 col-lg-12 col-xl-6"
                  }`}
                >
                  <TextField
                    {...{
                      name: data.name,
                      label: data.label,
                      type: data.type,
                      placeholder: data.placeholder,
                      occupy: data.occupy,
                      formik: sectionInfo?.formik,
                    }}
                  />
                </section>
              );
            }
            if (data.type === "multiSelect") {
              return (
                <section
                  key={data.label}
                  className={`${
                    Boolean(data.occupy)
                      ? "col-12"
                      : "col-12 col-sm-12 col-md-12 col-lg-12 col-xl-6"
                  }`}
                >
                  <MultiSelectField
                    {...{
                      name: data.name,
                      label: data.label,
                      type: data.type,
                      options: data?.options,
                      formik: sectionInfo?.formik,
                    }}
                  />
                </section>
              );
            }

            if (data.type === "select") {
              return (
                <section
                  key={data.label}
                  className={`${
                    Boolean(data.occupy)
                      ? "col-12"
                      : "col-12 col-sm-12 col-md-12 col-lg-12 col-xl-6"
                  }`}
                >
                  <SelectField
                    {...{
                      name: data.name,
                      label: data.label,
                      type: data.type,
                      options: data?.options,
                      formik: sectionInfo?.formik,
                    }}
                  />
                </section>
              );
            }

            if (data.type === "file") {
              return (
                <section
                  key={data.label}
                  className={`${
                    Boolean(data.occupy)
                      ? "col-12"
                      : "col-12 col-sm-12 col-md-12 col-lg-12 col-xl-6"
                  }`}
                >
                  <FileField
                    {...{
                      name: data.name,
                      label: data.label,
                      type: data.type,
                      formik: sectionInfo.formik,
                    }}
                  />
                </section>
              );
            }
            return null;
          })}
        </div>
      </>
    );
  }
);

export default ReusableSummary;
