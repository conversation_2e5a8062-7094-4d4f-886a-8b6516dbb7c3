import React from "react";

import styles from "./index.module.scss";

import TrackerFormWrapper from "../tracker-form-wrapper";
import CommentSvg from "../../svgs/Comment";
import CustomModal from "../modal";
import CancelSvg from "../../svgs/CancelSvg";
//import ArchiveSvg from "../../svgs/Archive";
import TrackerAddSvg from "../../svgs/TrackerAdd";
import DeleteSvg from "../../svgs/Delete";
import {templateModel} from "../../../data/waiver-template";
import {generateWaiverModelFromLeanData} from "../../../utils/generateWaiver";

type WaiverInputComponentsProps = {
  formik?: any;
  data?: any;
};


export const WaiverInputComponents=({
  formik,
  data,
}: WaiverInputComponentsProps) => {
  const [textAreaId,setTextAreaId]=React.useState(null);


  const handleChange=
    (id: number) =>
      (e: React.ChangeEvent<HTMLInputElement|HTMLTextAreaElement>) => {
        const {name,value}=e.target;

        const newValue=formik.values.waivers.map((waiver: any) => {
          if(waiver.id!==id) return waiver;
          return {...waiver,[name]: value};
        });

        formik.setFieldValue("waivers",newValue);
      };

  const [waivers,setWaiver]=React.useState(
    generateWaiverModelFromLeanData(data.waivers)
  );

  React.useEffect(() => {
    setWaiver(generateWaiverModelFromLeanData(data.waivers));

    //eslint-disable-next-line
  },[data?.waivers?.length]);

  const [show,setShow]=React.useState(false);

  const handleAddNewWaiver=() => {
    const t=Date.now();

    const emptyValue={
      id: t,
      archive: false,
      type: null,
      waiver_exception: null,
      status: null,
      comment: null,
    };

    const existingWaiverValues: any=formik.values.waivers;

    formik.setFieldValue("waivers",[
      ...(existingWaiverValues||[]),
      emptyValue,
    ]);

    setWaiver((w) => [...w,{...templateModel,id: t,__isNew__: true}]);
  };

  const handleRemoveWaiver=(id: number, action: "DEL" = "DEL") => {
    const newValue=formik.values.waivers.filter(
      (waiver: any) => waiver.id!==id
    );

    formik.setFieldValue("waivers",newValue);

    setWaiver((w) => w.filter((w) => w.id!==id));

    setShowDeleteModal(false);

    action==="DEL"&&formik?.submitForm();
  };

  //const handleToggleArchive = (id: number) => {
  //  const newValue = formik.values.waivers.map((waiver: any) => {
  //    if (waiver.id !== id) return waiver;

  //    return { ...waiver, archive: !waiver.archive };
  //  });

  //  formik.setFieldValue("waivers", newValue);
  //};

  const handleStatusSelection=(option: string) => (id: number) => {
    const newValue=formik.values.waivers.map((waiver: any) => {
      if(waiver.id!==id) return waiver;

      return {...waiver,status: waiver.status===option? null:option};
    });

    formik.setFieldValue("waivers",newValue);
  };

  const handleTypeSelection=(option: string) => (id: number) => {
    const newValue=formik.values.waivers.map((waiver: any) => {
      if(waiver.id!==id) return waiver;

      if(!waiver.type||waiver.type?.length<=0) {
        return {...waiver,type: [option]};
      }

      if(waiver.type?.includes(option)) {
        return {
          ...waiver,
          type: waiver.type.filter((s: string) => s!==option),
        };
      }

      return {...waiver,type: [...waiver.type,option]};
    });

    formik.setFieldValue("waivers",newValue);
  };

  const handleRef = React.useCallback(
    (node: any) => {
      const isNewWaiversEntry = (waivers || []).some(
        (waiver: { [key: string]: any }) => waiver.__isNew__
      );

      !!node &&
        isNewWaiversEntry &&
        waivers?.length > 0 &&
        node.scrollIntoView({ behavior: "smooth", block: "end" });
    },
    //eslint-disable-next-line
    [waivers?.length]
  );

  const [showDeleteModal, setShowDeleteModal] = React.useState(false);

  const [deleteID, setDeleteID] = React.useState(null);
  return (
    <>
      <CustomModal
        {...{
          title: "Delete",
          show: showDeleteModal,
          setShow: setShowDeleteModal,
          columnLayout: "col-12 col-md-8 col-lg-7 col-xl-4",
          align: "align-items-center",
        }}
      >
        <div className="text-center my-3">
          <div className={styles.delete}>
            Do you want to delete this waiver?
          </div>

          <div className="mt-3">
            <button
              className={`${styles.buttonBorder} mr-2 py-2`}
              onClick={() => setShowDeleteModal(false)}
              type="button"
            >
              No
            </button>

            <button
              type="button"
              onClick={() => handleRemoveWaiver(deleteID, "DEL")}
              className={`${styles.button} py-2`}
            >
              Yes
            </button>
          </div>
        </div>
      </CustomModal>
      <div>
        {(waivers || []).map(
          (
            { id, archive, waiver_exception, status, type, comment, ...rest },
            i
          ) => {
            const eachValue = formik.values.waivers.find(
              (waiver: any) => waiver.id === id
            );

            const isNew = !!(rest as { [key: string]: boolean | string })
              ?.__isNew__;

            return (
              <React.Fragment key={i}>
                {textAreaId === id && (
                  <CustomModal
                    {...{
                      title: "Add Exception Comment",
                      show,
                      setShow,
                      columnLayout: "col-12 col-md-8 col-lg-7 col-xl-4",
                      align: "align-items-start pt-5",
                    }}
                  >
                    <div
                      className={`${styles.textareaWrapper} text-center py-3 px-3`}
                    >
                      <textarea
                        name={comment.name}
                        value={eachValue?.[comment.name]}
                        onChange={handleChange(id)}
                        rows={9}
                        placeholder="Enter Comment"
                        className={`${styles.textarea}`}
                      />

                      <div className="mt-3">
                        <button
                          type="button"
                          className={`${styles.buttonBorder} mr-2 py-2`}
                          onClick={() => {
                            setShow(false);

                            setTextAreaId(null);
                          }}
                        >
                          Cancel
                        </button>

                        <button
                          type="button"
                          className={`${styles.button} py-2`}
                          onClick={() => {
                            setShow(false);
                            setTextAreaId(null);
                          }}
                        >
                          Done
                        </button>
                      </div>
                    </div>
                  </CustomModal>
                )}

                {isNew && (
                  <div className="d-flex justify-content-end w-100 bg-light mt-2">
                    <button
                      type="button"
                      className={`${styles.btnCancel} py-1 px-2`}
                      onClick={() => handleRemoveWaiver(id)}
                    >
                      <CancelSvg />
                    </button>
                  </div>
                )}

                <TrackerFormWrapper
                  //withOpacity={eachValue?.["archive"]}
                  withOpacity={false}
                  withMargin={!isNew}
                  paddingBottom={"pb-2"}
                >
                  <section className="container-fluid">
                    <div className="row mt-1">
                      <div
                        className={`col-3 d-flex align-items-center justify-content-end mr-0 pr-0`}
                      >
                        <label className={styles.label}>
                          {waiver_exception.label}
                        </label>
                      </div>

                      <div className="col-9 pl-2">
                        <div className="d-flex align-items-center w-100">
                          <div className="flex-fill">
                            <input
                              name={waiver_exception.name}
                              value={eachValue?.[waiver_exception.name]}
                              type={waiver_exception.value}
                              onChange={handleChange(id)}
                              className={`${styles.field} px-2 py-1`}
                            />
                          </div>
                          <div
                            className={` ${styles.commentIcon} px-2 ml-2`}
                            onClick={() => {
                              setShow(true);
                              setTextAreaId(id);
                            }}
                          >
                            <CommentSvg
                              color={
                                eachValue?.[comment.name]
                                  ? "rgba(213, 156, 0, 1)"
                                  : "#181818"
                              }
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="row mt-2">
                      <div
                        className={`col-3 d-flex align-items-center justify-content-end mr-0 pr-0`}
                      >
                        <div className={styles.label}>{type.label}</div>
                      </div>

                      <div className="col-9 pl-2">
                        {(type.options || []).map(
                          (option: string, i: number) => (
                            <button
                              type="button"
                              key={i}
                              onClick={() => handleTypeSelection(option)(id)}
                              className={`px-3 py-1 ${styles.buttons} ${
                                (eachValue[type.name] || []).includes(option)
                                  ? styles.picked
                                  : ""
                              } ${status.options?.length > 4 && "mb-2"} ${
                                i > 0 ? "ml-2" : ""
                              }
                        `}
                            >
                              {option}
                            </button>
                          )
                        )}
                      </div>
                    </div>

                    <div className="row mt-2">
                      <div
                        className={`col-3 d-flex align-items-center justify-content-end mr-0 pr-0`}
                      >
                        <div className={styles.label}>{status.label}</div>
                      </div>

                      <div className="col-9 pl-2">
                        {(status.options || []).map(
                          (option: string, i: number) => (
                            <button
                              type="button"
                              key={i}
                              onClick={() => handleStatusSelection(option)(id)}
                              className={`px-3 py-1 ${styles.buttons} ${
                                eachValue[status.name] === option
                                  ? styles.picked
                                  : ""
                              } ${status.options?.length > 4 && "mb-2"} ${
                                i > 0 ? "ml-2" : ""
                              }
                        `}
                            >
                              {option}
                            </button>
                          )
                        )}
                      </div>
                    </div>
                  </section>
                </TrackerFormWrapper>

                <div className="d-flex justify-content-end w-100 bg-light">
                  {/*<button
                  type="button"
                  className={`${styles.btnArchive} py-1 px-2`}
                  onClick={() => handleToggleArchive(id)}
                >
                  <span className="mr-2 ">
                    {eachValue?.["archive"] ? "Unarchive" : "Archive"}
                  </span>

                  <ArchiveSvg />
                </button>*/}

                  {!isNew && (
                    <button
                      type="button"
                      className={`${styles.btnArchive} py-1 px-2`}
                      onClick={() => {
                        setDeleteID(id);
                        setShowDeleteModal(true);
                      }}
                    >
                      <span className={`mr-2 ${styles.deleteText}`}>Delete</span>

                      <DeleteSvg />
                    </button>
                  )}
                </div>
              </React.Fragment>
            );
          }
        )}

        <div ref={handleRef} className="d-flex justify-content-center my-3">
          <button
            className={`${styles.buttonAddNew} px-2 py-1 mb-2`}
            onClick={handleAddNewWaiver}
            type="button"
          >
            <span className="mr-2">Add new waiver</span>
            <TrackerAddSvg />
          </button>
        </div>
      </div>
    </>
  );
};

export default WaiverInputComponents;
