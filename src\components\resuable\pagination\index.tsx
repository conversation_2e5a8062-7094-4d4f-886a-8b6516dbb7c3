import React from "react";

import ReactPaginate from "react-paginate";

import BackwardIcon from "../../svgs/BackwardIcon";
import Forward from "../../svgs/ForwardIcon";

import styles from "./index.module.scss";

const Pagination = (props) => {
  return (
    <ReactPaginate
      previousLabel={<BackwardIcon color={"rgba(0,0,0,1)"} />}
      nextLabel={<Forward color={"rgba(0,0,0,1)"} />}
      breakLabel="..."
      renderOnZeroPageCount={null}
      containerClassName={styles.pagination}
      previousLinkClassName={styles.previous}
      nextLinkClassName={styles.next}
      disabledClassName={styles.disabled}
      activeClassName={styles.active}
      pageRangeDisplayed={5}
      marginPgitagesDisplayed={2}
      {...props}
    />
  );
};

export default Pagination;
