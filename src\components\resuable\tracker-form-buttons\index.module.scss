@import "../../../../styles/_mixins.scss";

.label {
  @include font(0.8em, 600);
  color: rgba(24, 24, 24, 1);
  text-align: right;
}

.field {
  @include font(0.8em, 600);
  border: 1px solid rgba(241, 243, 242, 1);
  width: 100%;
  background-color: var(--white);

  &:focus {
    outline: 0;
    border: 1px solid var(--risk-primary);
  }
}

.button {
  outline: 0;
  border: 0;
  background-color: rgba(24, 24, 24, 0.1);
  color: rgba(24, 24, 24, 0.3);
  @include font(0.8em, 600);
  border-radius: 20px;

  &.picked {
    background-color: var(--risk-primary);
    color: var(--white);
  }

  &:hover {
    opacity: 0.8;
  }
}
