import React from "react";

import styles from "./index.module.scss";
import { useHistory } from "react-router";

const TrackerUpdate = ({ isLoading }: { isLoading?: boolean }) => {
  const history = useHistory();

  return (
    <div className={`position-absolute w-100 ${styles.pos}`}>
      <div className={`${styles.wrapper} text-center`}>
        <button
          type="button"
          className={`${styles.buttonBorder} mr-2 py-2`}
          onClick={() => history.push("/deals")}
        >
          Close
        </button>

        <button
          disabled={isLoading}
          type="submit"
          className={`${styles.button} py-2`}
        >
          {isLoading ? "Updating deal..." : "Update Deal"}
        </button>
      </div>
    </div>
  );
};

export default TrackerUpdate;
