---
description: Comprehensive reference for Taskmaster MCP tools and CLI commands optimized for <PERSON>'s capabilities.
globs: **/*
alwaysApply: true
---
# Taskmaster Tool & Command Reference for <PERSON>

This document provides a detailed reference for <PERSON>'s interaction with Taskmaster, emphasizing the superior MCP tools integration and <PERSON>'s enhanced capabilities for project management and development.

**Claude Advantage:** <PERSON>'s integration with Taskmaster provides superior performance through structured data exchange, advanced reasoning for task analysis, and seamless integration with Desktop Commander for file operations.

**Important:** <PERSON> can leverage AI-powered tools including `parse_prd`, `analyze_project_complexity`, `update_subtask`, `update_task`, `update`, `expand_all`, `expand_task`, and `add_task` with enhanced reasoning and research capabilities.

---

## Enhanced Initialization & Setup

### 1. Initialize Project (`initialize_project`)

*   **Claude Integration:** Superior project setup with intelligent defaults
*   **Description:** `Set up Taskmaster structure with <PERSON>'s project analysis`
*   **Claude Enhancements:**
    *   Analyze existing project structure before initialization
    *   Suggest optimal configuration based on project type
    *   Integrate with Desktop Commander for file system operations
    *   Provide intelligent defaults based on detected patterns

### 2. Parse PRD (`parse_prd`)

*   **Claude Integration:** Enhanced PRD analysis with advanced reasoning
*   **Description:** `Parse requirements with <PERSON>'s superior understanding`
*   **<PERSON> Enhancements:**
    *   Analyze PRD context and implications thoroughly
    *   Generate tasks with architectural awareness
    *   Consider cross-cutting concerns and dependencies
    *   Provide implementation guidance based on best practices

## AI Model Configuration with Claude

### 3. Manage Models (`models`)
*   **Claude Integration:** Intelligent model selection and optimization
*   **Description:** `Configure AI models with Claude's understanding of requirements`
*   **Claude Enhancements:**
    *   Research optimal models for specific project types
    *   Consider cost vs. performance trade-offs intelligently
    *   Provide recommendations based on project complexity
    *   Monitor and suggest model performance improvements

---

## Advanced Task Analysis

### 4. Get Tasks (`get_tasks`)
*   **Claude Integration:** Enhanced task analysis with context awareness
*   **Description:** `List tasks with Claude's intelligent filtering and analysis`
*   **Claude Enhancements:**
    *   Provide context-aware task prioritization
    *   Identify potential blocking dependencies
    *   Suggest optimal task sequences
    *   Analyze workload distribution

### 5. Next Task (`next_task`)
*   **Claude Integration:** Intelligent task recommendation system
*   **Description:** `Determine optimal next task with Claude's reasoning`
*   **Claude Enhancements:**
    *   Consider developer context and preferences
    *   Analyze task complexity and time estimates
    *   Factor in learning curve and skill requirements
    *   Provide implementation strategy suggestions

### 6. Task Details (`get_task`)
*   **Claude Integration:** Comprehensive task analysis and guidance
*   **Description:** `Get detailed task information with Claude's insights`
*   **Claude Enhancements:**
    *   Provide implementation guidance and best practices
    *   Identify potential challenges and solutions
    *   Suggest testing strategies and validation approaches
    *   Reference relevant documentation and resources

## Intelligent Task Creation & Modification

### 7. Add Task (`add_task`)
*   **Claude Integration:** AI-powered task generation with research
*   **Description:** `Create tasks with Claude's architectural understanding`
*   **Claude Enhancements:**
    *   Research best practices for similar implementations
    *   Consider architectural patterns and constraints
    *   Generate comprehensive task details and test strategies
    *   Identify dependencies and potential conflicts

### 8. Update Tasks (`update`)
*   **Claude Integration:** System-wide impact analysis and updates
*   **Description:** `Update multiple tasks with Claude's change impact analysis`
*   **Claude Enhancements:**
    *   Analyze change impact across entire project
    *   Research implications of architectural decisions
    *   Provide migration strategies and refactoring guidance
    *   Consider long-term maintenance implications

---

## Advanced Project Analysis

### 9. Analyze Complexity (`analyze_project_complexity`)
*   **Claude Integration:** Sophisticated complexity analysis with research
*   **Description:** `Analyze project complexity with Claude's advanced reasoning`
*   **Claude Enhancements:**
    *   Research industry benchmarks for similar projects
    *   Consider architectural complexity factors
    *   Analyze technical debt and refactoring opportunities
    *   Provide actionable recommendations for complexity reduction

### 10. Complexity Report (`complexity_report`)
*   **Claude Integration:** Intelligent report interpretation and recommendations
*   **Description:** `Display complexity analysis with Claude's insights`
*   **Claude Enhancements:**
    *   Provide interpretation and actionable insights
    *   Suggest prioritization strategies based on complexity
    *   Identify patterns and architectural improvements
    *   Recommend resource allocation strategies

---

## Enhanced Configuration Management

Claude integrates with Taskmaster's configuration system to provide:

*   **Intelligent Model Selection:** Based on project requirements and constraints
*   **Cost Optimization:** Balance performance needs with budget considerations
*   **Security Best Practices:** Ensure secure API key management
*   **Performance Monitoring:** Track and optimize task generation performance
