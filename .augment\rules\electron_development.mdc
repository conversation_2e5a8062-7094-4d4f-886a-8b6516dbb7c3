---
description: Electron-specific development guidelines for the React to Electron desktop app migration project using Augment Agent's capabilities.
globs: **/*.{js,ts,jsx,tsx,json}
alwaysApply: true
---

# Electron Development Guidelines for Augment Agent

## Project Architecture

- **Main Process Responsibilities:**
  - Database operations using better-sqlite3
  - File system access and native OS integration
  - IPC communication handling with security focus
  - Authentication token management and storage
  - Network connectivity detection and sync coordination

- **Renderer Process Responsibilities:**
  - React application with minimal modifications from web version
  - UI components and user interaction handling
  - IPC communication with main process for data operations
  - Offline/online status display and user feedback
  - Client-side validation and error handling

## Security Best Practices

- **Context Isolation:**
  ```javascript
  // ✅ DO: Enable context isolation in main process
  const mainWindow = new BrowserWindow({
    webPreferences: {
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });
  
  // ❌ DON'T: Disable security features
  const mainWindow = new BrowserWindow({
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  ```

- **IPC Communication:**
  ```javascript
  // ✅ DO: Use secure IPC patterns with validation
  // In preload.js
  const { contextBridge, ipcRenderer } = require('electron');
  
  contextBridge.exposeInMainWorld('electronAPI', {
    authenticateUser: (credentials) => ipcRenderer.invoke('auth:login', credentials),
    syncData: () => ipcRenderer.invoke('data:sync'),
    onNetworkChange: (callback) => ipcRenderer.on('network:status', callback)
  });
  
  // ❌ DON'T: Expose entire ipcRenderer
  window.ipcRenderer = require('electron').ipcRenderer;
  ```

## Database Integration

- **SQLite Setup:**
  ```javascript
  // ✅ DO: Use better-sqlite3 with proper configuration
  const Database = require('better-sqlite3');
  const path = require('path');
  
  const db = new Database(path.join(app.getPath('userData'), 'app.db'), {
    verbose: console.log // Only in development
  });
  
  // Enable WAL mode for better concurrency
  db.pragma('journal_mode = WAL');
  ```

- **Sync Metadata Tracking:**
  ```sql
  -- ✅ DO: Create proper sync metadata tables
  CREATE TABLE IF NOT EXISTS sync_metadata (
    table_name TEXT PRIMARY KEY,
    last_sync_timestamp INTEGER NOT NULL,
    last_sync_id TEXT,
    created_at INTEGER DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER DEFAULT (strftime('%s', 'now'))
  );
  
  CREATE TABLE IF NOT EXISTS operation_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type TEXT NOT NULL CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE')),
    table_name TEXT NOT NULL,
    record_id TEXT NOT NULL,
    data TEXT, -- JSON
    timestamp INTEGER DEFAULT (strftime('%s', 'now')),
    synced BOOLEAN DEFAULT FALSE,
    retry_count INTEGER DEFAULT 0
  );
  ```

## Authentication Implementation

- **Token Storage:**
  ```javascript
  // ✅ DO: Use secure storage for tokens
  const Store = require('electron-store');
  
  const store = new Store({
    encryptionKey: 'your-encryption-key',
    name: 'secure-tokens'
  });
  
  // Store tokens with dual expiration
  const storeTokens = (tokens) => {
    store.set('auth.tokens', {
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      expires_at: tokens.expires_at,
      offline_expires_at: tokens.offline_expires_at,
      stored_at: Date.now()
    });
  };
  ```

- **Offline Authentication:**
  ```javascript
  // ✅ DO: Implement context-aware token validation
  const validateToken = (token, isOnline) => {
    try {
      const decoded = jwt.decode(token);
      const currentTime = Math.floor(Date.now() / 1000);
      
      if (isOnline) {
        return decoded.exp > currentTime;
      } else {
        // Use extended offline expiration
        return decoded.offline_exp > currentTime;
      }
    } catch (error) {
      return false;
    }
  };
  ```

## Offline-First Patterns

- **Network Detection:**
  ```javascript
  // ✅ DO: Implement robust network detection
  const { net } = require('electron');
  
  class NetworkManager {
    constructor() {
      this.isOnline = net.isOnline();
      this.setupNetworkMonitoring();
    }
    
    setupNetworkMonitoring() {
      // Monitor network changes
      setInterval(() => {
        const wasOnline = this.isOnline;
        this.isOnline = net.isOnline();
        
        if (wasOnline !== this.isOnline) {
          this.handleNetworkChange(this.isOnline);
        }
      }, 5000);
    }
    
    handleNetworkChange(isOnline) {
      if (isOnline) {
        this.triggerSync();
      }
      // Notify renderer process
      mainWindow.webContents.send('network:status', { isOnline });
    }
  }
  ```

## React Integration

- **Component Adaptation:**
  ```jsx
  // ✅ DO: Adapt existing React components for Electron
  import { useEffect, useState } from 'react';
  
  const NetworkStatus = () => {
    const [isOnline, setIsOnline] = useState(true);
    
    useEffect(() => {
      // Listen for network status changes from main process
      const unsubscribe = window.electronAPI?.onNetworkChange?.(
        (event, { isOnline }) => setIsOnline(isOnline)
      );
      
      return unsubscribe;
    }, []);
    
    return (
      <div className={`network-status ${isOnline ? 'online' : 'offline'}`}>
        {isOnline ? '🟢 Online' : '🔴 Offline'}
      </div>
    );
  };
  ```

## Build and Packaging

- **Electron Builder Configuration:**
  ```json
  // ✅ DO: Configure electron-builder properly
  {
    "build": {
      "appId": "com.yourcompany.risk-app",
      "productName": "Risk App",
      "directories": {
        "output": "dist"
      },
      "files": [
        "build/**/*",
        "node_modules/**/*",
        "public/electron.js"
      ],
      "mac": {
        "category": "public.app-category.business",
        "hardenedRuntime": true,
        "entitlements": "assets/entitlements.mac.plist"
      },
      "win": {
        "target": "nsis",
        "publisherName": "Your Company"
      },
      "linux": {
        "target": "AppImage",
        "category": "Office"
      }
    }
  }
  ```

## Performance Optimization

- **Memory Management:**
  ```javascript
  // ✅ DO: Implement proper cleanup
  app.on('window-all-closed', () => {
    // Close database connections
    if (db) {
      db.close();
    }
    
    if (process.platform !== 'darwin') {
      app.quit();
    }
  });
  ```

- **Database Optimization:**
  ```javascript
  // ✅ DO: Use prepared statements for better performance
  const insertUser = db.prepare(`
    INSERT INTO users (id, email, name, created_at) 
    VALUES (?, ?, ?, ?)
  `);
  
  const insertManyUsers = db.transaction((users) => {
    for (const user of users) {
      insertUser.run(user.id, user.email, user.name, user.created_at);
    }
  });
  ```

For comprehensive task management, see [taskmaster.mdc](mdc:.augment/rules/taskmaster.mdc) and [dev_workflow.mdc](mdc:.augment/rules/dev_workflow.mdc).
