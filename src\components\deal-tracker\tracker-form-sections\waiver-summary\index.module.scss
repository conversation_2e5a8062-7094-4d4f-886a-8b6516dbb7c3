@import "../../../../../styles/_mixins.scss";

.button {
  background-color: rgba(0, 153, 34, 0.2);
  color: var(--risk-primary);
  outline: 0;
  border: 0;
  @include font(0.85em, 600);
  transition: 0.3s;
  &:hover {
    opacity: 0.7;
  }
}

.btnArchive {
  background-color: rgba(161, 170, 166, 0.2);
  outline: 0;
  border: 0;
  transition: 0.3s;
  @include font(0.8em, 600);
  &:hover {
    opacity: 0.7;
  }
}

.btnCancel {
  background-color: rgba(219, 70, 85, 0.2);
  outline: 0;
  border: 0;
  transition: 0.3s;
  &:hover {
    opacity: 0.7;
  }
  > svg {
    > path {
      fill: red;
    }
  }
}
