import React from "react";
// import { useAppSelector } from "./../../store/storeConfig";

// export default function withProtected(Component: any) {
//   return class Authenticate extends React.Component {
//     // state = { authenticated: useAppSelector((state) => state.authSlice) };
//     render() {
//       return (
//         <>{? <Component {...this.props} /> : ""}</>
//       );
//     }
//   };
// }
