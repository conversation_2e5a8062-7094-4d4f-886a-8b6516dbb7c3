---
description: Comprehensive MCP server configuration for the risk-app Electron development project
globs: .claude/**/*
alwaysApply: true
---

# MCP Server Configuration for Risk-App

This document outlines the MCP servers configured for the risk-app Electron project and their specific benefits for development.

## Configured MCP Servers

### 1. Task Master AI
- **Purpose**: Project task management and workflow automation
- **Command**: `npx -y --package=task-master-ai task-master-ai`
- **Benefits**: 
  - AI-powered task generation from PRD
  - Intelligent task breakdown and complexity analysis
  - Dependency management and progress tracking
  - Research-backed task updates and improvements

### 2. Desktop Commander
- **Purpose**: Advanced file system operations and command execution
- **Command**: `npx -y @desktopcommander/cli`
- **Benefits**:
  - Superior file operations (read, write, search, edit)
  - Multi-file context analysis
  - Intelligent code search and modification
  - Project structure analysis and manipulation

### 3. SQLite MCP Server
- **Purpose**: Direct SQLite database interaction and management
- **Command**: `npx -y mcp-sqlite ./db/local.db`
- **Benefits**:
  - Complete CRUD operations on SQLite databases
  - Schema inspection and table management
  - Query execution and data analysis
  - Database structure optimization

### 4. Electron Debug Server
- **Purpose**: Advanced Electron application debugging and control
- **Command**: `npx -y electron-mcp-server`
- **Benefits**:
  - Chrome DevTools Protocol integration
  - Application lifecycle management
  - Debug session control and monitoring
  - Performance analysis and optimization

### 5. Better-SQLite3 Documentation
- **Purpose**: Access to better-sqlite3 library documentation and examples
- **Command**: `npx -y @context7/better-sqlite3`
- **Benefits**:
  - Real-time access to better-sqlite3 API documentation
  - Performance optimization guidelines
  - Best practices for SQLite integration
  - Troubleshooting and debugging assistance

### 6. Electron Documentation
- **Purpose**: Comprehensive Electron framework documentation
- **Command**: `npx -y @context7/electron`
- **Benefits**:
  - Complete Electron API reference
  - Security best practices and guidelines
  - Cross-platform development patterns
  - IPC communication examples and patterns

## Project-Specific Integration Benefits

### Enhanced Development Workflow
- **Task Management**: Task Master AI provides intelligent project planning
- **File Operations**: Desktop Commander enables sophisticated code manipulation
- **Database Development**: SQLite MCP gives direct database access and optimization
- **Electron Debugging**: Advanced debugging capabilities for desktop application

### Security and Best Practices
- **Electron Security**: Access to latest security guidelines and patterns
- **SQLite Optimization**: Performance tuning and query optimization
- **Code Quality**: Intelligent code analysis and improvement suggestions
- **Architecture Guidance**: Research-backed architectural decisions

### Offline-First Architecture Support
- **Database Design**: SQLite schema optimization for offline operations
- **Sync Mechanisms**: Best practices for data synchronization
- **Conflict Resolution**: Advanced strategies for handling data conflicts
- **Performance Monitoring**: Tools for analyzing sync performance

## Usage Guidelines

1. **Start with Task Master**: Initialize project and generate tasks from PRD
2. **Use Desktop Commander**: For all file operations and code analysis
3. **Leverage SQLite MCP**: For database development and optimization
4. **Debug with Electron MCP**: For application lifecycle and performance issues
5. **Reference Documentation**: Use better-sqlite3 and Electron docs for implementation details

For detailed usage instructions, see [taskmaster.mdc](mdc:.claude/rules/taskmaster.mdc) and [dev_workflow.mdc](mdc:.claude/rules/dev_workflow.mdc).
