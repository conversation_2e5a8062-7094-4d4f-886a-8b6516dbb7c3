#!/usr/bin/env node

/**
 * Security test runner for Electron application
 * Run this script to validate security configurations
 */

const { app } = require('electron');
const path = require('path');
const SecurityTests = require('../electron/security/security-tests');

/**
 * Run security tests when Electron is ready
 */
async function runSecurityTests() {
  console.log('🔒 Electron Security Test Suite');
  console.log('================================\n');
  
  try {
    const securityTests = new SecurityTests();
    const results = await securityTests.runAllTests();
    
    console.log('\n📊 Test Summary:');
    console.log(`Total tests: ${results.total}`);
    console.log(`Passed: ${results.passed}`);
    console.log(`Failed: ${results.total - results.passed}`);
    
    if (results.allPassed) {
      console.log('\n🎉 All security tests passed! Your Electron app is properly secured.');
      process.exit(0);
    } else {
      console.log('\n⚠️  Some security tests failed. Please review the configuration.');
      
      // Show failed tests
      const failedTests = results.results.filter(r => !r.passed);
      console.log('\nFailed tests:');
      failedTests.forEach(test => {
        console.log(`❌ ${test.test}: ${test.input}`);
        console.log(`   Expected: ${test.expected}, Got: ${test.actual}`);
      });
      
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Security test suite failed:', error);
    process.exit(1);
  }
}

// Handle app ready event
if (app) {
  app.whenReady().then(runSecurityTests);
} else {
  console.error('❌ Electron app not available. Make sure to run this script with Electron.');
  process.exit(1);
}

// Handle app events
app.on('window-all-closed', () => {
  // Don't quit on macOS
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // Re-run tests if activated
  if (process.platform === 'darwin') {
    runSecurityTests();
  }
});
