const PortfolioSm = () => {
  return (
    <svg
      width="9"
      height="9"
      viewBox="0 0 9 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.91625 0.683533C3.6146 0.683533 3.35797 0.893152 3.28594 1.17568H5.70815C5.63611 0.893152 5.37949 0.683533 5.07783 0.683533H3.91625ZM6.3932 1.17569H7.28464C8.23011 1.17569 9 1.95493 9 2.91188C9 2.91188 8.97299 3.317 8.96398 3.88115C8.96308 3.9258 8.94147 3.96955 8.9059 3.99598C8.68934 4.15593 8.49125 4.28808 8.47324 4.29719C7.72586 4.79846 6.85738 5.15116 5.93217 5.32661C5.87184 5.33845 5.81241 5.30701 5.78179 5.25324C5.52246 4.80393 5.03802 4.51137 4.49775 4.51137C3.96108 4.51137 3.47214 4.80074 3.20515 5.25051C3.17409 5.30337 3.11556 5.3339 3.05568 5.32251C2.13812 5.14661 1.26963 4.79436 0.526763 4.30175L0.0945473 4.00099C0.0585293 3.97821 0.036018 3.9372 0.036018 3.89163C0.0225113 3.65922 0 2.91188 0 2.91188C0 1.95493 0.769885 1.17569 1.71536 1.17569H2.6023C2.68784 0.514934 3.24162 0 3.91696 0H5.07854C5.75388 0 6.30765 0.514934 6.3932 1.17569ZM8.89085 4.85513L8.87266 4.86427C7.95402 5.47663 6.84891 5.88335 5.68924 6.05244C5.52552 6.07529 5.3618 5.97018 5.31632 5.80566C5.21627 5.43094 4.89338 5.18416 4.50682 5.18416H4.50227H4.49318C4.10662 5.18416 3.78373 5.43094 3.68368 5.80566C3.6382 5.97018 3.47448 6.07529 3.31076 6.05244C2.15109 5.88335 1.04598 5.47663 0.127337 4.86427C0.122789 4.8597 0.0773118 4.83228 0.0409298 4.85513C0 4.87798 0 4.93282 0 4.93282L0.0318343 7.26345C0.0318343 8.22312 0.804952 9 1.75998 9H7.23547C8.1905 9 8.96362 8.22312 8.96362 7.26345L9 4.93282C9 4.93282 9 4.87798 8.95907 4.85513C8.93633 4.84142 8.90905 4.84599 8.89085 4.85513ZM4.84615 6.55088C4.84615 6.75932 4.69385 6.92309 4.5 6.92309C4.31077 6.92309 4.15385 6.75932 4.15385 6.55088V5.91068C4.15385 5.70721 4.31077 5.53847 4.5 5.53847C4.69385 5.53847 4.84615 5.70721 4.84615 5.91068V6.55088Z"
        fill="#86650C"
      />
    </svg>
  );
};

export default PortfolioSm;
