/*

create a per phase checklist

each phases are group without identity, i.e it doesn't matter which list is checked;


##getting total percentage per phase

1. get the total  number of checked list per phase then divide and multiply
i.e if totalList =3; totalPercentage = 40 = totalPercentage/totaList = someValue%
then if 2 lists are checked the current checkedPercentage = count * someValue %

...and so on


stage 1 ;
totalPercentage/totalListPerPhase = distributive% per phase;
stage 2 ;
currentPercentage = distibutive% per phase * numbersOfCheckedList

stage 3 ;
totalPhasePercentage = phase1 + phase2 + phase3 + phase4





*/


export const checkList = ""