/**
 * Security utilities for Electron application
 * Implements security best practices and validation functions
 */

const { URL } = require('url');

/**
 * Allowed domains for external resources
 */
const ALLOWED_DOMAINS = [
  'stackpath.bootstrapcdn.com',
  'cdn.jsdelivr.net',
  'cdn01.boxcdn.net',
  'fonts.googleapis.com',
  'fonts.gstatic.com'
];

/**
 * Allowed protocols for navigation
 */
const ALLOWED_PROTOCOLS = ['https:', 'http:', 'file:'];

/**
 * Validate if a URL is safe for external access
 * @param {string} url - URL to validate
 * @returns {boolean} - True if URL is safe
 */
function isUrlSafe(url) {
  try {
    const parsedUrl = new URL(url);
    
    // Check protocol
    if (!ALLOWED_PROTOCOLS.includes(parsedUrl.protocol)) {
      return false;
    }
    
    // For external URLs, check if domain is allowed
    if (parsedUrl.protocol === 'https:' || parsedUrl.protocol === 'http:') {
      return ALLOWED_DOMAINS.includes(parsedUrl.hostname);
    }
    
    // File protocol is allowed for local resources
    if (parsedUrl.protocol === 'file:') {
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('URL validation error:', error);
    return false;
  }
}

/**
 * Validate IPC message parameters
 * @param {string} channel - IPC channel name
 * @param {any} data - Data being sent
 * @returns {boolean} - True if message is valid
 */
function validateIpcMessage(channel, data) {
  // Check channel name format
  if (typeof channel !== 'string' || channel.length === 0) {
    return false;
  }
  
  // Prevent potential code injection in channel names
  if (!/^[a-zA-Z0-9-_]+$/.test(channel)) {
    return false;
  }
  
  // Check data size (prevent DoS attacks)
  const dataString = JSON.stringify(data);
  if (dataString.length > 1024 * 1024) { // 1MB limit
    console.warn('IPC message too large:', dataString.length);
    return false;
  }
  
  return true;
}

/**
 * Sanitize file paths to prevent directory traversal
 * @param {string} filePath - File path to sanitize
 * @returns {string|null} - Sanitized path or null if invalid
 */
function sanitizeFilePath(filePath) {
  if (typeof filePath !== 'string') {
    return null;
  }
  
  // Remove dangerous patterns
  const dangerous = ['../', '..\\', './', '.\\'];
  for (const pattern of dangerous) {
    if (filePath.includes(pattern)) {
      console.warn('Dangerous file path detected:', filePath);
      return null;
    }
  }
  
  return filePath;
}

/**
 * Validate permission requests
 * @param {string} permission - Permission being requested
 * @param {string} origin - Origin requesting permission
 * @returns {boolean} - True if permission should be granted
 */
function validatePermissionRequest(permission, origin) {
  // Only allow specific permissions
  const allowedPermissions = [
    'notifications',
    'clipboard-read',
    'clipboard-write'
  ];
  
  if (!allowedPermissions.includes(permission)) {
    console.warn('Unauthorized permission request:', permission);
    return false;
  }
  
  // For local app, allow all permitted permissions
  if (origin.startsWith('file://')) {
    return true;
  }
  
  // For external origins, be more restrictive
  try {
    const url = new URL(origin);
    return ALLOWED_DOMAINS.includes(url.hostname);
  } catch (error) {
    console.error('Invalid origin:', origin);
    return false;
  }
}

/**
 * Security headers for session
 */
const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Referrer-Policy': 'strict-origin-when-cross-origin'
};

/**
 * Apply security headers to a session
 * @param {Electron.Session} session - Electron session object
 */
function applySecurityHeaders(session) {
  session.webRequest.onHeadersReceived((details, callback) => {
    const responseHeaders = { ...details.responseHeaders };
    
    // Add security headers
    Object.entries(SECURITY_HEADERS).forEach(([header, value]) => {
      responseHeaders[header] = [value];
    });
    
    callback({ responseHeaders });
  });
}

module.exports = {
  isUrlSafe,
  validateIpcMessage,
  sanitizeFilePath,
  validatePermissionRequest,
  applySecurityHeaders,
  ALLOWED_DOMAINS,
  ALLOWED_PROTOCOLS,
  SECURITY_HEADERS
};
