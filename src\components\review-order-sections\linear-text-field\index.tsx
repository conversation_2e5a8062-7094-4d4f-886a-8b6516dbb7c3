import React from "react";
import { ErrorMessage, Field } from "formik";
import styles from "./index.module.css";

interface ILinearTextFieldProps {
  data: any;
}

export default function LinearTextField({
  data,
  ...rest
}: ILinearTextFieldProps) {
  return (
    <div className="d-flex align-items-center-center my-2 flex-column w-100 overflow-auto">
      <div className="d-flex">
        <div className={`d-flex align-self-center mr-2`} style={{ flex: 2 }}>
          {Boolean(data?.label) && (
            <label className={styles.label}>
              <span>{data?.label}</span>
            </label>
          )}
        </div>

        <div
          className="d-flex align-items-start flex-column flex-md-row flex-lg-row flex-xl-row"
          style={{ flex: 10 }}
        >
          {data?.children &&
            data?.children.map((child: any, index: any) => {
              return (
                <>
                  <Field
                    name={child?.name}
                    type={child?.type}
                    placeholder={child?.placeholder}
                    className={`${
                      styles.field
                    } py-2  px-1 my-1 my-md-0 my-lg-0 my-xl-0 ${
                      index === 1 && "mx-0 mx-md-1 mx-lg-2 mx-xl-2"
                    }`}
                    style={{ flex: index === 0 ? 8 : index === 1 ? 8 : 5 }}
                    {...rest}
                  />
                  <div className="text-danger small">
                    <ErrorMessage name={child?.name} />
                  </div>
                </>
              );
            })}
        </div>
      </div>
    </div>
  );
}
