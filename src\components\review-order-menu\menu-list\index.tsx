import React from "react";
import { IReviewOrderMenu } from "./../../../models/reviewOrderMenu";
import ActiveIcon from "./../../svgs/ActiveIcon";
import styles from "./index.module.css";
import { handleRef } from "./../../../utils/handleRef";
import { readLastLocation } from "../../../utils/setLastLocation";

interface IReviewOrderMenuProps {
  item: IReviewOrderMenu;
  selectedMenu: string;
  setSelectedMenu: (arg: string) => void;
}

export default function ReviewOrderMenuList({
  item: { id, name, ref },
  selectedMenu,
  setSelectedMenu,
}: IReviewOrderMenuProps) {
  const handleRefC = React.useCallback(
    (node: any) => {
      handleRef(node, name);
      setSelectedMenu(name);
    },
    [name, setSelectedMenu]
  );

  return (
    <section
      key={id}
      onClick={() => handleRefC(ref)}
      style={{ cursor: "pointer" }}
    >
      <div className="d-flex align-items-center my-3">
        <aside className=" d-flex align-items-start mr-3">
          <ActiveIcon
            color={
              name === selectedMenu || name === readLastLocation()
                ? "var(--risk-primary)"
                : "rgba(0,0,0,0.4)"
            }
          />
        </aside>
        <aside
          className={`${
            id === 6
              ? `${styles.suppName} font-weight-bold`
              : name === selectedMenu || name === readLastLocation()
              ? `${styles.nameGreen}`
              : `${styles.name}`
          } `}
        >
          {name}
        </aside>
      </div>
    </section>
  );
}
