import React from 'react';
import { useNetworkStatus } from '../../hooks/useElectron';
import styles from './ConnectionIndicator.module.scss';

interface ConnectionIndicatorProps {
  className?: string;
  size?: 'small' | 'medium' | 'large';
  variant?: 'dot' | 'badge' | 'icon' | 'text';
  showText?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'inline';
}

/**
 * Simple connection indicator component
 * Can be used as a dot, badge, icon, or text indicator
 */
const ConnectionIndicator: React.FC<ConnectionIndicatorProps> = ({
  className = '',
  size = 'medium',
  variant = 'dot',
  showText = false,
  position = 'inline'
}) => {
  const isOnline = useNetworkStatus();

  const getIndicatorContent = () => {
    switch (variant) {
      case 'dot':
        return (
          <div 
            className={`${styles.dot} ${styles[size]} ${isOnline ? styles.online : styles.offline}`}
            title={isOnline ? 'Online' : 'Offline'}
          />
        );
      
      case 'badge':
        return (
          <div 
            className={`${styles.badge} ${styles[size]} ${isOnline ? styles.online : styles.offline}`}
            title={isOnline ? 'Online' : 'Offline'}
          >
            {isOnline ? '●' : '●'}
          </div>
        );
      
      case 'icon':
        return (
          <span 
            className={`${styles.icon} ${styles[size]} ${isOnline ? styles.online : styles.offline}`}
            title={isOnline ? 'Online' : 'Offline'}
          >
            {isOnline ? '🟢' : '🔴'}
          </span>
        );
      
      case 'text':
        return (
          <span 
            className={`${styles.text} ${styles[size]} ${isOnline ? styles.online : styles.offline}`}
          >
            {isOnline ? 'Online' : 'Offline'}
          </span>
        );
      
      default:
        return null;
    }
  };

  const containerClasses = [
    styles.indicator,
    styles[position],
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {getIndicatorContent()}
      {showText && variant !== 'text' && (
        <span className={`${styles.statusText} ${isOnline ? styles.online : styles.offline}`}>
          {isOnline ? 'Online' : 'Offline'}
        </span>
      )}
    </div>
  );
};

export default ConnectionIndicator;
