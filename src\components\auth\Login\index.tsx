import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import styles from "./index.module.css";
import { InputPassword, Input } from "./Input/";
import { useLogin } from "./../../../apis/index";
import { ILoginValues } from "./../../../models/loginValues";
import { AlertIcon } from "../../../exports";
import LoginIcon from "../../../assets/images/login-icon.jpeg";

const initialValues: ILoginValues = {
  email: process.env.BACKSTAGE_LOGIN || "",
  pass: process.env.BACKSTAGE_PASSWORD || "",
};
// Auto-fill with environment variables for testing
// <EMAIL>
// L@xc!M69V



const validationSchema = Yup.object().shape({
  email: Yup.string().email().required(),
  pass: Yup.string().required(),
});

export default function LoginComponent() {
  const { mutate, isLoading, error, isError }: any = useLogin();

  const handleSubmit = async (values: ILoginValues) => {
    console.log('Login attempt with:', { email: values.email, hasPassword: !!values.pass });
    try {
      mutate(values);
    } catch (err) {
      console.error('Login error:', err);
    }
  };

  const [toggleType, setToggleType] = React.useState(true);

  const handleToggle = React.useCallback(() => setToggleType((t) => !t), []);

  return (
    <section className={`col-12 col-md-6 col-lg-5 col-xl-5 ${styles.wrapper}`}>
      <Formik
        onSubmit={async (values: any) => {
          handleSubmit(values);
        }}
        initialValues={initialValues}
        validationSchema={validationSchema}
      >
        {() => {
          return (
            <Form>
              <section
                className={`d-flex flex-column align-items-center justify-content-center ${styles.innerWrapper}`}
              >
                <section className="mb-2">
                  <img
                    src={LoginIcon}
                    className="img-fluid"
                    width="64px"
                    height={"48px"}
                    alt="login icon"
                  />
                </section>
                <section className="mb-3">
                  <div className="text-center">
                    <span className={`${styles.login} font-weight-bold`}>
                      Backstage Login
                    </span>
                  </div>
                </section>
                <section className="d-flex align-items-center text-danger">
                  {isError && (
                    <>
                      <div>
                        <img
                          src={AlertIcon}
                          alt="danger alert"
                          className="img-fluid mr-2"
                        />
                      </div>
                      <section className="d-flex align-items-center text-danger">
                        {error?.message || "Something went wrong."}
                      </section>
                    </>
                  )}
                </section>

                <section>
                  <Input
                    name="email"
                    label="Email"
                    type="email"
                    placeholder="enter your email"
                  />
                  <InputPassword
                    {...{
                      handleToggle,
                      toggleType,
                      name: "pass",
                      label: "Password",
                      placeholder: "enter your password",
                    }}
                    type={toggleType ? "password" : "text"}
                  />
                </section>

                <button
                  type="submit"
                  disabled={isLoading}
                  className={`${styles.button} mt-4 py-2`}
                >
                  {isLoading ? "Signing in..." : "Login"}
                </button>

                {/*<section className="mt-5">
                  <div className="border-bottom border-dark pb-3">
                    <span className={styles.forgetPassword}>
                      Forget Password?
                    </span>
                  </div>
                </section>*/}
              </section>
            </Form>
          );
        }}
      </Formik>
    </section>
  );
}
