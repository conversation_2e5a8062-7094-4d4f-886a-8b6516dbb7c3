{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "sourceMap": false}, "include": ["src"], "resolve": {"fallback": {"react/jsx-runtime": "react/jsx-runtime.js", "react/jsx-dev-runtime": "react/jsx-dev-runtime.js"}}}