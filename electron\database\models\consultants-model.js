/**
 * Consultants model for database operations
 * Handles consultant-specific business logic and queries
 */

const BaseModel = require('./base-model');

class ConsultantsModel extends BaseModel {
  constructor(db) {
    super(db, 'consultants');
    this.initializeConsultantsStatements();
  }

  /**
   * Initialize consultant-specific prepared statements
   */
  initializeConsultantsStatements() {
    this.preparedStatements.findBySpecialization = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE specialization = ? ORDER BY name ASC
    `);

    this.preparedStatements.findByEmail = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE email = ?
    `);
  }

  /**
   * Find consultants by specialization
   * @param {string} specialization - Consultant specialization
   * @returns {Array} Array of consultants
   */
  findBySpecialization(specialization) {
    try {
      return this.preparedStatements.findBySpecialization.all(specialization);
    } catch (error) {
      console.error('Error finding consultants by specialization:', error);
      throw error;
    }
  }

  /**
   * Find consultant by email
   * @param {string} email - Consultant email
   * @returns {Object|null} Consultant or null
   */
  findByEmail(email) {
    try {
      return this.preparedStatements.findByEmail.get(email);
    } catch (error) {
      console.error('Error finding consultant by email:', error);
      throw error;
    }
  }

  /**
   * Create consultant with validation
   * @param {Object} consultantData - Consultant data
   * @returns {Object} Created consultant
   */
  create(consultantData) {
    try {
      this.validateConsultantData(consultantData);
      return super.create(consultantData);
    } catch (error) {
      console.error('Error creating consultant:', error);
      throw error;
    }
  }

  /**
   * Validate consultant data
   * @param {Object} data - Consultant data to validate
   */
  validateConsultantData(data) {
    const errors = [];

    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
      errors.push('Consultant name is required');
    }

    if (data.email && !this.isValidEmail(data.email)) {
      errors.push('Invalid email format');
    }

    if (data.phone && !this.isValidPhone(data.phone)) {
      errors.push('Invalid phone number format');
    }

    if (errors.length > 0) {
      throw new Error(`Validation errors: ${errors.join(', ')}`);
    }
  }

  /**
   * Check if email is valid
   * @param {string} email - Email to validate
   * @returns {boolean} Whether email is valid
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Check if phone number is valid
   * @param {string} phone - Phone number to validate
   * @returns {boolean} Whether phone is valid
   */
  isValidPhone(phone) {
    // Basic phone validation - adjust regex as needed
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    return phoneRegex.test(cleanPhone);
  }
}

module.exports = ConsultantsModel;
