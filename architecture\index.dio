<mxfile host="65bd71144e">
    <diagram id="TInKN9Rd99bCLCSjSEEy" name="Page-1">
        <mxGraphModel dx="948" dy="569" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="14" value="" style="edgeStyle=none;html=1;fontSize=14;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="705" y="120" as="sourcePoint"/>
                        <mxPoint x="705" y="150" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2" value="&lt;span style=&quot;font-size: 14px&quot;&gt;New model (label, name, options, section, sort, type, value, children)&lt;/span&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="590" y="40" width="240" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="21" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=14;" edge="1" parent="1" source="4">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="640" y="200" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="&lt;font style=&quot;font-size: 14px&quot;&gt;UI&lt;/font&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="163" y="160" width="121" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="10" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="5" target="2">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="" style="edgeStyle=none;html=1;fontSize=14;" edge="1" parent="1" source="5" target="4">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="&lt;font style=&quot;font-size: 14px&quot;&gt;Existing model (label, name, options, section, sort, type, value)&lt;/font&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="110" y="40" width="230" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="6" target="15">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="&lt;font style=&quot;font-size: 14px&quot;&gt;UI&lt;/font&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="643" y="150" width="123" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="28" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="15" target="19">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="&lt;font style=&quot;font-size: 14px&quot;&gt;Connector Algorithm (Children Data Collector)&lt;/font&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="150" y="310" width="121" height="85" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="" style="edgeStyle=none;html=1;fontSize=14;" edge="1" parent="1" source="19">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="522" y="350.9307692307692" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="&lt;font style=&quot;font-size: 14px&quot;&gt;Contact Component&lt;/font&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="350" y="322.5" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="33" style="edgeStyle=orthogonalEdgeStyle;html=1;fontSize=14;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="750" y="230" as="targetPoint"/>
                        <mxPoint x="660" y="342.5" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="660" y="360"/>
                            <mxPoint x="750" y="360"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="&lt;span style=&quot;font-size: 14px&quot;&gt;Connector Algorithm&lt;br&gt;(Values Collector)&lt;/span&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="520" y="322.5" width="140" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>