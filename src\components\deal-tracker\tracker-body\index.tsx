import React from 'react';

import {Formik, Form} from 'formik';

import {extractFieldValue} from '../../../utils/groupBy';

import {extraWaiversValuesNew} from '../../../utils/extractWaivers';

import styles from './index.module.scss';

import {usePost} from './../../../apis';

import {useHistory, useParams} from 'react-router';

const TrackerBody = ({
  children,
  data,
}: {
  children: React.ReactNode;
  data: any;
}) => {
  const params: any = useParams();

  const [isNote, setIsNote] = React.useState(false);

  const {mutate, isLoading, isSuccess} = usePost({
    path: '/order/tracker',
    message: `${
      isNote ? 'Note successfully updated' : 'Tracker successfully updated.'
    }`,
    position: 'bottom-right',
    key: [`/order/tracker/${params?.id}`],
    key2: ['order', '/orders'],
    cb: setIsNote,
  });

  const handleSubmit = (values: any) => {
    mutate({...values, id: params?.id});
  };

  const history = useHistory();

  // Temporarily disabled redirect to debug HashRouter parameter parsing
  // React.useEffect(() => {
  //   if (!params?.id) {
  //     history.push('/deals');
  //   }
  // }, [params?.id, history]);

  return (
    <Formik
      initialValues={{
        ...extraWaiversValuesNew({}),
        ...extractFieldValue(data?.fields ?? []),
        note: data?.notes ?? [],
        waivers: data?.waivers ?? [],
      }}
      enableReinitialize={true}
      onSubmit={handleSubmit}
    >
      {formik => {
        return (
          <Form className={`container-fluid px-3 mt-3 ${styles.wrapper}`}>
            <div className="row">
              {React.Children.map(children, (child: any) => {
                return React.cloneElement(child, {
                  formik,
                  data,
                  isLoading,
                  isSuccess,
                  handleSubmitNote: handleSubmit,
                  setIsNote,
                });
              })}
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default TrackerBody;
