import React from "react";

import { createScript, appendScript, getByID } from "../utils/thirdPartyUtils";
import { getSession } from "./../utils/readLocalStorage";

const { REACT_APP_BOX_TOKEN: tokenName } = process.env;

export const useInjectBox = (data: any) => {
  var folderId = data?.box_folder_id || "";

  var accessToken = `${getSession(tokenName)?.access_token}`;

  React.useLayoutEffect(() => {
    createScript(
      "https://cdn01.boxcdn.net/platform/elements/15.0.0/en-US/explorer.js",
      "explorer",
      () => {
        const newScript = document.createElement("script");

        const scriptContent = document.createTextNode(`
          var contentExplorer = new Box.ContentExplorer();
          contentExplorer.show("${folderId}", "${accessToken}", {
            container: ".downloadContainer",
            logoUrl: "",
            responseInterceptor: (response) => {
              const statusCode = response?.response?.status;
              const boxToken = window.localStorage.getItem("BOX-TOKEN");

              const count = window.localStorage.getItem("BOX_COUNT");


              if ((statusCode === 401 || statusCode === 403) && !!boxToken && !count) {
                alert("Processing Box session. Reload this page.");
                window.localStorage.setItem("BOX_COUNT", true);
                window.location.reload();
              }else{
                  if ((statusCode === 401 || statusCode === 403) && !!boxToken){
                    window.localStorage.removeItem("BOX-TOKEN");
                    alert("Box session missing or expired. Reload this page.");
                    window.location.reload();
                  }
              }
              

              return response;
            }
          });

          contentExplorer.on("upload", (data) => {
            window.location.reload();
          });     
        `);

        newScript.id = "explorerScript";

        newScript.appendChild(scriptContent);

        appendScript(newScript);
      }
    );

    //createScript(
    //  "https://cdn01.boxcdn.net/platform/elements/15.0.0/en-US/uploader.js",
    //  "uploader",
    //  () => {
    //    const newScript = document.createElement("script");

    //    const scriptContent = document.createTextNode(`

    //      var uploader = new Box.ContentUploader();

    //      uploader.show("${folderId}", "${accessToken}", {
    //        container: ".uploadContainer",
    //      });

    //      uploader.on("complete", (data) => {
    //        window.location.reload();
    //      });

    //    `);

    //    newScript.id = "uploaderScript";

    //    newScript.appendChild(scriptContent);

    //    appendScript(newScript);
    //  }
    //);

    return () => {
      const explorerRef = getByID("explorer");
      const explorerScriptRef = getByID("explorerScript");

      //const uploaderRef = getByID("uploader");
      //const uploaderScriptRef = getByID("uploaderScript");

      explorerRef?.remove();
      explorerScriptRef?.remove();

      //uploaderRef.remove();
      //uploaderScriptRef.remove();
    };
  }, [folderId, accessToken]);
};
