@import "../../../../styles/_mixins.scss";

.wrapper {
  .innerWrapper {
    width: 100%;
    cursor: pointer;
    line-height: 18px;
    transition: 0.3s;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.03);

    &:hover .icons {
      display: flex !important;
    }

    &:hover {
      opacity: 0.95;
      border: 1px solid var(--risk-primary);
    }

    .note {
      @include font(0.9em, 500);
      white-space: pre-line;
      word-break: break-allss;
    }

    .time {
      @include font(0.85em, 500);
      color: rgba(161, 170, 166, 1);
    }

    .author {
      @include font(0.85em, 500);
      color: rgba(213, 156, 0, 1);
    }

    .icons {
      display: none !important;

      &:hover {
        opacity: 0.6;
      }
    }
  }
}

.buttonBorder {
  width: 45%;
  border-radius: 2px;
  outline: 0;
  color: var(--risk-primary);
  border: 1px solid var(--risk-primary);
  background-color: var(white);
  @include font(0.8em, 600);
  &:hover {
    opacity: 0.7;
  }
}
.button {
  width: 45%;
  border-radius: 2px;
  outline: 0;
  border: 0;
  color: var(--white);
  background-color: var(--risk-primary);
  @include font(0.8em, 600);

  &:hover {
    opacity: 0.7;
  }
}

.delete {
  color: rgba(219, 70, 85, 1);
  @include font(0.9em, 600);
}
