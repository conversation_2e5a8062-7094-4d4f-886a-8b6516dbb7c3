import React from "react";

//import NoPortfolio from "../../resuable/no-portfolio";
import PortfolioSvg from "../../svgs/Porfolio";

import styles from "./index.module.scss";

import CustomModal from "../../resuable/modal";

import AddPropertySummary from "../tracker-form-sections/add-property-summary";
import GridSheet from "../../resuable/data-grid";

import { useHistory, useParams } from "react-router-dom";
import { useGet, usePost } from "../../../apis";
import { useQueryClient } from "react-query";

interface ITrackerPortfolio {
  data?: any;
}

const RevealIcon = (
  <svg
    width="23"
    height="22"
    viewBox="0 0 23 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="0.05"
      y="0.05"
      width="22.9"
      height="21.9"
      rx="5.95"
      fill="#D59C00"
      fillOpacity="0.1"
    />
    <path
      d="M8.98713 11.603C8.99578 11.2487 8.71554 10.9544 8.3612 10.9458C8.00686 10.9371 7.7126 11.2174 7.70395 11.5717L8.98713 11.603ZM7.62072 14.9818C7.61207 15.3361 7.89231 15.6304 8.24665 15.6391C8.60099 15.6477 8.89525 15.3675 8.9039 15.0131L7.62072 14.9818ZM7.78976 14.5633C7.54992 14.8242 7.56705 15.2302 7.82802 15.4701C8.089 15.7099 8.49499 15.6928 8.73483 15.4318L7.78976 14.5633ZM15.6832 7.87125C15.9231 7.61027 15.906 7.20428 15.645 6.96444C15.384 6.72459 14.978 6.74172 14.7382 7.0027L15.6832 7.87125ZM8.19267 14.3593C7.84031 14.3977 7.58586 14.7145 7.62432 15.0669C7.66278 15.4192 7.9796 15.6737 8.33196 15.6352L8.19267 14.3593ZM11.7229 15.2651C12.0753 15.2266 12.3297 14.9098 12.2913 14.5574C12.2528 14.2051 11.936 13.9506 11.5836 13.9891L11.7229 15.2651ZM15.8523 7.45264C15.861 7.0983 15.5808 6.80404 15.2264 6.79539C14.8721 6.78674 14.5778 7.06698 14.5692 7.42133L15.8523 7.45264ZM14.4859 10.8314C14.4773 11.1858 14.7575 11.48 15.1119 11.4887C15.4662 11.4973 15.7605 11.2171 15.7691 10.8627L14.4859 10.8314ZM15.2803 8.07509C15.6327 8.03663 15.8871 7.71981 15.8487 7.36746C15.8102 7.0151 15.4934 6.76065 15.141 6.79911L15.2803 8.07509ZM11.75 7.16929C11.3977 7.20775 11.1432 7.52457 11.1817 7.87692C11.2202 8.22928 11.537 8.48373 11.8893 8.44527L11.75 7.16929ZM7.70395 11.5717L7.62072 14.9818L8.9039 15.0131L8.98713 11.603L7.70395 11.5717ZM8.73483 15.4318L15.6832 7.87125L14.7382 7.0027L7.78976 14.5633L8.73483 15.4318ZM8.33196 15.6352L11.7229 15.2651L11.5836 13.9891L8.19267 14.3593L8.33196 15.6352ZM14.5692 7.42133L14.4859 10.8314L15.7691 10.8627L15.8523 7.45264L14.5692 7.42133ZM15.141 6.79911L11.75 7.16929L11.8893 8.44527L15.2803 8.07509L15.141 6.79911Z"
      fill="#D59C00"
    />
    <rect
      x="0.05"
      y="0.05"
      width="22.9"
      height="21.9"
      rx="5.95"
      stroke="#D59C00"
      strokeWidth="0.1"
    />
  </svg>
);

const AddIcon = (
  <svg
    width="24"
    height="26"
    viewBox="0 0 24 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 8.125V17.875"
      stroke="#0D2516"
      strokeWidth="1.56"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.19995 13H16.8"
      stroke="#0D2516"
      strokeWidth="1.56"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1 26V0H-1V26H1Z"
      fill="#EAEAEA"
      mask="url(#path-1-inside-1_623_2570)"
    />
  </svg>
);

const TrackerPortfolio = ({ data }: ITrackerPortfolio) => {
  const [show, setShow] = React.useState(false);

  const [showGrid, setShowGrid] = React.useState(false);

  const { id }: { [key: string]: any } = useParams();

  const history = useHistory();

  React.useEffect(() => {
    if (!id) history.push("/deals");

    //eslint-disable-next-line
  }, []);

  const {
    data: portfolioData,
    isLoading,
    error,
    isError,
  } = useGet(`/order/portfolio/property/${id}`);

  const queryClient = useQueryClient();

  const invalidatePortfolio = () => {
    queryClient.invalidateQueries(`/order/portfolio/property/${id}`);
  };

  const {
    mutate,
    isLoading: isSaving,
    isError: isErrorSaving,
    isSuccess,
  } = usePost({
    path: "/order/portfolio/property",
    showErr: false,
  });

  const match = !isLoading && !isError;

  return (
    <>
      <CustomModal
        {...{
          title: "Add property to portfolio",
          show,
          setShow,
          columnLayout: "col-12 col-md-9 col-lg-8 col-xl-6",
          align: "align-items-start pt-5",
        }}
      >
        <AddPropertySummary
          {...{
            setShow,
            invalidatePortfolio,
          }}
        />
      </CustomModal>

      <CustomModal
        {...{
          title: "Portfolio properties",
          show: showGrid,
          setShow: setShowGrid,
          columnLayout: "col-12 col-md-11 col-lg-11 col-xl-11",
          align: "align-items-start pt-5",
        }}
      >
        {isLoading ? (
          <section className="d-flex justify-content-center align-items-center my-5">
            <div className="spinner-grow spinner-grow-sm text-success"></div>
          </section>
        ) : isError || error ? (
          <section className="d-flex justify-content-center align-items-center my-5">
            <div className="text-danger text-center">
              something went wrong...
            </div>
          </section>
        ) : (
          <GridSheet
            {...{
              portfolioData,
              mutate,
              isSaving,
              isError: isErrorSaving,
              isSuccess,
              invalidatePortfolio,
            }}
          />
        )}
      </CustomModal>

      {data.is_portfolio && (
        <section className={`${styles.wrapper}`}>
          {/*{!data.is_portfolio && <NoPortfolio />}*/}

          <>
            <div
              className={`${styles.header} position-sticky d-flex justify-content-between align-items-center py-1`}
            >
              <div className="d-flex align-items-center">
                <div className="ml-1 m-0">
                  <PortfolioSvg />
                </div>
                <div className={`${styles.view} mx-1`}>
                  Portfolio properties
                </div>

                {match && (
                  <div
                    className={`${styles.count} d-flex align-items-center justify-content-center ml-1 rounded rounded-circle`}
                  >
                    <span>{portfolioData?.length || 0}</span>
                  </div>
                )}
              </div>

              <div className="d-flex">
                <div
                  className={styles.reveal}
                  onClick={() => setShowGrid(true)}
                >
                  {RevealIcon}
                </div>

                <div
                  tabIndex={0}
                  onClick={() => setShow(true)}
                  className={`${styles.add} d-flex align-items-center ml-1`}
                >
                  {AddIcon}
                </div>
              </div>
            </div>

            {/*{data.portfolio_properties.length <= 0 ? (
              <NoPortfolio />
            ) : (
              <div
                className={`px-2 py-3 ${styles.portfolioName} text-capitalize`}
              >
                {data?.portfolio_properties?.map(
                  ({ property_name: name, porfolio_property_id: id }) => {
                    return (
                      <div className={`mb-3 ${styles.name}`} key={String(id)}>
                        {name}
                      </div>
                    );
                  }
                )}
              </div>
            )}*/}
          </>
        </section>
      )}
    </>
  );
};

export default TrackerPortfolio;
