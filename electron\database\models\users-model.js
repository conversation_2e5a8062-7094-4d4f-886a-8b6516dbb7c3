/**
 * Users model for database operations
 * Handles user-specific business logic and authentication data
 */

const BaseModel = require('./base-model');

class UsersModel extends BaseModel {
  constructor(db) {
    super(db, 'users');
    this.initializeUsersStatements();
  }

  /**
   * Initialize user-specific prepared statements
   */
  initializeUsersStatements() {
    this.preparedStatements.findByEmail = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE email = ?
    `);

    this.preparedStatements.findByRole = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE role = ? ORDER BY name ASC
    `);

    this.preparedStatements.updateLastLogin = this.db.prepare(`
      UPDATE ${this.tableName} 
      SET last_login = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
  }

  /**
   * Find user by email
   * @param {string} email - User email
   * @returns {Object|null} User or null
   */
  findByEmail(email) {
    try {
      return this.preparedStatements.findByEmail.get(email);
    } catch (error) {
      console.error('Error finding user by email:', error);
      throw error;
    }
  }

  /**
   * Find users by role
   * @param {string} role - User role
   * @returns {Array} Array of users
   */
  findByRole(role) {
    try {
      return this.preparedStatements.findByRole.all(role);
    } catch (error) {
      console.error('Error finding users by role:', error);
      throw error;
    }
  }

  /**
   * Update last login timestamp
   * @param {number} id - User ID
   */
  updateLastLogin(id) {
    try {
      this.preparedStatements.updateLastLogin.run(id);
    } catch (error) {
      console.error('Error updating last login:', error);
      throw error;
    }
  }

  /**
   * Create user with validation
   * @param {Object} userData - User data
   * @returns {Object} Created user
   */
  create(userData) {
    try {
      this.validateUserData(userData);
      return super.create(userData);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Validate user data
   * @param {Object} data - User data to validate
   */
  validateUserData(data) {
    const errors = [];

    if (!data.email || !this.isValidEmail(data.email)) {
      errors.push('Valid email is required');
    }

    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
      errors.push('Name is required');
    }

    if (errors.length > 0) {
      throw new Error(`Validation errors: ${errors.join(', ')}`);
    }
  }

  /**
   * Check if email is valid
   * @param {string} email - Email to validate
   * @returns {boolean} Whether email is valid
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

module.exports = UsersModel;
