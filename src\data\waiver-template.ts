export const templateModel = {
  id: 1,
  archive: false,
  type: {
    label: "Type:",
    name: "type",
    type: "buttons",
    options: ["Rep Exception", "Lender"],
    value: null,
  },
  waiver_exception: {
    name: "waiver_exception",
    label: "Waiver/Exceptions:",
    type: "text",
    value: "",
  },
  status: {
    name: "status",
    label: "Status:",
    type: "buttons",
    options: ["Approved", "Pending"],
    value: null,
  },
  comment: {
    name: "comment",
    label: "Waiver Comment",
    type: "text",
    value: "",
  },
};
