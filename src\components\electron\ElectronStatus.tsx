import React, { useState, useEffect } from 'react';
import { useElectron, useNetworkStatus } from '../../hooks/useElectron';
import { ElectronNotifications } from '../../utils/electronNotifications';
import styles from './ElectronStatus.module.scss';

interface ElectronStatusProps {
  className?: string;
  showVersion?: boolean;
  showNetworkStatus?: boolean;
  showSyncStatus?: boolean;
  compact?: boolean;
}

/**
 * Enhanced component that displays comprehensive Electron status information
 * Shows app version, network status, sync status, and environment info
 */
const ElectronStatus: React.FC<ElectronStatusProps> = ({
  className = '',
  showVersion = true,
  showNetworkStatus = true,
  showSyncStatus = true,
  compact = false,
}) => {
  const { isElectron, appVersion } = useElectron();
  const isOnline = useNetworkStatus();
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');
  const [previousOnlineStatus, setPreviousOnlineStatus] = useState(isOnline);

  // Handle network status changes with notifications
  useEffect(() => {
    if (previousOnlineStatus !== isOnline && previousOnlineStatus !== undefined) {
      const message = isOnline
        ? 'Connection restored. Data will sync automatically.'
        : 'Connection lost. Working in offline mode.';

      ElectronNotifications.show({
        title: isOnline ? 'Back Online' : 'Offline Mode',
        body: message,
        type: isOnline ? 'success' : 'warning',
        duration: 4000
      });

      // Simulate sync when coming back online
      if (isOnline && previousOnlineStatus === false) {
        setSyncStatus('syncing');
        setTimeout(() => {
          setSyncStatus('success');
          setLastSyncTime(new Date());
          setTimeout(() => setSyncStatus('idle'), 3000);
        }, 2000);
      }
    }
    setPreviousOnlineStatus(isOnline);
  }, [isOnline, previousOnlineStatus]);

  if (!isElectron) {
    return null; // Don't show anything in web environment
  }

  const getConnectionIcon = () => {
    if (isOnline) {
      return '🟢';
    }
    return '🔴';
  };

  const getSyncIcon = () => {
    switch (syncStatus) {
      case 'syncing':
        return '🔄';
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '⏸️';
    }
  };

  const getSyncStatusText = () => {
    switch (syncStatus) {
      case 'syncing':
        return 'Syncing...';
      case 'success':
        return 'Synced';
      case 'error':
        return 'Sync failed';
      default:
        return lastSyncTime ? `Last sync: ${lastSyncTime.toLocaleTimeString()}` : 'Not synced';
    }
  };

  if (compact) {
    return (
      <div className={`${styles.compactStatus} ${className}`}>
        <span className={styles.connectionIndicator}>
          {getConnectionIcon()}
        </span>
        {showSyncStatus && syncStatus !== 'idle' && (
          <span className={styles.syncIndicator}>
            {getSyncIcon()}
          </span>
        )}
      </div>
    );
  }

  return (
    <div className={`${styles.electronStatus} ${className}`}>
      {showVersion && appVersion && (
        <div className={styles.versionInfo}>
          <span className={styles.label}>Version:</span>
          <span className={styles.value}>v{appVersion}</span>
        </div>
      )}

      {showNetworkStatus && (
        <div className={styles.networkStatus}>
          <span className={styles.connectionIcon}>{getConnectionIcon()}</span>
          <span className={`${styles.statusText} ${isOnline ? styles.online : styles.offline}`}>
            {isOnline ? 'Online' : 'Offline'}
          </span>
        </div>
      )}

      {showSyncStatus && (
        <div className={styles.syncStatus}>
          <span className={styles.syncIcon}>{getSyncIcon()}</span>
          <span className={`${styles.syncText} ${styles[syncStatus]}`}>
            {getSyncStatusText()}
          </span>
        </div>
      )}
    </div>
  );
};

export default ElectronStatus;
