import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface User {
  id: string;
  email: string;
  username: string;
  role: string;
  permissions: string[];
}

interface AuthState {
  authenticated: boolean;
  user: User | null;
  mode: 'online' | 'offline' | null;
  isLoading: boolean;
  error: string | null;
  needsRefresh: boolean;
  canRefresh: boolean;
  expiresIn: number;
  isElectronAuth: boolean;
}

const initialState: AuthState = {
  authenticated: false,
  user: null,
  mode: null,
  isLoading: false,
  error: null,
  needsRefresh: false,
  canRefresh: false,
  expiresIn: 0,
  isElectronAuth: false
};

const authSlice = createSlice({
  name: "authSlice",
  initialState,
  reducers: {
    // Legacy action for backward compatibility
    handleAuthentication: (state, { payload }: PayloadAction<boolean>) => {
      state.authenticated = payload;
      if (!payload) {
        state.user = null;
        state.mode = null;
        state.error = null;
        state.needsRefresh = false;
        state.canRefresh = false;
        state.expiresIn = 0;
      }
    },

    // Enhanced actions for dual-expiration auth
    setAuthState: (state, { payload }: PayloadAction<Partial<AuthState>>) => {
      Object.assign(state, payload);
    },

    setUser: (state, { payload }: PayloadAction<User | null>) => {
      state.user = payload;
      state.authenticated = !!payload;
    },

    setAuthMode: (state, { payload }: PayloadAction<'online' | 'offline' | null>) => {
      state.mode = payload;
    },

    setLoading: (state, { payload }: PayloadAction<boolean>) => {
      state.isLoading = payload;
    },

    setError: (state, { payload }: PayloadAction<string | null>) => {
      state.error = payload;
    },

    clearError: (state) => {
      state.error = null;
    },

    setTokenStatus: (state, { payload }: PayloadAction<{
      needsRefresh: boolean;
      canRefresh: boolean;
      expiresIn: number;
    }>) => {
      state.needsRefresh = payload.needsRefresh;
      state.canRefresh = payload.canRefresh;
      state.expiresIn = payload.expiresIn;
    },

    setElectronAuth: (state, { payload }: PayloadAction<boolean>) => {
      state.isElectronAuth = payload;
    },

    logout: (state) => {
      state.authenticated = false;
      state.user = null;
      state.mode = null;
      state.error = null;
      state.needsRefresh = false;
      state.canRefresh = false;
      state.expiresIn = 0;
      state.isLoading = false;
    }
  },
});

export const {
  handleAuthentication,
  setAuthState,
  setUser,
  setAuthMode,
  setLoading,
  setError,
  clearError,
  setTokenStatus,
  setElectronAuth,
  logout
} = authSlice.actions;

export default authSlice.reducer;
