# Task ID: 4
# Title: Implement IPC Communication
# Status: pending
# Dependencies: 2
# Priority: medium
# Description: Set up inter-process communication between the main and renderer processes in Electron.
# Details:
Use Electron's IPC module to enable communication between the main and renderer processes. Implement basic message passing to test the setup.

# Test Strategy:
Test IPC communication by sending messages between the main and renderer processes and verifying their receipt.
