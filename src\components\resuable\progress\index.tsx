import React from "react";

import styles from "./index.module.scss";

interface IProgressBar {
  color: "success" | "primary" | "danger";
  progress: number;
  handleClick?: (arg: boolean) => void;
  rounded?: boolean;
  padding?: string;
  height?: string;
}

const ProgressBar = ({
  color,
  progress,
  handleClick,
  rounded = false,
  height = "24px",
}: IProgressBar) => {
  return (
    <section
      style={{ height }}
      className={`${styles.wrapper} ${
        rounded ? styles.roundFull : { borderRadius: "0" }
      } w-100`}
      onClick={() => handleClick && handleClick(true)}
    >
      <div
        style={{ width: `${progress === 0 ? 6 : progress}%` }}
        className={`
        ${+progress > 5 && "pl-5"}
        ${progress === 0 ? styles.zero : styles[color]}
        ${rounded ? styles.roundFull : styles.roundHalf}
          d-flex align-items-center justify-content-end h-100 text-center `}
      >
        <span className={`text-right ${progress > 5 ? "pr-3" : ""}`}>
          {progress}%
        </span>
      </div>
    </section>
  );
};

export default ProgressBar;
