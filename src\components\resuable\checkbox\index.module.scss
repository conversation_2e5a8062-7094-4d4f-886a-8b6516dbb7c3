@import "../../../../styles/_mixins.scss";

.wrapper {
  cursor: pointer;
  transition: 0.1s;
  @include font(0.89em, 500);

  &:hover {
    opacity: 0.85;
    @include font(0.85em, 600);
  }

  .label {
    color: rgba(24, 24, 24, 1);
    @include userSelect;

    &.green {
      color: var(--risk-primary);
      font-weight: bold;
    }
  }

  .field {
    accent-color: var(--risk-primary) !important;
  }
}
