@import "../../../../styles/_mixins.scss";

.pos {
  bottom: 0px;
  background-color: red !important;
}
.wrapper {
  background-color: var(--white);

  .btnCommon {
    width: 30%;
    border-radius: 2px;
    outline: 0;
    transition: 0.3s;
    @include font(0.9em, 600);
  }

  .buttonBorder {
    @extend .btnCommon;
    color: var(--risk-primary);
    border: 1px solid var(--risk-primary);
    background-color: #ffffff;
    @include font(0.9em, 600);
    transition: 0.3s;
    &:hover {
      opacity: 0.8;
    }
  }

  .button {
    @extend .btnCommon;
    border: 0;
    color: var(--white);
    background-color: var(--risk-primary);

    &:hover {
      opacity: 0.9;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
}
