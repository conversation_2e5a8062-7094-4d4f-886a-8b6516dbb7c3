---
description: Guidelines for creating and maintaining Augment rules to ensure consistency and effectiveness with Augment Agent's advanced capabilities.
globs: .augment/rules/*.mdc
alwaysApply: true
---

- **Required Rule Structure:**
  ```markdown
  ---
  description: Clear, one-line description of what the rule enforces
  globs: path/to/files/*.ext, other/path/**/*
  alwaysApply: boolean
  ---

  - **Main Points in Bold**
    - Sub-points with details
    - Examples and explanations
  ```

- **File References:**
  - Use `[filename](mdc:path/to/file)` ([filename](mdc:filename)) to reference files
  - Example: [electron.mdc](mdc:.augment/rules/electron.mdc) for rule references
  - Example: [schema.prisma](mdc:prisma/schema.prisma) for code references
  - Augment Agent can read files directly using codebase-retrieval and file tools

- **Code Examples:**
  - Use language-specific code blocks with clear DO/DON'T patterns
  ```typescript
  // ✅ DO: Show good examples
  const goodExample = true;
  
  // ❌ DON'T: Show anti-patterns
  const badExample = false;
  ```

- **Rule Content Guidelines:**
  - Start with high-level overview
  - Include specific, actionable requirements
  - Show examples of correct implementation
  - Reference existing code when possible
  - Keep rules DRY by referencing other rules
  - Leverage Augment Agent's codebase-retrieval capabilities

- **Rule Maintenance:**
  - Update rules when new patterns emerge
  - Add examples from actual codebase using Augment's codebase analysis
  - Remove outdated patterns
  - Cross-reference related rules
  - Use Augment's web search for latest best practices

- **Best Practices:**
  - Use bullet points for clarity
  - Keep descriptions concise
  - Include both DO and DON'T examples
  - Reference actual code over theoretical examples
  - Use consistent formatting across rules
  - Leverage Augment's superior code understanding for complex patterns

- **Augment-Specific Enhancements:**
  - Utilize Augment's world-leading codebase context engine for comprehensive analysis
  - Reference Augment's codebase-retrieval tool for understanding context across multiple files
  - Use Augment's web search capabilities for up-to-date information
  - Leverage Augment's file operations for precise code modifications
  - Take advantage of Augment's superior reasoning for rule validation
  - Use Augment's package management integration for dependency handling
  - Employ Augment's testing capabilities for comprehensive validation

- **Codebase Context Integration:**
  - Rules should leverage Augment's real-time codebase indexing
  - Reference patterns discovered through codebase-retrieval
  - Maintain consistency with existing architectural decisions
  - Consider cross-file dependencies and relationships
  - Factor in project-specific conventions and patterns

- **Advanced Pattern Recognition:**
  - Use Augment's context engine to identify emerging patterns
  - Analyze code relationships across the entire codebase
  - Detect architectural inconsistencies and suggest improvements
  - Identify opportunities for code reuse and standardization
  - Monitor for security and performance anti-patterns
