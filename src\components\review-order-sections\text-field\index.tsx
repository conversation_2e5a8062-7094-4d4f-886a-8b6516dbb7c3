import React from "react";

import { ErrorMessage, Field } from "formik";
import styles from "./index.module.css";

interface ITextFieldProps {
  label: string;
  name: string;
  type: string;
  placeholder?: string;
  occupy?: boolean;
  noLabel?: boolean;
  inLine?: boolean;
  formik: any;
  disabled?: any;
}

export default function TextField({
  label,
  name,
  type,
  placeholder,
  occupy,
  noLabel,
  inLine,
  formik,
  disabled,
  ...rest
}: ITextFieldProps) {
 

  return (
    <div className="d-flex align-items-center-center my-2 flex-column">
      <div className="d-flex">
        <div
          className={`d-flex align-self-center mr-2`}
          style={{
            flex: `${Boolean(occupy) ? 2 : 5}`,
          }}
        >
          {Boolean(label) && (
            <label className="">
              <span className={styles.label}>{label}</span>
            </label>
          )}
        </div>

        <div
          className=""
          style={{
            flex: `${Boolean(occupy) ? 10 : 7}`,
          }}
        >
          {type && (
            <Field
              name={name}
              type={type}
              placeholder={placeholder}
              className={`${styles.field} py-2 px-1`}
              {...rest}
              disabled={
                name === "flood_zone_specify"
                  ? formik?.values["flood_zone"]
                      ?.toLocaleLowerCase()
                      ?.includes("s") ||
                    formik?.values["flood_zone"]
                      ?.toLocaleLowerCase()
                      ?.includes("other")
                    ? false
                    : true
                  : false
              }
            />
          )}
        </div>
      </div>
      <div className="text-danger small">
        <ErrorMessage name={name} />
      </div>
    </div>
  );
}
