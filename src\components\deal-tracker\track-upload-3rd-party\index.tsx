import React from "react";

//import DragAndDrop from "../../resuable/dnd";
//import FileList from "../../resuable/file-list";
import LenderListUpload from "../../resuable/lender-list-upload";

import styles from "./index.module.scss";

interface ITrackerUploadThirdParty {
  formik?: any;
}

const data = [
  //{ id: 1, name: "Enterprise Insight Tech..." },
  //{ id: 2, name: "Enterprise Insight Tech..." },
  //{ id: 3, name: "Enterprise Insight Tech..." },
  //{ id: 5, name: "Enterprise Insight Tech..." },
  //{ id: 6, name: "Enterprise Insight Tech..." },
  //{ id: 7, name: "Enterprise Insight Tech..." },
  //{ id: 8, name: "Enterprise Insight Tech..." },
  //{ id: 8, name: "Enterprise Insight Tech..." },
  //{ id: 8, name: "Enterprise Insight Tech..." },
  //{ id: 8, name: "Enterprise Insight Tech..." },
];

const TrackerUploadThirdParty = ({ formik }: ITrackerUploadThirdParty) => {
  return (
    <section className={`${styles.wrapper} mb-4 bg-dark`}>
      <LenderListUpload
        {...{
          formik,
          data,
          label: "Lender/3rd party doc",
        }}
      />
    </section>
  );
};

export default TrackerUploadThirdParty;
