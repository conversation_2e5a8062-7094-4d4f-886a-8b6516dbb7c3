import React, { useState, useEffect } from 'react';
import { useNetworkStatus } from '../../hooks/useElectron';
import { ElectronNotifications } from '../../utils/electronNotifications';
import styles from './ConnectionStatusBar.module.scss';

interface ConnectionStatusBarProps {
  className?: string;
  showLastSync?: boolean;
  showDataStatus?: boolean;
  position?: 'top' | 'bottom' | 'inline';
}

interface SyncInfo {
  lastSyncTime: Date | null;
  pendingOperations: number;
  syncStatus: 'idle' | 'syncing' | 'success' | 'error';
  errorMessage?: string;
}

/**
 * Connection status bar component for displaying network and sync status
 * Designed to be placed in headers, navigation areas, or as a floating bar
 */
const ConnectionStatusBar: React.FC<ConnectionStatusBarProps> = ({
  className = '',
  showLastSync = true,
  showDataStatus = true,
  position = 'inline'
}) => {
  const isOnline = useNetworkStatus();
  const [syncInfo, setSyncInfo] = useState<SyncInfo>({
    lastSyncTime: null,
    pendingOperations: 0,
    syncStatus: 'idle'
  });
  const [isVisible, setIsVisible] = useState(true);

  // Simulate sync operations and data status
  useEffect(() => {
    if (isOnline && syncInfo.syncStatus === 'idle' && syncInfo.pendingOperations > 0) {
      // Auto-sync when coming online with pending operations
      setSyncInfo(prev => ({ ...prev, syncStatus: 'syncing' }));
      
      setTimeout(() => {
        setSyncInfo(prev => ({
          ...prev,
          syncStatus: 'success',
          lastSyncTime: new Date(),
          pendingOperations: 0
        }));
        
        setTimeout(() => {
          setSyncInfo(prev => ({ ...prev, syncStatus: 'idle' }));
        }, 3000);
      }, 2000);
    }
  }, [isOnline, syncInfo.syncStatus, syncInfo.pendingOperations]);

  // Simulate pending operations when offline
  useEffect(() => {
    if (!isOnline) {
      const interval = setInterval(() => {
        setSyncInfo(prev => ({
          ...prev,
          pendingOperations: prev.pendingOperations + Math.floor(Math.random() * 2)
        }));
      }, 10000); // Add pending operations every 10 seconds when offline

      return () => clearInterval(interval);
    }
  }, [isOnline]);

  const getConnectionStatus = () => {
    if (isOnline) {
      return {
        icon: '🟢',
        text: 'Online',
        className: styles.online
      };
    }
    return {
      icon: '🔴',
      text: 'Offline',
      className: styles.offline
    };
  };

  const getSyncStatus = () => {
    switch (syncInfo.syncStatus) {
      case 'syncing':
        return {
          icon: '🔄',
          text: 'Syncing...',
          className: styles.syncing
        };
      case 'success':
        return {
          icon: '✅',
          text: 'Synced',
          className: styles.success
        };
      case 'error':
        return {
          icon: '❌',
          text: 'Sync failed',
          className: styles.error
        };
      default:
        return {
          icon: '⏸️',
          text: syncInfo.lastSyncTime 
            ? `Last sync: ${syncInfo.lastSyncTime.toLocaleTimeString()}`
            : 'Not synced',
          className: styles.idle
        };
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => setIsVisible(true), 5000); // Show again after 5 seconds
  };

  const handleRetrySync = () => {
    if (isOnline) {
      setSyncInfo(prev => ({ ...prev, syncStatus: 'syncing' }));
      setTimeout(() => {
        setSyncInfo(prev => ({
          ...prev,
          syncStatus: 'success',
          lastSyncTime: new Date(),
          pendingOperations: 0
        }));
        setTimeout(() => {
          setSyncInfo(prev => ({ ...prev, syncStatus: 'idle' }));
        }, 3000);
      }, 1500);
    }
  };

  if (!isVisible) {
    return null;
  }

  const connectionStatus = getConnectionStatus();
  const syncStatus = getSyncStatus();

  return (
    <div className={`${styles.statusBar} ${styles[position]} ${className}`}>
      {/* Connection Status */}
      <div className={`${styles.statusItem} ${connectionStatus.className}`}>
        <span className={styles.icon}>{connectionStatus.icon}</span>
        <span className={styles.text}>{connectionStatus.text}</span>
      </div>

      <div className={styles.divider} />

      {/* Sync Status */}
      {showLastSync && (
        <>
          <div className={`${styles.statusItem} ${syncStatus.className}`}>
            <span className={`${styles.icon} ${syncInfo.syncStatus === 'syncing' ? styles.spinning : ''}`}>
              {syncStatus.icon}
            </span>
            <span className={styles.text}>{syncStatus.text}</span>
          </div>
          
          {syncInfo.syncStatus === 'error' && (
            <button 
              className={styles.retryButton}
              onClick={handleRetrySync}
              disabled={!isOnline}
            >
              Retry
            </button>
          )}
        </>
      )}

      {/* Data Status */}
      {showDataStatus && syncInfo.pendingOperations > 0 && (
        <>
          <div className={styles.divider} />
          <div className={`${styles.statusItem} ${styles.pending}`}>
            <span className={styles.icon}>📝</span>
            <span className={styles.text}>
              {syncInfo.pendingOperations} pending
            </span>
          </div>
        </>
      )}

      {/* Offline Mode Info */}
      {!isOnline && (
        <>
          <div className={styles.divider} />
          <div className={styles.offlineInfo}>
            <span className={styles.infoText}>
              Changes will sync when connection is restored
            </span>
          </div>
        </>
      )}

      {/* Dismiss Button for floating bars */}
      {position !== 'inline' && (
        <button 
          className={styles.dismissButton}
          onClick={handleDismiss}
          aria-label="Dismiss status bar"
        >
          ×
        </button>
      )}
    </div>
  );
};

export default ConnectionStatusBar;
