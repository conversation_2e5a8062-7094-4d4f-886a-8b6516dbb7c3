import React from "react";

interface IStatusButtonProps {
  status: boolean | string | undefined;
}

const statusContainer = (status: string | boolean | any) => {
  if (status) {
    switch (status?.toLowerCase()) {
      case "active":
        return "active boxRadius";
      case "archive":
        return "archive boxRadius";
      case "on-hold":
        return "archive boxRadius";
      case "closed":
        return "onHold boxRadius";
      case "paid":
        return "paid boxRadius";
      default:
        return "bg-white text-dark";
    }
  }
};

const statusDot = (status: string | boolean | any) => {
  if (status) {
    switch (status?.toLowerCase()) {
      case "active":
        return "dot dotActive";
      case "archive":
        return "dot dotArchive";
      case "on-hold":
        return "dot dotOnHold";
      case "closed":
        return "dot dotClosed";
      case "paid":
        return "dot dotPaid";
      default:
        return "bg-white text-dark";
    }
  }
};

export default function StatusButton({ status }: IStatusButtonProps) {
  return (
    <div className="">
      {!!status && (
        <span
          className={`d-flex align-items-center px-2  ${statusContainer(
            status
          )}`}
        >
          <aside className={`dot dotActive mr-1 ${statusDot(status)}`}></aside>
          {status}
        </span>
      )}
    </div>
  );
}
