import React from "react";

import CancelSvg from "../../../assets/svgs/cancel";

import styles from "./index.module.scss";

import { useHistory, useParams } from "react-router";

import TrackerDealStatus from "../tracker-deal-status";

import boxIconInactive from "../../../assets/images/box.png";
import { useMutation } from "react-query";
import { axiosInstanceCopyFolder } from "./../../../axios-config/index";
import { toast } from "react-toastify";
import { getRefreshTokenSequence } from "../../../hooks/useBoxConnect";
import { saveSession } from "../../../utils/readLocalStorage";

interface ITrackerHeader {
  name: any;
  status: string;
  lender: string;
  isBoxFolderId: boolean;
  orderNumber: string;
  data: { [key: string]: any };
}

const $status = "closed";
const shouldNotDisplay = (status: string) => status.toLowerCase() === $status;

const TrackerHeader = ({
  name,
  status,
  lender,
  isBoxFolderId,
  orderNumber,
  data,
}: ITrackerHeader) => {
  const history = useHistory();

  const params: any = useParams();

  const handleCopy = async (payload: { [key: string]: any }) => {
    try {
      await axiosInstanceCopyFolder.post("/copy_folder_structure", payload);
    } catch (err) {
      throw new Error(err);
    }
  };

  const extractAndComposeClosingDate = () => {
    const key = "loan_closing_date";

    try {
      const closingDateValue = data?.fields.find(
        (field: { [key: string]: any }) => field.name === key
      ).value;

      if (!closingDateValue) {
        const [m, d] = new Date().toLocaleDateString("en-US").split("/");

        const month = m?.startsWith("0") ? m?.slice(1) : m;
        const day = d?.startsWith("0") ? d?.slice(1) : d;

        return [month, day].join(".");
      }

      const [, m, d] = closingDateValue.split("-");

      const month = m?.startsWith("0") ? m?.slice(1) : m;
      const day = d?.startsWith("0") ? d?.slice(1) : d;

      return [month, day].join(".");
    } catch (err: any) {
      return null;
    }
  };

  const handleSetupBoxFolder = () => {
    if (!params?.id) return;

    getRefreshTokenSequence(
      () => {
        toast.error("Connect to Box, before setting up a deal folder", {
          position: "top-right",
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: false,
          draggable: true,
          progress: undefined,
        });

        window.location.reload();

        return;
      },
      () => {
        toast.error("Failed to initialize, try again.", {
          position: "top-right",
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: false,
          draggable: true,
          progress: undefined,
        });
        return;
      }
    )((response: any) => {
      const accessToken = response?.data?.access_token;

      const loan_closing_date = extractAndComposeClosingDate();

      const payload = {
        copy_folder_parent: data.mortgage_lender,
        new_folder_parent: "Active",
        deal_folder_name: `${data?.deal_name} - ${loan_closing_date}`,
        is_portfolio: data?.is_portfolio ? "yes" : "no",
        access_token: accessToken,
        id: params?.id,
      };

      mutate(payload);

      saveSession(process.env.REACT_APP_BOX_TOKEN, response?.data);
    });
  };

  const { mutate, isLoading, isError, isSuccess } = useMutation(handleCopy, {
    onSuccess: () => {
      toast.success("Great! Deal folder setup completed!");

      setTimeout(() => {
        window.location.reload();
      }, 200);
    },
    onError: () => {
      toast.error("Oops! Deal folder setup failed!");
    },
  });

  return (
    <section
      className={`${styles.wrapper} position-sticky d-flex justify-content-between align-items-center py-0`}
    >
      <div className={`d-flex align-items-center`}>
        <div
          className={`${styles.cancel} py-3 px-3`}
          onClick={() => history.push("/deals")}
        >
          <CancelSvg />
        </div>

        <div className={`${styles.deal} mx-3`}>Deal Tracker</div>

        {!!orderNumber && <div className={styles.orderNumber}>{orderNumber}</div>}

        {!!name && <div className={styles.dealDesc}>{name}</div>}

        {!!lender && <div className={`${styles.lender} ml-2`}>{lender}</div>}

        {!!status && <TrackerDealStatus status={status} customStyles="ml-3" />}

        <div>
          {shouldNotDisplay(data?.deal_status || "")
            ? null
            : !isBoxFolderId && (
                <div className="d-flex align-items-center mx-3">
                  <img
                    src={boxIconInactive}
                    alt="set up deal folder"
                    className="img-fluid"
                  />
                  {isSuccess ? null : isLoading ? (
                    <div className={`${styles.setUpText} ${styles.anim}`}>
                      Setting up deal folder...
                    </div>
                  ) : (
                    <button
                      className={`${styles.setUpButton}`}
                      onClick={handleSetupBoxFolder}
                    >
                      {isError ? "Try Again" : "Set up deal folder"}
                    </button>
                  )}
                </div>
              )}
        </div>
      </div>

      <button
        onClick={() => history.push(`/deals/${params?.id}`)}
        type="button"
        className={`${styles.button} px-5 py-2`}
      >
        Manage
      </button>
    </section>
  );
};

export default TrackerHeader;
