const path = require('path');
const webpack = require('webpack');

// Determine if we're in development mode
const isDevelopment = process.env.NODE_ENV !== 'production';

// Common configuration for both main and renderer processes
const commonConfig = {
  mode: isDevelopment ? 'development' : 'production',
  devtool: isDevelopment ? 'source-map' : false,
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    fallback: {
      // Electron main process doesn't need these polyfills
      "crypto": false,
      "stream": false,
      "buffer": false,
      "util": false,
      "path": false,
      "fs": false,
      "os": false
    }
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', { targets: { electron: '28' } }],
              '@babel/preset-react',
              '@babel/preset-typescript'
            ],
            plugins: [
              '@babel/plugin-proposal-class-properties',
              '@babel/plugin-transform-object-rest-spread'
            ]
          }
        }
      },
      {
        test: /\.node$/,
        use: 'node-loader'
      }
    ]
  }
};

// Main process configuration
const mainConfig = {
  ...commonConfig,
  target: 'electron-main',
  entry: './electron/main/main.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'main.js'
  },
  externals: {
    // Don't bundle native modules
    'better-sqlite3': 'commonjs better-sqlite3',
    'electron': 'commonjs electron',
    'electron-is-dev': 'commonjs electron-is-dev',
    'electron-reload': 'commonjs electron-reload',
    'electron-window-state': 'commonjs electron-window-state',
    'electron-updater': 'commonjs electron-updater'
  },
  node: {
    __dirname: false,
    __filename: false
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
    })
  ]
};

// Preload script configuration
const preloadConfig = {
  ...commonConfig,
  target: 'electron-preload',
  entry: './electron/preload/preload.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'preload.js'
  },
  externals: {
    'electron': 'commonjs electron'
  },
  node: {
    __dirname: false,
    __filename: false
  }
};

// Renderer process configuration (optional - React app uses react-scripts by default)
const rendererConfig = {
  ...commonConfig,
  target: 'electron-renderer',
  entry: './src/index.tsx',
  output: {
    path: path.resolve(__dirname, 'dist/renderer'),
    filename: 'renderer.js',
    publicPath: './'
  },
  module: {
    ...commonConfig.module,
    rules: [
      ...commonConfig.module.rules,
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.scss$/,
        use: ['style-loader', 'css-loader', 'sass-loader']
      },
      {
        test: /\.(png|jpe?g|gif|svg|ico)$/,
        type: 'asset/resource',
        generator: {
          filename: 'assets/images/[name][ext]'
        }
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/,
        type: 'asset/resource',
        generator: {
          filename: 'assets/fonts/[name][ext]'
        }
      }
    ]
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      'process.env.ELECTRON_IS_DEV': JSON.stringify(isDevelopment)
    })
  ],
  externals: {
    // Don't bundle Electron in renderer
    'electron': 'commonjs electron'
  }
};

// Export configurations based on environment
module.exports = (env, argv) => {
  const target = env?.target || 'all';

  switch (target) {
    case 'main':
      return mainConfig;
    case 'preload':
      return preloadConfig;
    case 'renderer':
      return rendererConfig;
    case 'all':
    default:
      return [mainConfig, preloadConfig];
  }
};
