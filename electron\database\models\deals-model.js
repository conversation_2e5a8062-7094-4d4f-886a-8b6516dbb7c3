/**
 * Deals model for database operations
 * Handles deal-specific business logic and queries
 */

const BaseModel = require('./base-model');

class DealsModel extends BaseModel {
  constructor(db) {
    super(db, 'deals');
    this.initializeDealsStatements();
  }

  /**
   * Initialize deal-specific prepared statements
   */
  initializeDealsStatements() {
    // Legacy flat field queries (for backward compatibility)
    this.preparedStatements.findByStatus = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE status = ? ORDER BY created_at DESC
    `);

    this.preparedStatements.findByDateRange = this.db.prepare(`
      SELECT * FROM ${this.tableName}
      WHERE loan_closing_date BETWEEN ? AND ?
      ORDER BY loan_closing_date ASC
    `);

    this.preparedStatements.findUpcoming = this.db.prepare(`
      SELECT * FROM ${this.tableName}
      WHERE reviewed_needed_date >= date('now')
      ORDER BY reviewed_needed_date ASC
    `);

    this.preparedStatements.findOverdue = this.db.prepare(`
      SELECT * FROM ${this.tableName}
      WHERE reviewed_needed_date < date('now') AND status != 'Closed'
      ORDER BY reviewed_needed_date ASC
    `);

    this.preparedStatements.updateStatus = this.db.prepare(`
      UPDATE ${this.tableName}
      SET status = ?, updated_at = CURRENT_TIMESTAMP, sync_status = 'pending'
      WHERE id = ?
    `);

    // JSON-based queries
    this.preparedStatements.findByOrderId = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE order_id = ?
    `);

    this.preparedStatements.findByJsonStatus = this.db.prepare(`
      SELECT * FROM ${this.tableName}
      WHERE json_extract(deal, '$.status') = ?
      ORDER BY created_at DESC
    `);

    this.preparedStatements.updateDealJson = this.db.prepare(`
      UPDATE ${this.tableName}
      SET deal = ?, updated_at = CURRENT_TIMESTAMP, sync_status = 'pending'
      WHERE id = ?
    `);

    this.preparedStatements.updateTrackerData = this.db.prepare(`
      UPDATE ${this.tableName}
      SET tracker_data = ?, updated_at = CURRENT_TIMESTAMP, sync_status = 'pending'
      WHERE id = ?
    `);

    this.preparedStatements.updateBothJsonFields = this.db.prepare(`
      UPDATE ${this.tableName}
      SET deal = ?, tracker_data = ?, updated_at = CURRENT_TIMESTAMP, sync_status = 'pending'
      WHERE id = ?
    `);
  }

  /**
   * Find deals by status
   * @param {string} status - Deal status
   * @returns {Array} Array of deals
   */
  findByStatus(status) {
    try {
      return this.preparedStatements.findByStatus.all(status);
    } catch (error) {
      console.error('Error finding deals by status:', error);
      throw error;
    }
  }

  /**
   * Find deals by date range
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @returns {Array} Array of deals
   */
  findByDateRange(startDate, endDate) {
    try {
      return this.preparedStatements.findByDateRange.all(startDate, endDate);
    } catch (error) {
      console.error('Error finding deals by date range:', error);
      throw error;
    }
  }

  /**
   * Find upcoming deals (review needed date >= today)
   * @returns {Array} Array of upcoming deals
   */
  findUpcoming() {
    try {
      return this.preparedStatements.findUpcoming.all();
    } catch (error) {
      console.error('Error finding upcoming deals:', error);
      throw error;
    }
  }

  /**
   * Find overdue deals (review needed date < today and not closed)
   * @returns {Array} Array of overdue deals
   */
  findOverdue() {
    try {
      return this.preparedStatements.findOverdue.all();
    } catch (error) {
      console.error('Error finding overdue deals:', error);
      throw error;
    }
  }

  /**
   * Update deal status
   * @param {number} id - Deal ID
   * @param {string} status - New status
   * @returns {Object} Updated deal
   */
  updateStatus(id, status) {
    try {
      const result = this.preparedStatements.updateStatus.run(status, id);

      if (result.changes === 0) {
        throw new Error(`No deal found with ID: ${id}`);
      }

      // Queue operation for sync
      this.queueOperation('UPDATE', id, { status });

      return this.findById(id);
    } catch (error) {
      console.error('Error updating deal status:', error);
      throw error;
    }
  }

  /**
   * Create deal with validation
   * @param {Object} dealData - Deal data
   * @returns {Object} Created deal
   */
  create(dealData) {
    try {
      // Validate required fields
      this.validateDealData(dealData);

      // Set default status if not provided
      if (!dealData.status) {
        dealData.status = 'Active';
      }

      return super.create(dealData);
    } catch (error) {
      console.error('Error creating deal:', error);
      throw error;
    }
  }

  /**
   * Update deal with validation
   * @param {number} id - Deal ID
   * @param {Object} dealData - Updated deal data
   * @returns {Object} Updated deal
   */
  update(id, dealData) {
    try {
      // Validate data if provided
      if (Object.keys(dealData).length > 0) {
        this.validateDealData(dealData, false); // Partial validation for updates
      }

      return super.update(id, dealData);
    } catch (error) {
      console.error('Error updating deal:', error);
      throw error;
    }
  }

  /**
   * Validate deal data
   * @param {Object} data - Deal data to validate
   * @param {boolean} isCreate - Whether this is for creation (requires all fields)
   */
  validateDealData(data, isCreate = true) {
    const errors = [];

    // Required fields for creation
    if (isCreate) {
      if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
        errors.push('Deal name is required');
      }
    }

    // Validate status if provided
    if (data.status) {
      const validStatuses = ['Active', 'On-hold', 'Closed', 'Dead', 'Paid', 'Archive'];
      if (!validStatuses.includes(data.status)) {
        errors.push(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
      }
    }

    // Validate dates if provided
    if (data.loan_closing_date && !this.isValidDate(data.loan_closing_date)) {
      errors.push('Invalid loan closing date format');
    }

    if (data.reviewed_needed_date && !this.isValidDate(data.reviewed_needed_date)) {
      errors.push('Invalid review needed date format');
    }

    // Validate loan amounts if provided
    if (data.mortgage_loan_amount && !this.isValidAmount(data.mortgage_loan_amount)) {
      errors.push('Invalid mortgage loan amount');
    }

    if (errors.length > 0) {
      throw new Error(`Validation errors: ${errors.join(', ')}`);
    }
  }

  /**
   * Check if date string is valid
   * @param {string} dateString - Date string to validate
   * @returns {boolean} Whether date is valid
   */
  isValidDate(dateString) {
    if (!dateString) return false;
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  /**
   * Check if amount string is valid
   * @param {string} amount - Amount string to validate
   * @returns {boolean} Whether amount is valid
   */
  isValidAmount(amount) {
    if (!amount) return true; // Optional field
    // Remove commas and dollar signs, then check if it's a valid number
    const cleanAmount = amount.replace(/[$,]/g, '');
    return !isNaN(parseFloat(cleanAmount)) && isFinite(cleanAmount);
  }

  /**
   * Get deals dashboard data
   * @returns {Object} Dashboard statistics
   */
  getDashboardData() {
    try {
      const totalDeals = this.count();
      const activeDeals = this.findByStatus('Active').length;
      const upcomingDeals = this.findUpcoming().length;
      const overdueDeals = this.findOverdue().length;

      // Get recent deals (last 10)
      const recentDeals = this.findAll({ limit: 10 });

      return {
        totalDeals,
        activeDeals,
        upcomingDeals,
        overdueDeals,
        recentDeals
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  /**
   * Search deals by multiple criteria
   * @param {Object} criteria - Search criteria
   * @returns {Array} Matching deals
   */
  searchDeals(criteria) {
    try {
      const { searchTerm, status, dateFrom, dateTo, propertyType } = criteria;

      let query = `SELECT * FROM ${this.tableName} WHERE 1=1`;
      const params = [];

      // Text search
      if (searchTerm) {
        query += ` AND (name LIKE ? OR borrower_sponsor_name LIKE ? OR property_name LIKE ?)`;
        const searchPattern = `%${searchTerm}%`;
        params.push(searchPattern, searchPattern, searchPattern);
      }

      // Status filter
      if (status) {
        query += ` AND status = ?`;
        params.push(status);
      }

      // Date range filter
      if (dateFrom) {
        query += ` AND loan_closing_date >= ?`;
        params.push(dateFrom);
      }

      if (dateTo) {
        query += ` AND loan_closing_date <= ?`;
        params.push(dateTo);
      }

      // Property type filter
      if (propertyType) {
        query += ` AND property_type = ?`;
        params.push(propertyType);
      }

      query += ` ORDER BY created_at DESC`;

      const stmt = this.db.prepare(query);
      return stmt.all(...params);
    } catch (error) {
      console.error('Error searching deals:', error);
      throw error;
    }
  }

  // ===== JSON-BASED METHODS =====

  /**
   * Find deal by order ID
   * @param {string} orderId - Order ID
   * @returns {Object|null} Deal or null if not found
   */
  findByOrderId(orderId) {
    try {
      const deal = this.preparedStatements.findByOrderId.get(orderId);
      return deal ? this.parseDealJson(deal) : null;
    } catch (error) {
      console.error('Error finding deal by order ID:', error);
      throw error;
    }
  }

  /**
   * Create deal with JSON structure
   * @param {Object} dealData - Deal data with JSON fields
   * @returns {Object} Created deal
   */
  createWithJson(dealData) {
    try {
      const { deal, tracker_data, ...flatFields } = dealData;

      // Validate required fields
      this.validateDealData(flatFields);

      // Prepare JSON fields
      const dealJson = deal ? JSON.stringify(deal) : '{}';
      const trackerJson = tracker_data ? JSON.stringify(tracker_data) : '{}';

      // Set order_id from deal JSON if available
      const orderId = deal?.order_id || flatFields.server_id || null;

      const recordData = {
        ...flatFields,
        deal: dealJson,
        tracker_data: trackerJson,
        order_id: orderId,
        status: deal?.status || flatFields.status || 'Active'
      };

      return super.create(recordData);
    } catch (error) {
      console.error('Error creating deal with JSON:', error);
      throw error;
    }
  }

  /**
   * Update deal JSON field
   * @param {number} id - Deal ID
   * @param {Object} dealData - Deal JSON data
   * @returns {Object} Updated deal
   */
  updateDealJson(id, dealData) {
    try {
      const dealJson = JSON.stringify(dealData);
      const result = this.preparedStatements.updateDealJson.run(dealJson, id);

      if (result.changes === 0) {
        throw new Error(`No deal found with ID: ${id}`);
      }

      // Queue operation for sync
      this.queueOperation('UPDATE', id, { deal: dealData });

      return this.findById(id);
    } catch (error) {
      console.error('Error updating deal JSON:', error);
      throw error;
    }
  }

  /**
   * Update tracker data JSON field
   * @param {number} id - Deal ID
   * @param {Object} trackerData - Tracker data
   * @returns {Object} Updated deal
   */
  updateTrackerData(id, trackerData) {
    try {
      const trackerJson = JSON.stringify(trackerData);
      const result = this.preparedStatements.updateTrackerData.run(trackerJson, id);

      if (result.changes === 0) {
        throw new Error(`No deal found with ID: ${id}`);
      }

      // Queue operation for sync
      this.queueOperation('UPDATE', id, { tracker_data: trackerData });

      return this.findById(id);
    } catch (error) {
      console.error('Error updating tracker data:', error);
      throw error;
    }
  }

  /**
   * Update both deal and tracker data JSON fields
   * @param {number} id - Deal ID
   * @param {Object} dealData - Deal JSON data
   * @param {Object} trackerData - Tracker data
   * @returns {Object} Updated deal
   */
  updateBothJsonFields(id, dealData, trackerData) {
    try {
      const dealJson = JSON.stringify(dealData);
      const trackerJson = JSON.stringify(trackerData);

      const result = this.preparedStatements.updateBothJsonFields.run(dealJson, trackerJson, id);

      if (result.changes === 0) {
        throw new Error(`No deal found with ID: ${id}`);
      }

      // Queue operation for sync
      this.queueOperation('UPDATE', id, { deal: dealData, tracker_data: trackerData });

      return this.findById(id);
    } catch (error) {
      console.error('Error updating both JSON fields:', error);
      throw error;
    }
  }

  /**
   * Merge tracker data with existing data
   * @param {number} id - Deal ID
   * @param {Object} newTrackerData - New tracker data to merge
   * @returns {Object} Updated deal
   */
  mergeTrackerData(id, newTrackerData) {
    try {
      const deal = this.findById(id);
      if (!deal) {
        throw new Error(`No deal found with ID: ${id}`);
      }

      // Parse existing tracker data
      let existingTrackerData = {};
      try {
        existingTrackerData = JSON.parse(deal.tracker_data || '{}');
      } catch (error) {
        console.warn('Invalid JSON in existing tracker_data, starting fresh');
      }

      // Merge with new data
      const mergedTrackerData = { ...existingTrackerData, ...newTrackerData };

      return this.updateTrackerData(id, mergedTrackerData);
    } catch (error) {
      console.error('Error merging tracker data:', error);
      throw error;
    }
  }

  /**
   * Parse deal JSON fields
   * @param {Object} deal - Raw deal data from database
   * @returns {Object} Deal with parsed JSON fields
   */
  parseDealJson(deal) {
    const parsedDeal = { ...deal };

    // Parse deal JSON
    if (deal.deal) {
      try {
        parsedDeal.dealData = JSON.parse(deal.deal);
      } catch (error) {
        console.warn(`Invalid JSON in deal field for deal ${deal.id}:`, deal.deal);
        parsedDeal.dealData = {};
      }
    }

    // Parse tracker data JSON
    if (deal.tracker_data) {
      try {
        parsedDeal.trackerData = JSON.parse(deal.tracker_data);
      } catch (error) {
        console.warn(`Invalid JSON in tracker_data field for deal ${deal.id}:`, deal.tracker_data);
        parsedDeal.trackerData = {};
      }
    }

    return parsedDeal;
  }

  /**
   * Override findById to include JSON parsing
   * @param {number} id - Deal ID
   * @returns {Object|null} Deal with parsed JSON or null
   */
  findById(id) {
    try {
      const deal = super.findById(id);
      return deal ? this.parseDealJson(deal) : null;
    } catch (error) {
      console.error('Error finding deal by ID:', error);
      throw error;
    }
  }

  /**
   * Override findAll to include JSON parsing
   * @param {Object} options - Query options
   * @returns {Array} Deals with parsed JSON
   */
  findAll(options = {}) {
    try {
      const deals = super.findAll(options);
      return deals.map(deal => this.parseDealJson(deal));
    } catch (error) {
      console.error('Error finding all deals:', error);
      throw error;
    }
  }

  /**
   * Search deals using JSON fields
   * @param {Object} criteria - Search criteria including JSON field searches
   * @returns {Array} Matching deals
   */
  searchDealsJson(criteria) {
    try {
      const { searchTerm, status, dateFrom, dateTo, propertyType, jsonFields } = criteria;

      let query = `SELECT * FROM ${this.tableName} WHERE 1=1`;
      const params = [];

      // Text search in both flat and JSON fields
      if (searchTerm) {
        query += ` AND (
          name LIKE ? OR
          borrower_sponsor_name LIKE ? OR
          property_name LIKE ? OR
          json_extract(deal, '$.name') LIKE ? OR
          json_extract(deal, '$.property_name') LIKE ?
        )`;
        const searchPattern = `%${searchTerm}%`;
        params.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
      }

      // Status filter (check both flat and JSON fields)
      if (status) {
        query += ` AND (status = ? OR json_extract(deal, '$.status') = ?)`;
        params.push(status, status);
      }

      // JSON field searches
      if (jsonFields) {
        for (const [field, value] of Object.entries(jsonFields)) {
          query += ` AND json_extract(deal, '$.${field}') = ?`;
          params.push(value);
        }
      }

      // Date range filter
      if (dateFrom) {
        query += ` AND (loan_closing_date >= ? OR json_extract(deal, '$.loan_closing_date') >= ?)`;
        params.push(dateFrom, dateFrom);
      }

      if (dateTo) {
        query += ` AND (loan_closing_date <= ? OR json_extract(deal, '$.loan_closing_date') <= ?)`;
        params.push(dateTo, dateTo);
      }

      query += ` ORDER BY created_at DESC`;

      const stmt = this.db.prepare(query);
      const deals = stmt.all(...params);
      return deals.map(deal => this.parseDealJson(deal));
    } catch (error) {
      console.error('Error searching deals with JSON:', error);
      throw error;
    }
  }
}

module.exports = DealsModel;
