import React from "react";

import TrackerFormWrapper from "../../../resuable/tracker-form-wrapper";
import MapTrackerForm from "../../map-tracker";

interface ITrackerPropertySummary {
  formik?: any;
  data?: any;
  selectedProperty: any;
}

const TrackerSummaryProperty = ({
  formik,
  data,
  selectedProperty,
}: ITrackerPropertySummary) => {
  if (!selectedProperty) {
    const id = data?.portfolio_properties[0]?.portfolio_property_id;

    return (
      <TrackerFormWrapper>
        <MapTrackerForm
          {...{
            data: data?.property_fields?.filter(
              (items) => items.porfolio_property_id === id
            ),
            formik,
          }}
        />
      </TrackerFormWrapper>
    );
  }
  return (
    <TrackerFormWrapper>
      <MapTrackerForm
        {...{
          data: data?.property_fields.filter(
            (v) =>
              v.porfolio_property_id === selectedProperty?.porfolio_property_id
          ),
          formik,
        }}
      />
    </TrackerFormWrapper>
  );
};

export default TrackerSummaryProperty;
