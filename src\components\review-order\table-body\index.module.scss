.tableWrapper {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .tableInnerWrapper {
    border-collapse: separate;
    border-spacing: 0 0.3em;
    .tableHeader {
      background: #ffffff;
      color: #a1aaa6;
      font-size: 0.9em;
      font-weight: bold;

      > tr > td:first-child {
        border-bottom-left-radius: 2px;
        border-top-left-radius: 2px;
      }

      > tr > td:last-child {
        border-bottom-right-radius: 2px;
        border-top-right-radius: 2px;
      }
    }

    .tableBody {
      &:hover {
        border: 3px solid var(--risk-primary);
      }
      > tr {
        cursor: pointer;
        background-color: #ffffff;
        margin: 20px;
        transition: 0.2s;
        &:hover {
          > td:first-child {
            transition: 0.2s;

            border-left: 1px solid var(--risk-primary);
          }
          > td:last-child {
            transition: 0.2s;
            border-right: 1px solid var(--risk-primary);
          }

          box-shadow: 0.05px -0.05px 0.05px 0.5px var(--risk-primary);
          border: 3px solid var(--risk-primary) !important;
        }
      }
    }
  }
}
