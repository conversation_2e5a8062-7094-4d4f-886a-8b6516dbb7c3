import React from "react";

import styles from "./index.module.scss";

import { waiverSummary } from "../../../../data/tracker-form";

import { extractWaiverFields } from "../../../../utils/extractWaivers";

import TrackerFormWrapper from "../../../resuable/tracker-form-wrapper";
import MapTrackerForm from "../../map-tracker";

import TrackerAddSvg from "../../../svgs/TrackerAdd";
import CancelSvg from "../../../svgs/CancelSvg";
import ArchiveSvg from "../../../svgs/Archive";

interface IWaiverSummary {
  formik?: any;
  data?: any;
}

const generateWaiverKeys = (id: number) => {
  const exceptionKey = `exception_${id}`;

  const typeKey = `type_${id}`;

  const statusKey = `status_${id}`;

  const commentKey = `comment_${id}`;

  return { exceptionKey, typeKey, statusKey, commentKey };
};

const WaiverSummary = ({ formik, data }: IWaiverSummary) => {
  const [waivers, setWaivers] = React.useState<
    Array<{
      id: number;
      data: Array<any>;
    }>
  >([]);

  const [id, setId] = React.useState(new Date().getTime());

  const waiverException = {
    ...waiverSummary[0],
    name: generateWaiverKeys(id).exceptionKey,
    commentName: generateWaiverKeys(id).commentKey,
  };

  const waiverStatus = {
    ...waiverSummary[1],
    name: generateWaiverKeys(id).statusKey,
  };

  const waiverType = {
    ...waiverSummary[2],
    name: generateWaiverKeys(id).typeKey,
  };

  const newWaiver = [waiverException, waiverStatus, waiverType];

  const ref = React.useRef<any>(null);

  const handleAddWaiver = () => {
    setWaivers((current) => [...current, { id, data: newWaiver }]);
    setId((id) => id + 1);
  };

  const handleRef = React.useCallback(
    (node: any) => {
      !!node &&
        waivers.length > 0 &&
        node.scrollIntoView({ behavior: "smooth", block: "end" });
    },
    [waivers?.length]
  );

  const handleRemoveWaiver = (id: number) => {
    const waiver = waivers.find((waiver) => waiver.id === id);

    if (waiver?.id) {
      const keys = generateWaiverKeys(waiver.id);
      delete formik.values?.[keys.commentKey];
      delete formik.values?.[keys.exceptionKey];
      delete formik.values?.[keys.statusKey];
      delete formik.values?.[keys.typeKey];
    }

    const filteredWaivers = waivers.filter((waiver) => waiver.id !== id);

    setWaivers(filteredWaivers);
  };

  return (
    <>
      {!data.waivers.length && (
        <>
          <TrackerFormWrapper withMargin={true}>
            <MapTrackerForm
              {...{
                data: waiverSummary,
                formik,
              }}
            />
          </TrackerFormWrapper>
        </>
      )}

      {extractWaiverFields(data?.waivers)?.length > 0 &&
        extractWaiverFields(data?.waivers)?.map((waiverObject) => {
          const { waiver, waiver_comment, status, type, archive } =
            waiverObject;

          const clonedWaiver = {
            ...waiverSummary[0],
            name: waiver,
            commentName: waiver_comment,
          };
          const clonedStatus = {
            ...waiverSummary[1],
            name: status,
          };
          const clonedType = {
            ...waiverSummary[2],
            name: type,
          };

          const groupedWaiverArr = [clonedWaiver, clonedStatus, clonedType];

          return (
            <React.Fragment key={waiver}>
              <div className="bg-light" style={{ height: "20px" }}></div>
              <TrackerFormWrapper
                withOpacity={formik.values?.[archive]}
                withMargin={false}
                paddingBottom={"pb-1"}
              >
                <MapTrackerForm
                  {...{
                    data: groupedWaiverArr,
                    formik,
                  }}
                />
              </TrackerFormWrapper>
              <div className="d-flex justify-content-end w-100 bg-light">
                <button
                  type="button"
                  className={`${styles.btnArchive} py-1 px-2`}
                  onClick={() =>
                    formik.setFieldValue(archive, !!!formik.values?.[archive])
                  }
                >
                  <span className="mr-2 ">
                    {formik.values?.[archive] ? "Unarchive" : "Archive"}
                  </span>

                  <ArchiveSvg />
                </button>
              </div>
            </React.Fragment>
          );
        })}

      {waivers.length > 0 &&
        waivers.map((waiver) => {
          return (
            <>
              <div className="d-flex justify-content-end w-100 bg-light mt-2">
                <button
                  type="button"
                  className={`${styles.btnCancel} py-1 px-2`}
                  ref={ref}
                  onClick={() => handleRemoveWaiver(waiver.id)}
                >
                  <CancelSvg />
                </button>
              </div>
              <TrackerFormWrapper withMargin={false}>
                <MapTrackerForm
                  {...{
                    data: waiver.data,
                    formik,
                  }}
                />
              </TrackerFormWrapper>
            </>
          );
        })}

      <div ref={handleRef} className="d-flex justify-content-center my-3">
        <button
          className={`${styles.button} px-2 py-1`}
          onClick={handleAddWaiver}
          type="button"
        >
          <span className="mr-2">Add new</span>
          <TrackerAddSvg />
        </button>
      </div>
    </>
  );
};

export default WaiverSummary;
