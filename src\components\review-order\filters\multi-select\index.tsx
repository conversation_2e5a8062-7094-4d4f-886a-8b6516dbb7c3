import React from "react";

import { ErrorMessage, Field } from "formik";

import Select from "react-select";

import styles from "./index.module.scss";

interface IMultiSelectProps {
  label?: string;
  name?: string;
  type?: string;
  options?: Array<any>;
  relay?: boolean;
  formik?: any;
  placeholder?: string;
}

const CustomSelect = ({ placeholder, field, form, options, isMulti }: any) => {
  const onChange = (option: any) => {
    form.setFieldValue(
      field?.name,
      option?.map((item: any) => item?.value)
    );
  };

  const getValue = () => {
    if (options) {
      return options?.filter(
        (option: any) => field?.value?.indexOf(option?.value) >= 0
      );
    }
  };

  return (
    <Select
      className=""
      name={field?.name}
      value={getValue()}
      onChange={onChange}
      placeholder={placeholder}
      options={options}
      isMulti={isMulti}
    />
  );
};

export default function MultiSelectFieldProperty({
  label,
  name,
  type,
  options,
  placeholder = "",
  formik,
}: IMultiSelectProps) {
  return (
    <div className="d-flex flex-column justify-content-start my-2">
      <label className="m-0">
        <span className={styles.label}>{label}</span>
      </label>
      <Field
        name={name}
        options={options}
        component={CustomSelect}
        placeholder={placeholder}
        isMulti={true}
      />
      <ErrorMessage name={name} />
    </div>
  );
}
