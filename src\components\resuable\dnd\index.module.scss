@import "../../../../styles/_mixins.scss";

.wrapper {
  width: 100%;
  background-color: #f1f3f2;
  @media screen and (max-width: 800px) {
    width: 100%;
  }

  .inner {
    height: 15vh;
    background-color: #f8f8f8;
    border-radius: 4px;
    transition: 0.3s;
    border: 2px dashed #a1aaa6;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }

    &.opacity {
      opacity: 0.3 !important;
    }
  }
}

.name {
  @include font(0.9em, 600);

  @media screen and (max-width: 1000px) {
    @include font(0.8em, 600);
  }

  .lenderName {
    color: var(--risk-primary);
  }
}

.text {
  @include font(0.85em, 500);
  color: #a1aaa6;

  @media screen and (max-width: 1000px) {
    @include font(0.8em, 600);
  }
}
