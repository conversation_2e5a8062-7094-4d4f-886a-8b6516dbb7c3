#!/usr/bin/env node

/**
 * Database test script for Risk-App
 * Tests database initialization, models, and basic operations
 */

const { app } = require('electron');
const path = require('path');
const { databaseManager } = require('../electron/database/database');
const { ModelFactory } = require('../electron/database/models');

/**
 * Test database functionality
 */
async function testDatabase() {
  console.log('🧪 Database Test Suite');
  console.log('=====================\n');
  
  let db, models;
  
  try {
    // Test 1: Database initialization
    console.log('1️⃣ Testing database initialization...');
    db = await databaseManager.initialize(true); // Development mode
    console.log('✅ Database initialized successfully');
    console.log(`   Database path: ${databaseManager.getDatabasePath()}`);
    
    // Test 2: Model factory initialization
    console.log('\n2️⃣ Testing model factory...');
    models = new ModelFactory(db);
    console.log('✅ Models initialized successfully');
    console.log(`   Available models: ${models.getModelNames().join(', ')}`);
    
    // Test 3: Database statistics
    console.log('\n3️⃣ Testing database statistics...');
    const stats = models.getStats();
    console.log('✅ Database statistics retrieved');
    console.log('   Stats:', JSON.stringify(stats, null, 2));
    
    // Test 4: Create test deal
    console.log('\n4️⃣ Testing deal creation...');
    const testDeal = {
      name: 'Test Deal #1',
      status: 'Active',
      borrower_sponsor_name: 'Test Borrower',
      loan_type: 'Commercial',
      property_name: 'Test Property',
      property_address: '123 Test Street, Test City, TC 12345',
      property_type: 'Office Building'
    };
    
    const createdDeal = models.deals.create(testDeal);
    console.log('✅ Deal created successfully');
    console.log(`   Deal ID: ${createdDeal.id}`);
    console.log(`   Deal Name: ${createdDeal.name}`);
    
    // Test 5: Read deal
    console.log('\n5️⃣ Testing deal retrieval...');
    const retrievedDeal = models.deals.findById(createdDeal.id);
    console.log('✅ Deal retrieved successfully');
    console.log(`   Retrieved: ${retrievedDeal.name} (Status: ${retrievedDeal.status})`);
    
    // Test 6: Update deal
    console.log('\n6️⃣ Testing deal update...');
    const updatedDeal = models.deals.update(createdDeal.id, {
      status: 'On-hold',
      mortgage_loan_amount: '$1,000,000'
    });
    console.log('✅ Deal updated successfully');
    console.log(`   New status: ${updatedDeal.status}`);
    console.log(`   Loan amount: ${updatedDeal.mortgage_loan_amount}`);
    
    // Test 7: Search deals
    console.log('\n7️⃣ Testing deal search...');
    const searchResults = models.deals.searchDeals({
      searchTerm: 'Test',
      status: 'On-hold'
    });
    console.log('✅ Deal search completed');
    console.log(`   Found ${searchResults.length} deals matching criteria`);
    
    // Test 8: Dashboard data
    console.log('\n8️⃣ Testing dashboard data...');
    const dashboardData = models.deals.getDashboardData();
    console.log('✅ Dashboard data retrieved');
    console.log('   Dashboard stats:', JSON.stringify(dashboardData, null, 2));
    
    // Test 9: Create test report
    console.log('\n9️⃣ Testing report creation...');
    const testReport = {
      name: 'Test Report #1',
      description: 'A test report for database validation',
      date: new Date().toISOString().split('T')[0],
      link: 'https://example.com/report1'
    };
    
    const createdReport = models.reports.create(testReport);
    console.log('✅ Report created successfully');
    console.log(`   Report ID: ${createdReport.id}`);
    
    // Test 10: Create test consultant
    console.log('\n🔟 Testing consultant creation...');
    const testConsultant = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '******-123-4567',
      specialization: 'Risk Assessment'
    };
    
    const createdConsultant = models.consultants.create(testConsultant);
    console.log('✅ Consultant created successfully');
    console.log(`   Consultant ID: ${createdConsultant.id}`);
    
    // Test 11: Final statistics
    console.log('\n1️⃣1️⃣ Final database statistics...');
    const finalStats = models.getStats();
    console.log('✅ Final statistics retrieved');
    console.log('   Final stats:', JSON.stringify(finalStats, null, 2));
    
    // Test 12: Operation queue check
    console.log('\n1️⃣2️⃣ Testing operation queue...');
    const queueCheck = db.prepare('SELECT COUNT(*) as count FROM operation_queue').get();
    console.log('✅ Operation queue checked');
    console.log(`   Queued operations: ${queueCheck.count}`);
    
    console.log('\n🎉 All database tests passed successfully!');
    console.log('\n📊 Test Summary:');
    console.log(`   ✅ Database initialization: PASSED`);
    console.log(`   ✅ Model factory: PASSED`);
    console.log(`   ✅ CRUD operations: PASSED`);
    console.log(`   ✅ Search functionality: PASSED`);
    console.log(`   ✅ Dashboard data: PASSED`);
    console.log(`   ✅ Operation queue: PASSED`);
    console.log(`   ✅ All models working: PASSED`);
    
    return true;
    
  } catch (error) {
    console.error('\n❌ Database test failed:', error);
    console.error('Stack trace:', error.stack);
    return false;
  } finally {
    // Cleanup
    if (databaseManager) {
      console.log('\n🧹 Cleaning up...');
      databaseManager.close();
      console.log('✅ Database connection closed');
    }
  }
}

// Handle app ready event
if (app) {
  app.whenReady().then(async () => {
    const success = await testDatabase();
    process.exit(success ? 0 : 1);
  });
} else {
  console.error('❌ Electron app not available. Make sure to run this script with Electron.');
  process.exit(1);
}

// Handle app events
app.on('window-all-closed', () => {
  // Don't quit on macOS
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // Re-run tests if activated
  if (process.platform === 'darwin') {
    testDatabase().then(success => process.exit(success ? 0 : 1));
  }
});
