@import "../../../../styles/_mixins.scss";

.label {
  color: rgba(24, 24, 24, 1);
  @include font(0.8em, 600);
}

.field {
  border: 1px solid rgba(241, 243, 242, 1);
  width: 100%;
  background-color: var(--white);
  @include font(0.8em, 600);

  &:focus {
    outline: 0;
    border: 1px solid var(--risk-primary);
  }
}

.commentIcon {
  padding-bottom: 3px !important;
  padding-top: 3px !important;
  cursor: pointer;
  background-color: #ffffff;
  transition: 0.4s;

  &:hover {
    opacity: 0.6;
  }

  > svg {
    fill-opacity: 0.7;
  }
}

.buttonBorder {
  width: 45%;
  border-radius: 2px;
  outline: 0;
  color: var(--risk-primary);
  border: 1px solid var(--risk-primary);
  background-color: var(white);
  @include font(0.8em, 600);
  &:hover {
    opacity: 0.7;
  }
}

.button {
  width: 45%;
  border-radius: 2px;
  outline: 0;
  border: 0;
  color: var(--white);
  background-color: var(--risk-primary);
  @include font(0.8em, 600);

  &:hover {
    opacity: 0.7;
  }
}

.textarea {
  background-color: #ffffff;
  border: 0;
  outline: 0;
  width: 100%;
  padding: 10px;
  background-color: none;
  color: var(gray);
  @include font(0.8em, 600);
  resize: none;
  @include scrollbars();

  &::placeholder {
    @include font(1em, 600);
    color: rgb(24, 24, 24, 0.2);
  }

  &:focus {
    outline: 0;
    border: 0;
    background-color: none;
  }
}

.textareaWrapper {
  background-color: rgba(248, 248, 248, 1);
}

.buttons {
  outline: 0;
  border: 0;
  background-color: rgba(24, 24, 24, 0.1);
  color: rgba(24, 24, 24, 0.3);
  @include font(0.8em, 600);
  border-radius: 20px;

  &.picked {
    background-color: var(--risk-primary);
    color: var(--white);
  }

  &:hover {
    opacity: 0.8;
  }
}

.buttonAddNew {
  background-color: rgba(0, 153, 34, 0.2);
  color: var(--risk-primary);
  outline: 0;
  border: 0;
  @include font(0.8em, 600);
  transition: 0.3s;
  &:hover {
    opacity: 0.7;
  }
}

.btnArchive {
  background-color: rgba(161, 170, 166, 0.2);
  outline: 0;
  border: 0;
  transition: 0.3s;
  @include font(0.8em, 600);
  &:hover {
    opacity: 0.7;
  }
}

.btnCancel {
  background-color: rgba(219, 70, 85, 0.2);
  outline: 0;
  border: 0;
  transition: 0.3s;
  &:hover {
    opacity: 0.7;
  }
  > svg {
    > path {
      fill: red;
    }
  }
}

.delete {
  color: rgba(219, 70, 85, 1);
  @include font(0.9em, 600);
}


.deleteText {
  color: rgba(219, 70, 85, 0.7);
  @include font(0.9em, 600);
}
