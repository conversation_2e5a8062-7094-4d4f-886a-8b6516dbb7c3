import React from "react";

import styles from "./index.module.scss";

interface ITrackerAllFields {
  formik?: any;
  tabClass?: any;
  label: string;
  index: number;
  children: React.ReactNode;
  data?: any;
  includePadding: boolean;
  isSuccess?: boolean;
  isLoading?: boolean;
}

const TrackerAllFields = ({
  formik,
  tabClass,
  children,
  data,
  includePadding = false,
}: ITrackerAllFields) => {
  return (
    <section
      className={`${styles.wrapper} ${tabClass} container-fluid  ${
        includePadding ? "p-0" : "pl-0"
      }`}
    >
      {React.Children.map(children, (child: any) => {
        return React.cloneElement(child, {
          formik,
          data,
        });
      })}
    </section>
  );
};

export default TrackerAllFields;
