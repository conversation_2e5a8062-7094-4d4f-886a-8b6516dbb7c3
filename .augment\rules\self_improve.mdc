---
description: Guidelines for continuously improving Augment rules based on emerging code patterns and best practices, leveraging Augment Agent's world-leading context engine capabilities.
globs: **/*
alwaysApply: true
---

- **Augment-Enhanced Rule Improvement Triggers:**
  - New code patterns identified through comprehensive codebase analysis
  - Repeated implementations discovered via codebase-retrieval across entire project
  - Common error patterns that Augment's reasoning and context engine can prevent
  - Integration opportunities with Augment's advanced tools and capabilities
  - Best practices discovered through web search research and codebase understanding

- **Advanced Analysis Process:**
  - Use codebase-retrieval to analyze entire project patterns and architectural decisions
  - Leverage Augment's web search for latest industry standards and best practices
  - Compare implementations across multiple files and modules using context engine
  - Identify architectural patterns and anti-patterns through comprehensive analysis
  - Research security and performance implications using combined codebase and web research

- **Intelligent Rule Updates:**
  - **Add New Rules When:**
    - Pattern analysis reveals consistent approaches across 3+ files through codebase-retrieval
    - Augment's research identifies industry best practices applicable to the project
    - Security or performance patterns emerge from comprehensive codebase analysis
    - Integration patterns with tools become standardized across the project
    - Architectural decisions need codification based on context engine findings

  - **Enhance Existing Rules When:**
    - Better examples discovered through comprehensive codebase analysis
    - Web research reveals updated best practices applicable to existing patterns
    - Augment identifies edge cases through advanced reasoning and context understanding
    - Tool integration improvements are available and beneficial to the project
    - Performance optimizations are discovered through codebase and research analysis

- **Augment-Powered Pattern Recognition:**
  ```typescript
  // Augment can identify and analyze patterns like:
  const pattern = await analyzeCodebase({
    searchPatterns: ['async/await usage', 'error handling', 'authentication patterns'],
    analyzeArchitecture: true,
    researchBestPractices: true,
    considerProjectContext: true
  });
  
  // Then suggest rule improvements based on comprehensive findings
  ```

- **Enhanced Rule Quality Checks:**
  - Rules validated through Augment's reasoning capabilities and codebase understanding
  - Examples verified against actual codebase using comprehensive context engine
  - References updated through web search for currency and relevance
  - Patterns tested across multiple file contexts using codebase-retrieval
  - Architecture implications thoroughly considered through project analysis

- **Continuous Improvement with Augment Agent:**
  - Monitor development conversations for emerging patterns and architectural decisions
  - Use web search to stay current with technology trends and security updates
  - Analyze commit patterns and code evolution through codebase understanding
  - Research security advisories and performance updates relevant to the project
  - Cross-reference with industry-standard practices using comprehensive analysis

- **Advanced Rule Deprecation:**
  - Use Augment's reasoning to identify obsolete patterns through codebase analysis
  - Research replacement patterns through web search and industry best practices
  - Analyze impact across codebase using comprehensive context engine
  - Provide migration guidance with specific examples from actual project code
  - Update cross-references automatically based on codebase understanding

- **Documentation Enhancement:**
  - Maintain examples synchronized with live code using codebase-retrieval
  - Update external references through web research and validation
  - Link related rules with intelligent cross-referencing based on code patterns
  - Document architectural decisions with full context from project analysis
  - Provide reasoning for rule changes based on comprehensive understanding

- **Augment-Specific Improvements:**
  - Leverage codebase-retrieval for comprehensive rule effectiveness analysis
  - Use web search for validating rule recommendations against industry standards
  - Integrate with Task Master for rule-driven development workflows
  - Provide context-aware rule suggestions based on project patterns
  - Enable intelligent rule conflict resolution through comprehensive analysis

- **Project-Specific Rule Evolution:**
  - Monitor Electron-specific patterns and best practices through codebase analysis
  - Track offline-first architecture decisions and their implications
  - Analyze authentication and security patterns specific to desktop applications
  - Consider React-to-Electron migration patterns and their effectiveness
  - Evaluate database synchronization approaches and their implementation quality

- **Codebase Context Integration:**
  - Rules should reflect actual project architecture discovered through context engine
  - Consider cross-file dependencies and relationships in rule formulation
  - Factor in existing conventions and patterns when creating new rules
  - Maintain consistency with established architectural decisions
  - Leverage real-time codebase indexing for rule validation and improvement

Follow [augment_rules.mdc](mdc:.augment/rules/augment_rules.mdc) for proper rule formatting and structure.
