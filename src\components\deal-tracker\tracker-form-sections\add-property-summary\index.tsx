import React from "react";

import { Formik, Form } from "formik";

import TrackerFormWrapper from "../../../resuable/tracker-form-wrapper";

import MapTrackerForm from "../../map-tracker";

import styles from "./index.module.scss";

import { useParams, useHistory } from "react-router-dom";

import { toastInfo, useGet, usePost } from "../../../../apis";
import { toastError } from "./../../../../apis/index";
import { extractNameValue } from "../../../../utils/groupBy";

interface IAddPropertySummary {
  setShow: (arg: boolean) => void;
  invalidatePortfolio(): void;
}

const AddPropertySummary = ({
  setShow,
  invalidatePortfolio,
}: IAddPropertySummary) => {
  const { id }: { [key: string]: string } = useParams();

  const history = useHistory();

  React.useEffect(() => {
    if (!!!id) history.push("/deals");
    //eslint-disable-next-line
  }, []);

  const { mutate, isLoading } = usePost({
    path: "/order/portfolio/property",
    showErr: false,
  });

  const {
    data: fieldsData,
    isLoading: isLoadingFields,
    isError: isErrorFields,
  } = useGet(`/order/portfolio/property/fields/${id}`);

  const handleSubmit = (values: any, { resetForm }) => {
    const data = { ...values, deal_id: id };

    mutate(data, {
      onSuccess: () => {
        invalidatePortfolio();
        toastInfo("Property successfully added!", 5000);
        resetForm();
      },
      onError: () => {
        toastError("Couldn't add property. Try again.", 5000);
      },
    });
  };

  return (
    <TrackerFormWrapper>
      <section className={`${styles.wrapper} px-2 py-4`}>
        {isLoadingFields ? (
          <section className="d-flex justify-content-center align-items-center my-5">
            <div className="spinner-grow spinner-grow-sm text-success"></div>
          </section>
        ) : isErrorFields ? (
          <section className="d-flex justify-content-center align-items-center my-5">
            <div className="text-danger text-center">
              something went wrong...
            </div>
          </section>
        ) : fieldsData?.fields?.length <= 0 ? (
          <div>Fields not available. Try again later.</div>
        ) : (
          <Formik
            initialValues={extractNameValue(fieldsData?.fields || [])}
            onSubmit={handleSubmit}
          >
            {(formik) => {
              return (
                <Form>
                  <MapTrackerForm
                    {...{
                      data: fieldsData?.fields,
                      formik,
                    }}
                  />

                  <div className="text-center mt-5">
                    <button
                      type="submit"
                      className={`${styles.button} py-2`}
                      disabled={isLoading}
                    >
                      {isLoading ? "Adding property..." : "Save"}
                    </button>

                    <button
                      className={`${styles.buttonBorder} ml-2 py-2`}
                      onClick={() => setShow(false)}
                    >
                      Cancel
                    </button>
                  </div>
                </Form>
              );
            }}
          </Formik>
        )}
      </section>
    </TrackerFormWrapper>
  );
};

export default AddPropertySummary;
