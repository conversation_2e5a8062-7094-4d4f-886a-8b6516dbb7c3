/**
 * Base model class for database operations
 * Provides common CRUD operations and sync functionality
 */

class BaseModel {
  constructor(db, tableName) {
    this.db = db;
    this.tableName = tableName;
    this.preparedStatements = {};
    this.initializePreparedStatements();
  }

  /**
   * Initialize prepared statements for better performance
   */
  initializePreparedStatements() {
    // Basic CRUD operations
    this.preparedStatements.findById = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE id = ?
    `);

    this.preparedStatements.findByServerId = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE server_id = ?
    `);

    this.preparedStatements.findAll = this.db.prepare(`
      SELECT * FROM ${this.tableName} ORDER BY created_at DESC
    `);

    this.preparedStatements.findPending = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE sync_status = 'pending' ORDER BY created_at DESC
    `);

    this.preparedStatements.updateSyncStatus = this.db.prepare(`
      UPDATE ${this.tableName} 
      SET sync_status = ?, last_synced_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    this.preparedStatements.delete = this.db.prepare(`
      DELETE FROM ${this.tableName} WHERE id = ?
    `);

    this.preparedStatements.count = this.db.prepare(`
      SELECT COUNT(*) as count FROM ${this.tableName}
    `);
  }

  /**
   * Find record by ID
   * @param {number} id - Record ID
   * @returns {Object|null} Record or null
   */
  findById(id) {
    try {
      return this.preparedStatements.findById.get(id);
    } catch (error) {
      console.error(`Error finding ${this.tableName} by ID:`, error);
      throw error;
    }
  }

  /**
   * Find record by server ID
   * @param {string} serverId - Server ID
   * @returns {Object|null} Record or null
   */
  findByServerId(serverId) {
    try {
      return this.preparedStatements.findByServerId.get(serverId);
    } catch (error) {
      console.error(`Error finding ${this.tableName} by server ID:`, error);
      throw error;
    }
  }

  /**
   * Find all records
   * @param {Object} options - Query options
   * @returns {Array} Array of records
   */
  findAll(options = {}) {
    try {
      const { limit, offset, where } = options;
      
      let query = `SELECT * FROM ${this.tableName}`;
      const params = [];

      if (where) {
        const conditions = Object.keys(where).map(key => {
          params.push(where[key]);
          return `${key} = ?`;
        });
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      query += ` ORDER BY created_at DESC`;

      if (limit) {
        query += ` LIMIT ?`;
        params.push(limit);
      }

      if (offset) {
        query += ` OFFSET ?`;
        params.push(offset);
      }

      const stmt = this.db.prepare(query);
      return stmt.all(...params);
    } catch (error) {
      console.error(`Error finding all ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Find records pending sync
   * @returns {Array} Array of pending records
   */
  findPending() {
    try {
      return this.preparedStatements.findPending.all();
    } catch (error) {
      console.error(`Error finding pending ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Create new record
   * @param {Object} data - Record data
   * @returns {Object} Created record with ID
   */
  create(data) {
    try {
      const now = new Date().toISOString();
      const recordData = {
        ...data,
        created_at: now,
        updated_at: now,
        sync_status: 'pending'
      };

      const columns = Object.keys(recordData);
      const placeholders = columns.map(() => '?').join(', ');
      const values = Object.values(recordData);

      const query = `
        INSERT INTO ${this.tableName} (${columns.join(', ')})
        VALUES (${placeholders})
      `;

      const stmt = this.db.prepare(query);
      const result = stmt.run(...values);

      // Queue operation for sync
      this.queueOperation('CREATE', result.lastInsertRowid, recordData);

      return {
        id: result.lastInsertRowid,
        ...recordData
      };
    } catch (error) {
      console.error(`Error creating ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Update record
   * @param {number} id - Record ID
   * @param {Object} data - Updated data
   * @returns {Object} Updated record
   */
  update(id, data) {
    try {
      const updateData = {
        ...data,
        updated_at: new Date().toISOString(),
        sync_status: 'pending'
      };

      const columns = Object.keys(updateData);
      const setClause = columns.map(col => `${col} = ?`).join(', ');
      const values = [...Object.values(updateData), id];

      const query = `
        UPDATE ${this.tableName} 
        SET ${setClause}
        WHERE id = ?
      `;

      const stmt = this.db.prepare(query);
      const result = stmt.run(...values);

      if (result.changes === 0) {
        throw new Error(`No ${this.tableName} found with ID: ${id}`);
      }

      // Queue operation for sync
      this.queueOperation('UPDATE', id, updateData);

      return this.findById(id);
    } catch (error) {
      console.error(`Error updating ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Delete record
   * @param {number} id - Record ID
   * @returns {boolean} Success status
   */
  delete(id) {
    try {
      const record = this.findById(id);
      if (!record) {
        throw new Error(`No ${this.tableName} found with ID: ${id}`);
      }

      const result = this.preparedStatements.delete.run(id);

      // Queue operation for sync
      this.queueOperation('DELETE', id, { id });

      return result.changes > 0;
    } catch (error) {
      console.error(`Error deleting ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Update sync status
   * @param {number} id - Record ID
   * @param {string} status - Sync status
   */
  updateSyncStatus(id, status) {
    try {
      this.preparedStatements.updateSyncStatus.run(status, id);
    } catch (error) {
      console.error(`Error updating sync status for ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get record count
   * @returns {number} Total record count
   */
  count() {
    try {
      const result = this.preparedStatements.count.get();
      return result.count;
    } catch (error) {
      console.error(`Error counting ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Queue operation for sync
   * @param {string} operationType - CREATE, UPDATE, DELETE
   * @param {number} recordId - Record ID
   * @param {Object} data - Record data
   */
  queueOperation(operationType, recordId, data) {
    try {
      const queueStmt = this.db.prepare(`
        INSERT INTO operation_queue (operation_type, table_name, record_id, data)
        VALUES (?, ?, ?, ?)
      `);

      queueStmt.run(
        operationType,
        this.tableName,
        recordId.toString(),
        JSON.stringify(data)
      );
    } catch (error) {
      console.error('Error queueing operation:', error);
      // Don't throw here - operation succeeded, sync queue failed
    }
  }

  /**
   * Bulk insert records (for sync operations)
   * @param {Array} records - Array of records
   * @returns {number} Number of inserted records
   */
  bulkInsert(records) {
    if (!records || records.length === 0) {
      return 0;
    }

    try {
      const sampleRecord = records[0];
      const columns = Object.keys(sampleRecord);
      const placeholders = columns.map(() => '?').join(', ');

      const query = `
        INSERT OR REPLACE INTO ${this.tableName} (${columns.join(', ')})
        VALUES (${placeholders})
      `;

      const stmt = this.db.prepare(query);
      const transaction = this.db.transaction(() => {
        for (const record of records) {
          const values = columns.map(col => record[col]);
          stmt.run(...values);
        }
      });

      transaction();
      return records.length;
    } catch (error) {
      console.error(`Error bulk inserting ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Search records
   * @param {string} searchTerm - Search term
   * @param {Array} searchFields - Fields to search in
   * @returns {Array} Matching records
   */
  search(searchTerm, searchFields = ['name']) {
    try {
      const conditions = searchFields.map(field => `${field} LIKE ?`).join(' OR ');
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE ${conditions}
        ORDER BY created_at DESC
      `;

      const searchPattern = `%${searchTerm}%`;
      const params = searchFields.map(() => searchPattern);

      const stmt = this.db.prepare(query);
      return stmt.all(...params);
    } catch (error) {
      console.error(`Error searching ${this.tableName}:`, error);
      throw error;
    }
  }
}

module.exports = BaseModel;
