import React from "react";
import { ErrorMessage, Field } from "formik";
import styles from "./index.module.css";
import Select from "react-select";

interface IMultiSelectProps {
  label: string;
  name: string;
  type: string;
  options: any;
  relay?: boolean;
  formik?: any;
  placeholder?: string;
}

const CustomSelect = ({ placeholder, field, form, options, isMulti }: any) => {
  const onChange = (option: any) => {
    form.setFieldValue(
      field?.name,
      option?.map((item: any) => item?.value)
    );
  };

  const getValue = () => {
    if (options) {
      return options?.filter(
        (option: any) => field?.value?.indexOf(option?.value) >= 0
      );
    }
  };

  return (
    <Select
      className=""
      name={field?.name}
      value={getValue()}
      onChange={onChange}
      placeholder={placeholder}
      options={options}
      isMulti={isMulti}
    />
  );
};

export default function MultiSelectField({
  label,
  name,
  type,
  options,
  placeholder = "Requested items...",
  relay,
  formik,
  ...rest
}: IMultiSelectProps) {
  return (
    <div className="d-flex align-items-center-center my-2">
      <div className="d-flex align-self-center mr-2" style={{ flex: 5 }}>
        <label className="">
          <span className={styles.label}>{label}</span>
        </label>
      </div>
      <div className="" style={{ flex: 7 }}>
        <Field
          name={name}
          options={options}
          component={CustomSelect}
          placeholder={placeholder}
          isMulti={true}
        />
      </div>
      <ErrorMessage name={name} />
    </div>
  );
}
