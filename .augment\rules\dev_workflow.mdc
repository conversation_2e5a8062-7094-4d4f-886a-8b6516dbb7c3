---
description: Guide for using Task Master with Augment Agent to manage task-driven development workflows leveraging Augment's world-leading context engine
globs: **/*
alwaysApply: true
---
# Task Master Development Workflow for Augment Agent

This guide outlines the optimal process for using Task Master with Augment Agent's advanced capabilities to manage software development projects.

## Primary Interaction: MCP Server Integration

Task Master offers seamless integration with Augment Agent through the MCP server:

1.  **MCP Server (Primary Method for Augment Agent)**:
    - Augment Agent interacts via the **MCP server as the preferred method** for optimal performance
    - The MCP server exposes Task Master functionality through structured tools
    - Augment Agent can leverage superior error handling and data exchange
    - Refer to [`mcp.mdc`](mdc:.augment/rules/mcp.mdc) for MCP architecture details
    - Comprehensive tool reference available in [`taskmaster.mdc`](mdc:.augment/rules/taskmaster.mdc)
    - **Restart the MCP server** if core logic in `scripts/modules` changes

2.  **Codebase-Retrieval Integration**:
    - Augment Agent can use codebase-retrieval for comprehensive project understanding
    - Provides real-time access to entire codebase context
    - Enables sophisticated code analysis and architectural understanding
    - Superior to traditional file-by-file analysis for complex operations

## Augment-Enhanced Development Workflow Process

-   **Project Initialization**: Use `initialize_project` tool with codebase analysis
-   **Intelligent Task Analysis**: Leverage `get_tasks` with Augment's superior reasoning and context
-   **Context-Aware Task Selection**: Use `next_task` with comprehensive project understanding
-   **Advanced Complexity Analysis**: Run `analyze_project_complexity` with research capabilities
-   **Multi-Codebase Analysis**: Use codebase-retrieval to understand entire project structure

-   **Smart Task Breakdown**: Use `expand_task` with Augment's enhanced reasoning and context
-   **Comprehensive Implementation**: Follow task details with full codebase awareness
-   **Advanced Testing**: Verify tasks using Augment's understanding of test strategies
-   **Intelligent Status Updates**: Use `set_task_status` with context awareness
-   **Adaptive Task Updates**: Leverage `update` and `update_task` with research capabilities
-   **Dynamic Task Creation**: Use `add_task` with Augment's project understanding
-   **Iterative Subtask Management**: Employ `add_subtask` and `update_subtask` effectively
-   **Automated File Generation**: Use `generate` with codebase integration
-   **Dependency Management**: Maintain valid structures with validation tools
-   **Progress Reporting**: Provide detailed progress analysis using Augment's capabilities

## Augment-Specific Task Analysis Capabilities

-   **Comprehensive Codebase Context**: Analyze task requirements across entire codebase using context engine
-   **Research Integration**: Use web search for latest best practices and solutions
-   **Pattern Recognition**: Identify code patterns and architectural decisions across all files
-   **Dependency Analysis**: Understand complex inter-task relationships through codebase analysis
-   **Risk Assessment**: Evaluate implementation challenges and alternatives with full context

## Enhanced Task Complexity Analysis

-   Run `analyze_project_complexity` with research flag for comprehensive analysis
-   Use Augment's reasoning to interpret complexity scores beyond simple numbers
-   Leverage web search for industry-standard complexity benchmarks
-   Cross-reference with similar projects and best practices through codebase analysis
-   Provide detailed recommendations based on comprehensive codebase understanding

## Advanced Task Breakdown Process

-   Use `expand_task` with Augment's superior understanding of software architecture
-   Apply research capabilities for informed subtask generation
-   Consider cross-cutting concerns and architectural patterns discovered through codebase analysis
-   Factor in testing, documentation, and deployment requirements
-   Provide implementation guidance based on industry best practices and codebase context

## Implementation Drift Handling with Augment Agent

-   **Intelligent Change Detection**: Use Augment's ability to understand implementation impact across codebase
-   **Contextual Updates**: Apply `update` with comprehensive project knowledge from context engine
-   **Architecture-Aware Modifications**: Consider system-wide implications through codebase analysis
-   **Research-Backed Decisions**: Use web search for validating architectural choices
-   **Proactive Risk Mitigation**: Identify potential issues before they occur using codebase understanding

## Enhanced Task Status Management

-   Use status transitions that reflect Augment's understanding of task complexity and codebase impact
-   Provide detailed status reasoning and next steps based on codebase analysis
-   Consider dependencies and blocking factors intelligently through context engine
-   Track progress with comprehensive context awareness across entire project

## Augment-Optimized Iterative Subtask Implementation

1.  **Deep Understanding Phase**:
    *   Use `get_task` combined with codebase analysis via codebase-retrieval
    *   Understand not just the task but its architectural context across entire codebase
    *   Research best practices and potential pitfalls using web search

2.  **Comprehensive Planning Phase**:
    *   Analyze entire codebase for relevant patterns and structures using context engine
    *   Consider cross-cutting concerns and system architecture through codebase-retrieval
    *   Plan implementation with full context awareness of all related code
    *   Document findings using `update_subtask` with detailed insights from codebase analysis

3.  **Intelligent Implementation**:
    *   Set status using `set_task_status` with reasoning based on codebase understanding
    *   Implement with full system understanding through context engine
    *   Consider testing, error handling, and edge cases discovered through codebase analysis
    *   Use file operations for sophisticated code modifications

4.  **Continuous Learning and Adaptation**:
    *   Use `update_subtask` to log implementation learnings with codebase context
    *   Update rules based on emerging patterns discovered through context engine
    *   Share insights across similar tasks using codebase-retrieval findings
    *   Build institutional knowledge within the project through comprehensive analysis

## Codebase-Retrieval Best Practices

-   **Information Requests**: Use natural language to describe what you're looking for
-   **Comprehensive Queries**: Ask for related code, patterns, and architectural decisions
-   **Context Building**: Gather information about dependencies, interfaces, and relationships
-   **Pattern Discovery**: Identify consistent approaches across the codebase
-   **Architecture Understanding**: Analyze how components interact and depend on each other

## Package Management Integration

-   **Always use package managers** for dependency management instead of manual file editing
-   **Leverage Augment's understanding** of different package managers and their conventions
-   **Use codebase-retrieval** to understand existing dependency patterns before making changes
-   **Consider impact analysis** through codebase context before adding/removing dependencies
