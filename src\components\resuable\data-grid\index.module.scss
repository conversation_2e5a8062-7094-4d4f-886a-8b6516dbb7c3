@import "../../../../styles/_mixins.scss";

.reactGrid {
  height: 70vh;
  @include scrollbars(13px);
}

.saveStatus {
  background-color: rgb(233, 248, 237);
  border-radius: 20px;
  span:first-of-type {
    @include font(0.9em, 600);
  }
}

.saving {
  color: rgba(99, 121, 104, 1);
}
.saved {
  color: var(--risk-primary);
}
.error {
  color: rgba(213, 156, 0, 1);
}

.btn {
  cursor: pointer;
  background-color: var(--risk-primary);
  color: #ffffff;
  border-radius: 20px;
  @include font(0.9em, 500);

  transition: 0.3s;

  &:hover {
    opacity: 0.7;
  }
}

.customInput {
  &:hover {
    .hoverItem {
      display: block;
    }
  }
}

.hoverItem {
  display: none;
}

@import "../../../../styles/_mixins.scss";

.wrapper {
  .innerWrapper {
    width: 100%;
    cursor: pointer;
    line-height: 18px;
    transition: 0.3s;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.03);

    &:hover .icons {
      display: flex !important;
    }

    &:hover {
      opacity: 0.95;
      border: 1px solid var(--risk-primary);
    }

    .note {
      @include font(0.9em, 500);
      white-space: pre-line;
      word-break: break-allss;
    }

    .time {
      @include font(0.85em, 500);
      color: rgba(161, 170, 166, 1);
    }

    .author {
      @include font(0.85em, 500);
      color: rgba(213, 156, 0, 1);
    }

    .icons {
      display: none !important;

      &:hover {
        opacity: 0.6;
      }
    }
  }
}

.instruction {
  color: var(--black);
  @include font(0.9em, 600);
}

.iText {
  color: var(--risk-primary);
  @include font(0.9em, 500);
}

.buttonBorder {
  width: 45%;
  border-radius: 2px;
  outline: 0;
  color: var(--risk-primary);
  border: 1px solid var(--risk-primary);
  background-color: var(white);
  @include font(0.8em, 600);
  &:hover {
    opacity: 0.7;
  }
}
.button {
  width: 45%;
  border-radius: 2px;
  outline: 0;
  border: 0;
  color: var(--white);
  background-color: var(--risk-primary);
  @include font(0.8em, 600);

  &:hover {
    opacity: 0.7;
  }
}

.delete {
  color: rgba(219, 70, 85, 1);
  @include font(0.9em, 600);
}
