import React from "react";

import styles from "./index.module.scss";

interface ITextArea {
  label: string;
  name: string;
  showLabel: boolean;
  placeholder: string;
  value: string;
  onChange: (e: any) => void;
  [key: string]: any;
}

const TextArea = ({
  label,
  name,
  showLabel,
  placeholder,
  onChange,
  value,
}: ITextArea) => {
  return (
    <>
      <div className={`${styles.wrapper}`}>
        {label && showLabel && (
          <div className={styles.label}>
            <label htmlFor={label}>{label}</label>
          </div>
        )}

        <div className="">
          <textarea
            onChange={onChange}
            value={value}
            rows={3}
            name={name}
            placeholder={placeholder}
            className={`${styles.field}`}
          />
        </div>
      </div>
    </>
  );
};

export default TextArea;
