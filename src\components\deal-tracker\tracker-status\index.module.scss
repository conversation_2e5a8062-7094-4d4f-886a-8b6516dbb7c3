@import "../../../../styles/_mixins.scss";

@import "../../../../styles/_mixins.scss";

.wrapper {
  background-color: rgba(241, 243, 242, 0.2);

  .info {
    right: 0;
    top: -20px;
  }

  .button {
    background-color: rgb(213, 156, 0);
    padding: 1em 2em;
    color: var(--white);
    outline: 0;
    border: 0;
    transition: 0.3s;
    @include font(0.9rem, 600);
    &:hover {
      border-radius: 10px;
      opacity: 0.7;
    }

    @media screen and (max-width: 1200px) {
      @include font(0.7em, 600);
    }

    @media screen and (max-width: 1000px) {
      @include font(0.8em, 600);
      padding: 1em;
    }

    @media screen and (max-width: 1300px) {
      padding: 1em;
    }
  }

  .checkList {
    @include font(0.8em, 400);
    color: rgba(24, 24, 24, 0.7);
  }
}
