import React from "react";

import CustomModal from "../../../resuable/modal";
import FieldTag from "../field-tag";
import FilterPopupContent from "./filter-popup-content";

import styles from "./index.module.scss";

import { formatDate } from "./../../../../utils/formatDate";

import { buildFilterUrl } from "../../../../utils/filtersUtil";

import { clearLocalStorage } from "../../../../utils/clearLocalStorage";

const cancelIcon = (
  <svg
    width="8"
    height="8"
    viewBox="0 0 8 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.02151 6.2704C0.826215 6.46563 0.826154 6.78221 1.02138 6.97751C1.2166 7.17281 1.53319 7.17287 1.72849 6.97764L1.02151 6.2704ZM4.35349 4.35364C4.54879 4.15842 4.54885 3.84184 4.35362 3.64654C4.1584 3.45124 3.84181 3.45118 3.64651 3.6464L4.35349 4.35364ZM3.64631 3.6466C3.45112 3.84194 3.45124 4.15852 3.64658 4.35371C3.84192 4.5489 4.1585 4.54878 4.35369 4.35344L3.64631 3.6466ZM6.97769 1.72744C7.17288 1.53211 7.17276 1.21552 6.97742 1.02034C6.78208 0.825148 6.4655 0.825268 6.27031 1.0206L6.97769 1.72744ZM4.35349 3.6464C4.15819 3.45118 3.8416 3.45124 3.64638 3.64654C3.45115 3.84184 3.45121 4.15842 3.64651 4.35364L4.35349 3.6464ZM6.27151 6.97764C6.46681 7.17287 6.7834 7.17281 6.97862 6.97751C7.17385 6.78221 7.17379 6.46563 6.97849 6.2704L6.27151 6.97764ZM3.64638 4.35351C3.8416 4.54881 4.15819 4.54887 4.35349 4.35364C4.54879 4.15842 4.54885 3.84184 4.35362 3.64654L3.64638 4.35351ZM1.72862 1.02054C1.5334 0.825238 1.21681 0.825178 1.02151 1.0204C0.826215 1.21563 0.826154 1.53221 1.02138 1.72751L1.72862 1.02054ZM1.72849 6.97764L4.35349 4.35364L3.64651 3.6464L1.02151 6.2704L1.72849 6.97764ZM4.35369 4.35344L6.97769 1.72744L6.27031 1.0206L3.64631 3.6466L4.35369 4.35344ZM3.64651 4.35364L6.27151 6.97764L6.97849 6.2704L4.35349 3.6464L3.64651 4.35364ZM4.35362 3.64654L1.72862 1.02054L1.02138 1.72751L3.64638 4.35351L4.35362 3.64654Z"
      fill="#A1AAA6"
    />
  </svg>
);

const CancelButton = ({ onClick }) => {
  return (
    <div className={`${styles.cancelButton} ml-2`} onClick={onClick}>
      {cancelIcon}
    </div>
  );
};

const FieldValue = ({ children }: { children: React.ReactText }) => {
  return <div className={`${styles.fieldTagValue}`}>{children}</div>;
};

const filterIcon = (
  <div
    className={`${styles.svgBg} mr-2 d-flex align-items-center rounded rounded-circle`}
  >
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 4.375C4 4.16789 4.19188 4 4.42857 4H9.57143C9.80812 4 10 4.16789 10 4.375V5.5C10 5.59945 9.95485 5.69484 9.87447 5.76516L7.85714 7.53032V9.87499C7.85714 9.97445 7.81199 10.0698 7.73162 10.1402L6.87447 10.8902C6.7519 10.9974 6.56757 11.0295 6.40742 10.9714C6.24728 10.9134 6.14286 10.7767 6.14286 10.625V7.53032L4.12553 5.76516C4.04515 5.69484 4 5.59945 4 5.5V4.375Z"
        fill="#ffffff"
      />
    </svg>
  </div>
);

const filter = (set: (arg: boolean) => void) => (
  <div
    onClick={() => set(true)}
    className={`${styles.filterButton} d-flex align-items-center  py-1 px-2 rounded`}
    style={{ width: "90px" }}
    aria-label="filter orders in different dimensions"
  >
    {filterIcon}
    <span className="">Filters</span>
  </div>
);

const flattenFormValue = (values: { [key: string]: any }) => {
  const result = [];

  Object.entries(values).forEach(([key, value]) => {
    const payload = { value: value, id: key };

    result.push(payload);
  });

  return result;
};

const checkFilterValues = (values: any[]) => {
  return values
    ?.map((v) => v.value)
    .some((v) => {
      if (
        !Array.isArray(v) &&
        typeof v === "object" &&
        Object.values(v || {})?.length > 0
      ) {
        return true;
      }

      if (Array.isArray(v) && v?.length > 0) {
        return true;
      }

      if ((typeof v === "string" || v === true) && !!v) {
        return true;
      }

      return false;
    });
};

const FilterPopup = ({ formik, setFilters, handleSubmit, data }: any) => {
  const [show, setShow] = React.useState(false);

  React.useLayoutEffect(() => {
    setFilters(buildFilterUrl(formik.values));

    //eslint-disable-next-line
  }, []);

  const selectedValues = flattenFormValue(formik.values);

  const isFilterValue = checkFilterValues(selectedValues);

  const handleRemoveOne = (id: string, value: string) => {
    const currentValues: Array<string> = formik.values?.[id];

    if (id === "closing_date" || id === "portfolio") {
      formik.setFieldValue(id, id === "closing_date" ? {} : "");
      formik.submitForm();
      return void 0;
    }

    Array.isArray(currentValues) &&
      formik.setFieldValue(
        id,
        currentValues.filter((v: string) => v !== value)
      );
    formik.submitForm();
  };

  const handleReset = () => {
    clearLocalStorage(process.env.REACT_APP_RISK_FORM);
    Object.entries(formik.values).forEach(([key]) => {
      formik.setFieldValue(key, "");
    });
    formik.submitForm();

  };

  return (
    <>
      <CustomModal
        {...{
          title: "Filter options",
          show,
          setShow,
          columnLayout: "col-12 col-md-9 col-lg-9 col-xl-7",
          align: "align-items-start pt-5",
        }}
      >
        {
          <FilterPopupContent
            formik={formik}
            setShow={setShow}
            handleSubmit={handleSubmit}
            data={data}
            handleReset={handleReset}
          />
        }
      </CustomModal>

      <div className={`d-flex align-items-center w-100`}>
        {filter(setShow)}
        <div
          style={{ flexWrap: "wrap" }}
          className={`ml-3 w-100 d-flex align-items-center justify-content-start ${
            styles.tag
          } ${isFilterValue && "bg-white"} text-center rounded`}
        >
          {selectedValues.length > 0 &&
            selectedValues.map(({ value, id }) => {
              if (
                !Array.isArray(value) &&
                typeof value === "object" &&
                Object.values(value)?.length > 0
              ) {
                return (
                  <FieldTag>
                    <span>Closing: </span>{" "}
                    {formatDate(value?.startDate, {
                      year: "numeric",
                      day: "numeric",
                      month: "short",
                    })}
                    {" - "}
                    {formatDate(value?.endDate, {
                      year: "numeric",
                      day: "numeric",
                      month: "short",
                    })}
                    <CancelButton
                      onClick={() => {
                        handleRemoveOne(id, "");
                      }}
                    />
                  </FieldTag>
                );
              }

              if (Array.isArray(value) && value?.length > 0) {
                return value.map((v: string) => (
                  <FieldTag>
                    <FieldValue>{v}</FieldValue>
                    <CancelButton
                      onClick={() => {
                        handleRemoveOne(id, v);
                      }}
                    />
                  </FieldTag>
                ));
              }

              if (value === true) {
                return (
                  <FieldTag>
                    <span>Portfolio:</span>{" "}
                    <FieldValue>{value ? "True" : null}</FieldValue>
                    <CancelButton
                      onClick={() => {
                        handleRemoveOne(id, "");
                      }}
                    />
                  </FieldTag>
                );
              }

              if (typeof value === "string" && value !== "") {
                return (
                  <FieldTag>
                    {<FieldValue>{value}</FieldValue>}
                    <CancelButton
                      onClick={() => {
                        handleRemoveOne(id, "");
                      }}
                    />
                  </FieldTag>
                );
              }
              return null;
            })}
        </div>
        {isFilterValue && (
          <button
            className={`btn border-0 bg-transparent px-2 ml-2`}
            onClick={handleReset}
            title={"Clear filters"}
          >
            <span>Clear Filters</span>
          </button>
        )}
      </div>
    </>
  );
};

export default FilterPopup;
