import React from "react";

import styles from "./index.module.scss";

import TextArea from "../../resuable/text-area";
import NoNote from "./../../svgs/NoNote";
import NoteList from "./../../resuable/note-list";

import dayjs from "dayjs";

import { getUserName } from "../../../utils/getUserName";

import { useParams } from "react-router-dom";

interface ITrackerNote {
  formik?: any;
  setIsNote?: (arg: boolean) => void;
}

const TrackerNote = ({ formik, setIsNote }: ITrackerNote) => {
  const [noteVal, setNoteVal] = React.useState<{
    id?: number;
    note: any;
  }>({ id: null, note: "" });

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNoteVal((current) => ({ ...current, note: e.target.value }));
  };

  const params: any = useParams();

  const handleSubmitNote = () => {
    setIsNote(true);

    formik.handleSubmit({ ...formik.values, id: params?.id });
  };

  const handleAddNote = (e: any) => {
    e.preventDefault();

    const previousNotes = formik.values?.["note"];

    if (!!noteVal?.id) {
      const item = previousNotes.find((note: any) => note.id === noteVal?.id);

      const newItem = { ...item, note: noteVal?.note.trim() };

      const index = previousNotes.findIndex(
        (note: any) => note.id === noteVal?.id
      );

      const currentNotes = [...previousNotes];

      currentNotes.splice(index, 1, newItem);

      formik.setFieldValue("note", currentNotes);

      setNoteVal({ note: "" });

      setIsNote(true);

      formik.handleSubmit({ ...formik.values, id: params?.id });

      return;
    }

    if (previousNotes?.length) {
      if (!!noteVal.note) {
        formik.setFieldValue("note", [
          {
            id: new Date().getTime(),
            note: noteVal.note.trim(),
            time: dayjs().format("MM/DD/YYYY h:mma"),
            author: getUserName() ?? "",
          },
          ...previousNotes,
        ]);
        setNoteVal({ note: "" });
      }
    } else {
      if (!!noteVal.note) {
        formik.setFieldValue("note", [
          {
            id: new Date().getTime(),
            note: noteVal.note.trim(),
            time: dayjs().format("MM/DD/YYYY h:mma"),
            author: getUserName() ?? "",
          },
        ]);
        setNoteVal({ note: "" });
      }
    }

    setIsNote(true);

    formik.handleSubmit({ ...formik.values, id: params?.id });
  };

  return (
    <section className={`${styles.wrapper} p-2`}>
      <TextArea
        {...{
          name: "note",
          label: "Type in notes",
          showLabel: false,
          placeholder: "Type in notes",
          onChange,
          value: noteVal.note,
        }}
      />

      <button
        type="button"
        onClick={handleAddNote}
        disabled={!noteVal?.note}
        className={`w-100 py-1 ${styles.btn} ${
          !noteVal?.note ? styles.disabled : ""
        }`}
      >
        {!!noteVal?.id ? "Edit Note" : "Add Note"}
      </button>

      <section className={`${styles.notes} mt-2`}>
        {!formik.values?.["note"]?.length ? (
          <div className={`text-center mt-5 pt-5`}>
            <div>
              <NoNote />
            </div>
            <div className={`${styles.noNote}`}>No note has been added.</div>
          </div>
        ) : (
          formik.values?.["note"]?.map((note: any) => {
            return (
              <NoteList
                key={note.id}
                {...{
                  note,
                  setNoteVal,
                  noteCheck: noteVal.note,
                  formik,
                  handleSubmitNote,
                }}
              />
            );
          })
        )}
      </section>
    </section>
  );
};

export default TrackerNote;
