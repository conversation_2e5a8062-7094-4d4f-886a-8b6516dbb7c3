import React from "react";

import { DateRangePicker } from "@matharumanpreet00/react-daterange-picker";

import CustomModal from "../../../resuable/modal";

import styles from "./index.module.scss";

const dateFormat = new Intl.DateTimeFormat("en-US", {});

const DateRangeField = ({ label, formik, name }: any) => {
  const [show, setShow] = React.useState(false);

  const startDate = formik.values?.[name]?.startDate;
  const endDate = formik.values?.[name]?.endDate;

  return (
    <>
      <CustomModal
        {...{
          title: null,
          show,
          includeBg: true,
          setShow,
          columnLayout: "col-12 col-md-9 col-lg-8 col-xl-6 z-index-1 mt-3",
          align: "align-items-start pt-5",
        }}
      >
        <DateRangePicker
          open={true}
          onChange={(range) => {
            formik.setFieldValue(name, range);
          }}
        />
      </CustomModal>

      <div className="my-2">
        <span className={styles.label}>{label}</span>

        <div className={`${styles.field}`} onClick={() => setShow(true)}>
          {startDate && dateFormat.format(new Date(startDate))} <span>-</span>{" "}
          {endDate && dateFormat.format(new Date(endDate))}
        </div>
      </div>
    </>
  );
};

export default DateRangeField;
