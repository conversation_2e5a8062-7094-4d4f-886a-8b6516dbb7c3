import React, { useState } from 'react';
import { ConnectionStatusBar } from './ConnectionStatusBar';
import { ConnectionIndicator } from './ConnectionIndicator';
import ElectronStatus from '../electron/ElectronStatus';
import { ElectronNotifications } from '../../utils/electronNotifications';
import styles from './ConnectionStatusTest.module.scss';

/**
 * Test component for demonstrating all connection status UI indicators
 * This component is for development and testing purposes
 */
const ConnectionStatusTest: React.FC = () => {
  const [simulatedOnlineStatus, setSimulatedOnlineStatus] = useState(true);
  const [showTestPanel, setShowTestPanel] = useState(false);

  const handleToggleConnection = () => {
    setSimulatedOnlineStatus(!simulatedOnlineStatus);
    
    // Show notification for testing
    ElectronNotifications.show({
      title: simulatedOnlineStatus ? 'Going Offline' : 'Going Online',
      body: simulatedOnlineStatus 
        ? 'Simulating offline mode for testing'
        : 'Simulating online mode for testing',
      type: simulatedOnlineStatus ? 'warning' : 'success',
      duration: 3000
    });
  };

  const handleTestNotifications = () => {
    const notifications = [
      { title: 'Test Info', body: 'This is an info notification', type: 'info' as const },
      { title: 'Test Success', body: 'This is a success notification', type: 'success' as const },
      { title: 'Test Warning', body: 'This is a warning notification', type: 'warning' as const },
      { title: 'Test Error', body: 'This is an error notification', type: 'error' as const }
    ];

    notifications.forEach((notification, index) => {
      setTimeout(() => {
        ElectronNotifications.show({
          ...notification,
          duration: 2000
        });
      }, index * 1000);
    });
  };

  const handleTestSyncNotifications = () => {
    ElectronNotifications.show({
      title: 'Sync Started',
      body: 'Synchronizing data with server...',
      type: 'info',
      duration: 2000
    });

    setTimeout(() => {
      ElectronNotifications.show({
        title: 'Sync Completed',
        body: 'All data has been synchronized successfully.',
        type: 'success',
        duration: 3000
      });
    }, 3000);
  };

  if (!showTestPanel) {
    return (
      <div className={styles.testToggle}>
        <button 
          onClick={() => setShowTestPanel(true)}
          className={styles.toggleButton}
        >
          Show Connection Status Test Panel
        </button>
      </div>
    );
  }

  return (
    <div className={styles.testContainer}>
      <div className={styles.header}>
        <h3>Connection Status UI Test Panel</h3>
        <button 
          onClick={() => setShowTestPanel(false)}
          className={styles.closeButton}
        >
          ×
        </button>
      </div>

      <div className={styles.controls}>
        <button 
          onClick={handleToggleConnection}
          className={`${styles.controlButton} ${simulatedOnlineStatus ? styles.online : styles.offline}`}
        >
          Simulate {simulatedOnlineStatus ? 'Offline' : 'Online'} Mode
        </button>
        
        <button 
          onClick={handleTestNotifications}
          className={styles.controlButton}
        >
          Test All Notifications
        </button>
        
        <button 
          onClick={handleTestSyncNotifications}
          className={styles.controlButton}
        >
          Test Sync Notifications
        </button>
      </div>

      <div className={styles.section}>
        <h4>Connection Status Bar (Inline)</h4>
        <ConnectionStatusBar 
          position="inline"
          showLastSync={true}
          showDataStatus={true}
        />
      </div>

      <div className={styles.section}>
        <h4>Connection Status Bar (Top)</h4>
        <div style={{ position: 'relative', height: '60px', border: '1px dashed #ccc' }}>
          <ConnectionStatusBar 
            position="top"
            showLastSync={true}
            showDataStatus={true}
          />
        </div>
      </div>

      <div className={styles.section}>
        <h4>Connection Indicators</h4>
        <div className={styles.indicatorGrid}>
          <div className={styles.indicatorItem}>
            <span>Dot (Small):</span>
            <ConnectionIndicator variant="dot" size="small" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Dot (Medium):</span>
            <ConnectionIndicator variant="dot" size="medium" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Dot (Large):</span>
            <ConnectionIndicator variant="dot" size="large" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Badge (Small):</span>
            <ConnectionIndicator variant="badge" size="small" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Badge (Medium):</span>
            <ConnectionIndicator variant="badge" size="medium" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Badge (Large):</span>
            <ConnectionIndicator variant="badge" size="large" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Icon (Small):</span>
            <ConnectionIndicator variant="icon" size="small" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Icon (Medium):</span>
            <ConnectionIndicator variant="icon" size="medium" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Icon (Large):</span>
            <ConnectionIndicator variant="icon" size="large" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Text (Small):</span>
            <ConnectionIndicator variant="text" size="small" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Text (Medium):</span>
            <ConnectionIndicator variant="text" size="medium" />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Text (Large):</span>
            <ConnectionIndicator variant="text" size="large" />
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <h4>Connection Indicator with Text</h4>
        <div className={styles.indicatorGrid}>
          <div className={styles.indicatorItem}>
            <span>Dot + Text:</span>
            <ConnectionIndicator variant="dot" size="medium" showText={true} />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Badge + Text:</span>
            <ConnectionIndicator variant="badge" size="medium" showText={true} />
          </div>
          
          <div className={styles.indicatorItem}>
            <span>Icon + Text:</span>
            <ConnectionIndicator variant="icon" size="medium" showText={true} />
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <h4>Enhanced Electron Status</h4>
        <div className={styles.electronStatusGrid}>
          <div className={styles.electronStatusItem}>
            <span>Full Status:</span>
            <ElectronStatus 
              showVersion={true}
              showNetworkStatus={true}
              showSyncStatus={true}
              compact={false}
            />
          </div>
          
          <div className={styles.electronStatusItem}>
            <span>Compact Status:</span>
            <ElectronStatus 
              showVersion={false}
              showNetworkStatus={true}
              showSyncStatus={true}
              compact={true}
            />
          </div>
          
          <div className={styles.electronStatusItem}>
            <span>Network Only:</span>
            <ElectronStatus 
              showVersion={false}
              showNetworkStatus={true}
              showSyncStatus={false}
              compact={false}
            />
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <h4>Positioned Indicators</h4>
        <div className={styles.positionedContainer}>
          <div className={styles.positionedBox}>
            <span>Container with positioned indicators</span>
            <ConnectionIndicator variant="dot" size="small" position="top-right" />
            <ConnectionIndicator variant="badge" size="small" position="top-left" />
            <ConnectionIndicator variant="icon" size="small" position="bottom-right" />
            <ConnectionIndicator variant="text" size="small" position="bottom-left" />
          </div>
        </div>
      </div>

      <div className={styles.footer}>
        <p><strong>Note:</strong> This test panel demonstrates all connection status UI components. 
        In production, these components will automatically reflect the actual network status.</p>
      </div>
    </div>
  );
};

export default ConnectionStatusTest;
