@import "../../../../styles/_mixins.scss";

.wrapper {
  background-color: #ffffff;
  height: 85vh;
  overflow-y: auto;

  @include scrollbars();

  .title {
    color: rgba(24, 24, 24, 1);
    @include font(0.9em, 600);
    @include userSelect;
  }

  .percentage {
    color: var(--risk-primary);
    @include font(0.9em, 600);
    @include userSelect;
  }

  .stickyBar {
    background-color: #ffffff;
    top: 10px;
    @include userSelect;
  }

  .button {
    width: 30%;
    border-radius: 2px;
    outline: 0;
    border: 0;
    color: var(--white);
    background-color: var(--risk-primary);
    transition: 0.3s;
    @include font(0.9em, 600);

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }

    &:hover {
      opacity: 0.7;
    }
  }
}
