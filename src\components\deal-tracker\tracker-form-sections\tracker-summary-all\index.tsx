import React from "react";

import TrackerFormWrapper from "../../../resuable/tracker-form-wrapper";
import MapTrackerForm from "../../map-tracker";

import { groupBy } from "../../../../utils/groupBy";

interface ITrackerPropertySummary {
  formik?: any;
  data?: any;
}

const TrackerSummaryAllFields = ({ formik, data }: ITrackerPropertySummary) => {
  const totalSection = data?.section_count;

  return (
    <>
      {Array(totalSection)
        .fill(0)
        .map((_, i) => {
          const number = ++i;

          return (
            <TrackerFormWrapper
              key={i}
              withMargin={number === 1 ? false : true}
            >
              <MapTrackerForm
                {...{
                  data: groupBy(data?.fields, "section", number),
                  formik,
                }}
              />
            </TrackerFormWrapper>
          );
        })}
    </>
  );
};

export default TrackerSummaryAllFields;
