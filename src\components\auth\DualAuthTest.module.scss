@import "../../../styles/_mixins.scss";

.notElectron {
  padding: 20px;
  text-align: center;
  color: rgba(24, 24, 24, 0.6);
  @include font(0.9em, 400);
}

.testToggle {
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 1000;

  .toggleButton {
    padding: 8px 16px;
    background-color: var(--risk-primary);
    color: white;
    border: none;
    border-radius: 6px;
    @include font(0.8em, 500);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--risk-primary-light);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
  }
}

.testContainer {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 450px;
  max-height: 80vh;
  background: white;
  border: 1px solid rgba(46, 113, 111, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow-y: auto;
  font-family: inherit;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(46, 113, 111, 0.1);
    background: linear-gradient(135deg, rgba(46, 113, 111, 0.05), rgba(46, 113, 111, 0.1));

    h3 {
      margin: 0;
      @include font(1.1em, 600);
      color: var(--risk-primary);
    }

    .closeButton {
      background: none;
      border: none;
      @include font(1.5em, 400);
      color: rgba(24, 24, 24, 0.5);
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(24, 24, 24, 0.1);
        color: rgba(24, 24, 24, 0.8);
      }
    }
  }

  .section {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(46, 113, 111, 0.1);

    &:last-child {
      border-bottom: none;
    }

    h4 {
      margin: 0 0 12px 0;
      @include font(0.9em, 600);
      color: rgba(24, 24, 24, 0.8);
    }
  }

  .statusGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;

    .statusItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 8px;
      background-color: rgba(241, 243, 242, 0.5);
      border-radius: 4px;
      border: 1px solid rgba(46, 113, 111, 0.1);
      @include font(0.75em, 500);

      span:first-child {
        color: rgba(24, 24, 24, 0.7);
      }

      span:last-child {
        font-weight: 600;
      }
    }
  }

  .userInfo {
    background-color: rgba(241, 243, 242, 0.3);
    padding: 12px;
    border-radius: 6px;
    border: 1px solid rgba(46, 113, 111, 0.1);

    div {
      margin-bottom: 4px;
      @include font(0.8em, 400);

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: rgba(24, 24, 24, 0.8);
      }
    }
  }

  .error {
    background-color: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    border-radius: 6px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    @include font(0.8em, 500);
    color: var(--danger);

    .clearErrorButton {
      background: var(--danger);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 4px 8px;
      @include font(0.7em, 500);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(231, 76, 60, 0.8);
      }
    }
  }

  .loginForm {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .input {
      padding: 8px 12px;
      border: 1px solid rgba(46, 113, 111, 0.3);
      border-radius: 6px;
      @include font(0.8em, 400);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--risk-primary);
        box-shadow: 0 0 0 2px rgba(46, 113, 111, 0.1);
      }
    }

    .loginButton {
      padding: 10px 16px;
      background-color: var(--risk-primary);
      color: white;
      border: none;
      border-radius: 6px;
      @include font(0.8em, 600);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background-color: var(--risk-primary-light);
      }

      &:disabled {
        background-color: rgba(46, 113, 111, 0.5);
        cursor: not-allowed;
      }
    }
  }

  .actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .actionButton {
      padding: 6px 12px;
      background-color: rgba(46, 113, 111, 0.1);
      color: var(--risk-primary);
      border: 1px solid rgba(46, 113, 111, 0.3);
      border-radius: 4px;
      @include font(0.75em, 500);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background-color: rgba(46, 113, 111, 0.2);
        border-color: rgba(46, 113, 111, 0.5);
      }

      &:disabled {
        background-color: rgba(46, 113, 111, 0.05);
        color: rgba(46, 113, 111, 0.5);
        cursor: not-allowed;
      }
    }
  }

  .permissionTest {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .input {
      padding: 8px 12px;
      border: 1px solid rgba(46, 113, 111, 0.3);
      border-radius: 6px;
      @include font(0.8em, 400);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--risk-primary);
        box-shadow: 0 0 0 2px rgba(46, 113, 111, 0.1);
      }
    }

    .permissionResult {
      padding: 8px 12px;
      border-radius: 4px;
      @include font(0.75em, 600);
      text-align: center;
    }
  }

  .statsGrid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;

    .statItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 8px;
      background-color: rgba(241, 243, 242, 0.5);
      border-radius: 4px;
      border: 1px solid rgba(46, 113, 111, 0.1);
      @include font(0.75em, 500);

      span:first-child {
        color: rgba(24, 24, 24, 0.7);
      }

      span:last-child {
        font-weight: 600;
      }
    }
  }

  .footer {
    padding: 16px 20px;
    background-color: rgba(241, 243, 242, 0.3);
    border-radius: 0 0 12px 12px;

    p {
      margin: 0;
      @include font(0.75em, 400);
      color: rgba(24, 24, 24, 0.6);
      line-height: 1.4;

      strong {
        color: rgba(24, 24, 24, 0.8);
      }
    }
  }
}

// Status colors
.success {
  color: var(--success) !important;
}

.warning {
  color: var(--warning) !important;
}

.error {
  color: var(--danger) !important;
}

.neutral {
  color: rgba(24, 24, 24, 0.6) !important;
}

// Responsive adjustments
@media screen and (max-width: 768px) {
  .testContainer {
    width: calc(100vw - 40px);
    left: 20px;
    right: 20px;
    max-height: 70vh;
  }

  .statusGrid {
    grid-template-columns: 1fr;
  }
}

@media screen and (max-width: 480px) {
  .testToggle {
    bottom: 60px;
    right: 10px;

    .toggleButton {
      padding: 6px 12px;
      @include font(0.75em, 500);
    }
  }

  .testContainer {
    top: 10px;
    left: 10px;
    right: 10px;
    width: auto;
    max-height: 80vh;

    .header {
      padding: 12px 16px;

      h3 {
        @include font(1em, 600);
      }
    }

    .section {
      padding: 12px 16px;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .testContainer {
    background: rgba(24, 24, 24, 0.95);
    border-color: rgba(255, 255, 255, 0.2);

    .header {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.1));
      border-bottom-color: rgba(255, 255, 255, 0.1);

      h3 {
        color: rgba(255, 255, 255, 0.9);
      }

      .closeButton {
        color: rgba(255, 255, 255, 0.5);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    .section {
      border-bottom-color: rgba(255, 255, 255, 0.1);

      h4 {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .statusGrid .statusItem,
    .statsGrid .statItem {
      background-color: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);

      span:first-child {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    .userInfo {
      background-color: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);

      div {
        color: rgba(255, 255, 255, 0.8);

        strong {
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }

    .loginForm .input,
    .permissionTest .input {
      background-color: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.3);
      color: rgba(255, 255, 255, 0.9);

      &:focus {
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
      }
    }

    .actions .actionButton {
      background-color: rgba(255, 255, 255, 0.05);
      color: rgba(255, 255, 255, 0.8);
      border-color: rgba(255, 255, 255, 0.3);

      &:hover:not(:disabled) {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }

    .footer {
      background-color: rgba(255, 255, 255, 0.05);

      p {
        color: rgba(255, 255, 255, 0.6);

        strong {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}
