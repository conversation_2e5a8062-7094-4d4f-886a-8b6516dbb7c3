{"version": 3, "file": "preload.js", "mappings": ";;;;;;;;;;AAAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;;ACtBA,MAAM;EAAEA,aAAa;EAAEC;AAAY,CAAC,GAAGC,mBAAO,CAAC,0BAAU,CAAC;;AAE1D;AACA;AACAF,aAAa,CAACG,iBAAiB,CAAC,aAAa,EAAE;EAC7C;EACAC,UAAU,EAAEA,CAAA,KAAMH,WAAW,CAACI,MAAM,CAAC,aAAa,CAAC;EAEnD;EACAC,cAAc,EAAGC,OAAO,IAAKN,WAAW,CAACI,MAAM,CAAC,kBAAkB,EAAEE,OAAO,CAAC;EAE5E;EACAC,SAAS,EAAGC,QAAQ,IAAKR,WAAW,CAACS,EAAE,CAAC,UAAU,EAAED,QAAQ,CAAC;EAC7DE,UAAU,EAAGF,QAAQ,IAAKR,WAAW,CAACS,EAAE,CAAC,WAAW,EAAED,QAAQ,CAAC;EAC/DG,UAAU,EAAGH,QAAQ,IAAKR,WAAW,CAACS,EAAE,CAAC,WAAW,EAAED,QAAQ,CAAC;EAE/D;EACAI,QAAQ,EAAE;IACR;EAAA,CACD;EAED;EACAC,QAAQ,EAAGL,QAAQ,IAAK;IACtBM,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEP,QAAQ,CAAC;IAC3C,OAAO,MAAMM,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAER,QAAQ,CAAC;EAC7D,CAAC;EAEDS,SAAS,EAAGT,QAAQ,IAAK;IACvBM,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEP,QAAQ,CAAC;IAC5C,OAAO,MAAMM,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAER,QAAQ,CAAC;EAC9D,CAAC;EAED;EACAU,UAAU,EAAE;IACV;EAAA,CACD;EAED;EACAC,IAAI,EAAE;IACJ;EAAA,CACD;EAED;EACAC,IAAI,EAAE;IACJ;EAAA;AAEJ,CAAC,CAAC;;AAEF;AACA,OAAON,MAAM,CAACb,OAAO;AACrB,OAAOa,MAAM,CAACO,OAAO;AACrB,OAAOP,MAAM,CAACQ,MAAM,C", "sources": ["webpack://risk-app/external commonjs \"electron\"", "webpack://risk-app/webpack/bootstrap", "webpack://risk-app/./electron/preload/preload.js"], "sourcesContent": ["module.exports = require(\"electron\");", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');\n\n// Expose protected methods that allow the renderer process to use\n// the ipcRenderer without exposing the entire object\ncontextBridge.exposeInMainWorld('electronAPI', {\n  // App information\n  getVersion: () => ipcRenderer.invoke('app-version'),\n  \n  // Dialog methods\n  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),\n  \n  // Menu events (listen only)\n  onMenuNew: (callback) => ipcRenderer.on('menu-new', callback),\n  onMenuOpen: (callback) => ipcRenderer.on('menu-open', callback),\n  onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),\n  \n  // Database operations (will be added later)\n  database: {\n    // Placeholder for database operations\n  },\n  \n  // Network status\n  onOnline: (callback) => {\n    window.addEventListener('online', callback);\n    return () => window.removeEventListener('online', callback);\n  },\n  \n  onOffline: (callback) => {\n    window.addEventListener('offline', callback);\n    return () => window.removeEventListener('offline', callback);\n  },\n  \n  // File system operations (will be added later)\n  fileSystem: {\n    // Placeholder for file system operations\n  },\n  \n  // Authentication (will be added later)\n  auth: {\n    // Placeholder for authentication operations\n  },\n  \n  // Sync operations (will be added later)\n  sync: {\n    // Placeholder for sync operations\n  }\n});\n\n// Security: Remove access to Node.js APIs in the renderer process\ndelete window.require;\ndelete window.exports;\ndelete window.module;\n"], "names": ["contextBridge", "ip<PERSON><PERSON><PERSON><PERSON>", "require", "exposeInMainWorld", "getVersion", "invoke", "showMessageBox", "options", "onMenuNew", "callback", "on", "onMenuOpen", "onMenuSave", "database", "onOnline", "window", "addEventListener", "removeEventListener", "onOffline", "fileSystem", "auth", "sync", "exports", "module"], "sourceRoot": ""}