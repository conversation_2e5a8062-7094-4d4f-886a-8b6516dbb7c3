/**
 * Reports model for database operations
 * Handles report-specific business logic and queries
 */

const BaseModel = require('./base-model');

class ReportsModel extends BaseModel {
  constructor(db) {
    super(db, 'reports');
    this.initializeReportsStatements();
  }

  /**
   * Initialize report-specific prepared statements
   */
  initializeReportsStatements() {
    this.preparedStatements.findByDateRange = this.db.prepare(`
      SELECT * FROM ${this.tableName} 
      WHERE date BETWEEN ? AND ?
      ORDER BY date DESC
    `);

    this.preparedStatements.findRecent = this.db.prepare(`
      SELECT * FROM ${this.tableName} 
      ORDER BY date DESC 
      LIMIT ?
    `);
  }

  /**
   * Find reports by date range
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {Array} Array of reports
   */
  findByDateRange(startDate, endDate) {
    try {
      return this.preparedStatements.findByDateRange.all(startDate, endDate);
    } catch (error) {
      console.error('Error finding reports by date range:', error);
      throw error;
    }
  }

  /**
   * Find recent reports
   * @param {number} limit - Number of reports to return
   * @returns {Array} Array of recent reports
   */
  findRecent(limit = 10) {
    try {
      return this.preparedStatements.findRecent.all(limit);
    } catch (error) {
      console.error('Error finding recent reports:', error);
      throw error;
    }
  }

  /**
   * Create report with validation
   * @param {Object} reportData - Report data
   * @returns {Object} Created report
   */
  create(reportData) {
    try {
      this.validateReportData(reportData);
      return super.create(reportData);
    } catch (error) {
      console.error('Error creating report:', error);
      throw error;
    }
  }

  /**
   * Validate report data
   * @param {Object} data - Report data to validate
   */
  validateReportData(data) {
    const errors = [];

    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
      errors.push('Report name is required');
    }

    if (data.link && !this.isValidUrl(data.link)) {
      errors.push('Invalid report link URL');
    }

    if (errors.length > 0) {
      throw new Error(`Validation errors: ${errors.join(', ')}`);
    }
  }

  /**
   * Check if URL is valid
   * @param {string} url - URL to validate
   * @returns {boolean} Whether URL is valid
   */
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

module.exports = ReportsModel;
