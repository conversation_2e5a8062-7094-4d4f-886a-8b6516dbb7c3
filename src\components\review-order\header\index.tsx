import React from "react";

import Search from "../filters/search";
import CreateReview from "./create-review";
import Title from "./title";
import { ConnectionIndicator } from "../../connection-status";
import { useElectron } from "../../../hooks/useElectron";

import styles from "./index.module.scss";

interface IReviewHeader {
  setSearchClientState: (v: any) => void;
  searchClientState: string;
}

export default function ReviewHeader({
  setSearchClientState,
  searchClientState,
}: IReviewHeader) {
  const { isElectron } = useElectron();

  return (
    <div className="container-fluid">
      <div className="row justify-content-between">
        <div
          className={`col-12 col-md-8 col-lg-6 col-xl-6 d-flex align-items-center ${styles.innerWrapper}`}
        >
          <div className="d-flex align-items-center">
            <Title title="Deals" />
            {/* Connection indicator for Electron */}
            {isElectron && (
              <div className="ml-2">
                <ConnectionIndicator
                  variant="badge"
                  size="small"
                  showText={false}
                />
              </div>
            )}
          </div>
          <div className="ml-2 ml-sm-2 ml-md-3 ml-lg-4 ml-xl-4 flex-fill">
            <Search {...{ searchClientState, setSearchClientState }} />
          </div>
        </div>

        <div className="d-flex align-items-center justify-content-end col-12 col-md-4 col-lg-2 col-xl-2 text-center">
          <div>
            <CreateReview />
          </div>
        </div>
      </div>
    </div>
  );
}
