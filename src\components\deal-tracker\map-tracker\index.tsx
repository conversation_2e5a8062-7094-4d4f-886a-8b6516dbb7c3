import React from "react";

import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ield from "../../resuable/tracker-contact-field";
import MultiSelectFieldTracker from "../../resuable/multi-select-tracker";
import TrackerButtons from "../../resuable/tracker-form-buttons";
import Tracker<PERSON>electField from "../../resuable/tracker-select";
import TrackerT<PERSON>t<PERSON>ield from "../../resuable/tracker-text-field";
import WaiverTextField from "../../resuable/waiver-input";

const pickField = (fieldType: string, item: { [key: string]: any }) => {
  switch (fieldType) {
    case "text":
    case "date":
    case "time":
    case "phone":
    case "tel":
    case "email": {
      return <TrackerTextField {...item} />;
    }
    case "select": {
      return <TrackerSelectField {...item} />;
    }
    case "buttons": {
      return <TrackerButtons {...item} />;
    }
    case "waiver": {
      return <WaiverTextField {...item} />;
    }
    case "popform": {
      return <TrackerContactField {...item} />;
    }
    case "multiselect": {
      return <MultiSelectFieldTracker {...item} />;
    }
    default:
      throw new Error("Invalid form type supplied.");
  }
};

const MapTrackerForm = ({
  data,
  formik,
}: {
  data: Array<any>;
  formik: any;
}) => {
  return (
    <>
      {data.length > 0 && (
        <div className="row">
          {data.map((item: any) => {
            return (
              <div className="col-12 mt-1" key={item.name}>
                {pickField(item.type, { ...item, formik })}
              </div>
            );
          })}
        </div>
      )}
    </>
  );
};
export default MapTrackerForm;
