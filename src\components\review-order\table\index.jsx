import React from "react";

import { useTable, usePagination } from "react-table";
import { useGetReviewOrder } from "../../../apis";
import { trimData } from "../../../utils/trimData";
import { useAppSelector, useAppDispatch } from "../../../store/storeConfig";
import {
  handlePageChange,
  handlePageSizeChange,
  handleTotalCountChange,
} from "../../../store/slices/tableSlice";

import TableOptions from "../table-options";
import TableBody from "../table-body";

import Sort from "../filters/sort";
import ReviewHeader from "./../header/index";

import styles from "./index.module.scss";
import PortfolioSm from "../../svgs/PortofolioSm";
import { formatDate } from "./../../../utils/formatDate";

import FilterPopup from "../filters/filter-popup";

import { Form, Formik } from "formik";

import { buildFilterUrl } from "../../../utils/filtersUtil";
import { saveSession } from "../../../utils/readLocalStorage";
import { getSession } from "./../../../utils/readLocalStorage";

const Avatar = (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9 3.75C9 5.40685 7.65685 6.75 6 6.75C4.34315 6.75 3 5.40685 3 3.75C3 2.09315 4.34315 0.75 6 0.75C6.79565 0.75 7.55871 1.06607 8.12132 1.62868C8.68393 2.19129 9 2.95435 9 3.75Z"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M0.75 11.2501C3.9659 9.25581 8.0341 9.25581 11.25 11.2501"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

const columns = [
  {
    Header: "",
    accessor: "id",
    width: 20,
    Cell: ({ row }) => {
      const assigned = row?.original?.assigned_to_initials;
      if (assigned)
        return (
          <div
            className={`${styles.assigned} d-flex align-items-center justify-content-center mt-1`}
          >
            {assigned}
          </div>
        );

      return (
        <div
          className={`${styles.profile} d-flex align-items-center justify-content-center`}
        >
          {Avatar}
        </div>
      );
    },
  },
  {
    Header: "Order no",
    accessor: "order_number",
    Cell: ({ row }) => {
      return (
        <div className={`${styles.tableItems}`}>
          <div className={styles.header}>{row?.original?.order_number}</div>
          <div className={styles.footer}>{row?.original?.loan_number}</div>
        </div>
      );
    },
  },

  {
    Header: "Client",
    accessor: "client",
    Cell: ({ row }) => {
      return (
        <div className={`${styles.tableItems}`}>
          <div className={`${styles.client}`}>{row?.original?.client}</div>
          <div className={styles.footer}>{row?.original?.loan_execution}</div>
        </div>
      );
    },
  },
  {
    Header: "Deal Name",
    accessor: "deal",
    Cell: ({ row }) => {
      return (
        <div className={`${styles.tableItems}`}>
          <div className={`d-flex align-items-center ${styles.header}`}>
            <div className="mr-1">{row?.original?.deal}</div>
            {row?.original?.is_portfolio && <PortfolioSm />}
          </div>

          <div className={styles.footer}>
            {row?.original?.property_location}
          </div>
        </div>
      );
    },
  },
  {
    Header: "Sponsor",
    accessor: "sponsor",
    Cell: ({ row }) => {
      return (
        <div className={`${styles.tableItems}`}>
          <div className={styles.header}>{row?.original?.sponsor}</div>
          <div className={styles.footer}>
            {row?.original?.borrowing_entity_name}
          </div>
        </div>
      );
    },
  },
  {
    Header: "Est Closing Date",
    accessor: "status",
    Cell: ({ row }) => {
      const status = row?.original?.status?.toLocaleLowerCase();

      const date = row?.original?.loan_closing_date;

      return (
        <div className={`${styles.tableItems}`}>
          <div className={styles.header} style={{ fontSize: "0.9em" }}>
            {formatDate(new Date(date), {
              weekday: "short",
              year: "numeric",
              day: "numeric",
              month: "short",
            })}
          </div>
          <div
            className={styles?.[status] ?? styles.paid}
            style={{ fontSize: "0.9em" }}
          >
            {row?.original?.status}
          </div>
        </div>
      );
    },
  },
];

const risk_form = process.env.REACT_APP_RISK_FORM;

function OrderTable() {
  const { queryPageIndex, queryPageSize, totalCount } = useAppSelector(
    (state) => state.tableSlice
  );

  const dispatch = useAppDispatch();

  const [searchClientState, setSearchClientState] = React.useState("");

  const [sortKey, setSortKey] = React.useState("Est Closing Date Soonest");

  const [filters, setFilters] = React.useState(null);

  const { isLoading, error, data, isSuccess, isError } = useGetReviewOrder(
    "/orders",
    queryPageIndex,
    queryPageSize,
    searchClientState,
    sortKey,
    filters
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    page,
    canPreviousPage,
    canNextPage,
    pageOptions,
    pageCount,
    gotoPage,
    nextPage,
    previousPage,
    state: { pageIndex, pageSize },
  } = useTable(
    {
      columns,
      data: isSuccess ? trimData(data?.orders) : [],
      initialState: {
        pageIndex: queryPageIndex,
        pageSize: queryPageSize,
      },
      manualPagination: true,
      pageCount: isSuccess ? Math.floor(totalCount / 10) + 1 : null,
    },
    usePagination
  );

  React.useEffect(() => {
    dispatch(handlePageChange(pageIndex));
  }, [pageIndex, dispatch]);

  React.useEffect(() => {
    dispatch(handlePageSizeChange(pageSize));
  }, [pageSize, gotoPage, dispatch]);

  React.useEffect(() => {
    if (data?.count) {
      dispatch(handleTotalCountChange(data?.count));
    }
  }, [data?.count, dispatch]);

  const handleSubmit = (values) => {
    saveSession(risk_form, values);

    setFilters(buildFilterUrl(values));
  };

  const formValues = getSession(risk_form);

  return (
    <section className="position-relative" style={{ height: "100vh" }}>
      <section className={`bg-white p-2`}>
        <ReviewHeader {...{ searchClientState, setSearchClientState }} />
      </section>

      <div className="container-fluid my-2">
        <div className="row align-items-center justify-content-between">
          <Formik
            initialValues={{
              consultant: formValues?.consultant || "",
              portfolio: formValues?.portfolio || "",
              client: formValues?.client || "",
              closing_date: formValues?.closing_date || "",
              property_state: formValues?.property_state || "",
              status_param: formValues?.status_param || "",
            }}
            onSubmit={handleSubmit}
            //enableReinitialize={true}
          >
            {(formik) => {
              return (
                <Form className="col-8">
                  <FilterPopup
                    formik={formik}
                    data={data}
                    setFilters={setFilters}
                    isSuccess
                    handleRemove
                  />
                </Form>
              );
            }}
          </Formik>
          <div className="col-3">
            <Sort {...{ setSortKey, sortKey }} />
          </div>
        </div>
      </div>

      <section>
        {isLoading ? (
          <section
            style={{ height: "70vh" }}
            className="d-flex justify-content-center align-items-center flex-column"
          >
            <div className="spinner-grow spinner-grow-sm text-success my-3"></div>
            <div>Kindly wait...</div>
          </section>
        ) : isError ? (
          <section className="container text-danger text-capitalize font-weight-bold py-5 text-center">
            {error?.message || "Something went wrong."}
          </section>
        ) : data?.orders?.length <= 0 ? (
          <section className="container text-info text-capitalize font-weight-bold py-5 text-center">
            No Available Deals.
          </section>
        ) : isSuccess ? (
          <section
            className={`table-responsive table-hover container-fluid ${styles.tableSpace}`}
          >
            <TableBody
              {...{
                getTableProps,
                headerGroups,
                getTableBodyProps,
                page,
                prepareRow,
                gotoPage,
                canPreviousPage,
                canNextPage,
                previousPage,
                nextPage,
                pageIndex,
                pageOptions,
                pageCount,
              }}
            />
          </section>
        ) : null}
      </section>

      {!isLoading && !isError && data?.orders?.length > 0 && (
        <section
          className={`position-absolute w-100 py-3 ${styles.tableOptionsFloat}`}
          style={{ bottom: "0px" }}
        >
          <TableOptions
            {...{
              data,
              gotoPage,
              canPreviousPage,
              canNextPage,
              previousPage,
              nextPage,
              pageIndex,
              pageOptions,
              pageCount,
            }}
          />
        </section>
      )}
    </section>
  );
}

export default OrderTable;


