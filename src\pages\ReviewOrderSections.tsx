import React from "react";
import ReviewOrderMenu from "../components/review-order-menu";
import ReviewOrderMenuContainer from "../components/review-order-menu/menu";
import ReviewsOrderSectionComponent from "../components/review-order-sections";
import DueDiligence from "../components/review-order-sections/due-diligence";
import FormProvider from "../components/review-order-sections/form-provider";
import LoanSummary from "../components/review-order-sections/loan-summary";
import OrderSummary from "../components/review-order-sections/order-summary";
import PropertySummary from "../components/review-order-sections/property-summary";
import { getreviewOrderMenuData } from "../data/review-order-menu";
import Contacts from "./../components/review-order-sections/contacts";
import CondoSummary from "../components/review-order-sections/condo-summary";
import ConstructionSummary from "../components/review-order-sections/construction-summary";
import EnvironmentalSummary from "./../components/review-order-sections/environmental-summary/index";
import TenantSummary from "../components/review-order-sections/tenant-summary";
import { handleScroll } from "./../utils/handleScroll";
import { cacheScroll } from "./../utils/cacheScroll";
import SupplementalHeader from "../components/review-order-sections/supplemental-header";
import ReviewOrderTop from "../components/review-order-sections/review-order-top";
import { readLastLocation } from "../utils/setLastLocation";
import SubmittedDate from "../components/review-order-menu/submit-date";
import { useParams } from "react-router";
import { useGetSingleOrder } from "../apis";

export default function ReviewOrderSections() {
  const reviewOrderSectionsRef = React.useRef<any>(null);
  const orderSummaryRef = React.useRef(null);
  const loanSummaryRef = React.useRef(null);
  const propertySummaryRef = React.useRef(null);
  const dueDiligenceRef = React.useRef(null);
  const contactsRef = React.useRef(null);
  const condoRef = React.useRef(null);
  const constructionRef = React.useRef(null);
  const environmentalRef = React.useRef(null);
  const tenantRef = React.useRef(null);
  const suppHeaderRef = React.useRef(null);

  const reviewOrderMenuData = React.useMemo(
    () =>
      getreviewOrderMenuData({
        orderSummaryRef,
        loanSummaryRef,
        propertySummaryRef,
        dueDiligenceRef,
        contactsRef,
        condoRef,
        constructionRef,
        environmentalRef,
        tenantRef,
        suppHeaderRef,
      }),
    []
  );

  const cacheScrollMemo = React.useCallback(
    () =>
      cacheScroll({
        orderSummaryRef,
        loanSummaryRef,
        propertySummaryRef,
        dueDiligenceRef,
        contactsRef,
        condoRef,
        constructionRef,
        environmentalRef,
        tenantRef,
        suppHeaderRef,
      }),
    []
  );

  React.useEffect(() => {
    cacheScrollMemo();
  }, [cacheScrollMemo]);

  const [selectedMenu, setSelectedMenu] = React.useState<undefined | string>(
    "Order Summary"
  );

  React.useEffect(() => {
    if (readLastLocation()) {
      setSelectedMenu(readLastLocation());
    }
  }, []);

  const [isClicking, setIsClicking] = React.useState<boolean>(false);

  const handleScrollMemo = React.useCallback(() => {
    if (isClicking === false) {
      handleScroll({
        reviewOrderMenuData,
        orderSummaryRef,
        reviewOrderSectionsRef,
        setSelectedMenu,
        selectedMenu,
      });
    }
  }, [reviewOrderMenuData, selectedMenu, isClicking]);

  const param = useParams<any>();

  const { data, isLoading, error, isError } = useGetSingleOrder(
    "/order",
    param?.id === "create" ? param?.id : Number(param?.id)
  );

  if (isLoading) {
    return (
      <p
        className="d-flex align-items-center justify-content-center w-100"
        style={{ height: "100vh" }}
      >
        Loading...
      </p>
    );
  }
  if (!data && param?.id !== "create")
    return <p>Oops, something went wrong.</p>;

  if (isError || error) return <p>Oops, something went wrong.</p>;

  return (
    <section className="d-flex" style={{ flex: 10 }}>
      <ReviewsOrderSectionComponent
        {...{ handleScroll: handleScrollMemo, ref: reviewOrderSectionsRef }}
      >
        <FormProvider {...{ data }}>
          <ReviewOrderTop />

          <OrderSummary ref={orderSummaryRef} />
          <LoanSummary ref={loanSummaryRef} />
          <PropertySummary ref={propertySummaryRef} />
          <DueDiligence ref={dueDiligenceRef} />
          <Contacts ref={contactsRef} />
          <SupplementalHeader ref={suppHeaderRef} />
          <CondoSummary ref={condoRef} />
          <ConstructionSummary ref={constructionRef} />
          <EnvironmentalSummary ref={environmentalRef} />
          <TenantSummary ref={tenantRef} />
        </FormProvider>
      </ReviewsOrderSectionComponent>
      <ReviewOrderMenu
        {...{ reviewOrderMenuData, selectedMenu, setIsClicking }}
      >
        <ReviewOrderMenuContainer {...{ setSelectedMenu }} />
        <SubmittedDate {...{ data }} />
      </ReviewOrderMenu>
    </section>
  );
}
