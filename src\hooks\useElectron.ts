import { useEffect, useState } from 'react';

// Type definitions for Electron API
interface ElectronAPI {
  getVersion: () => Promise<string>;
  showMessageBox: (options: any) => Promise<any>;
  onMenuNew: (callback: () => void) => void;
  onMenuOpen: (callback: () => void) => void;
  onMenuSave: (callback: () => void) => void;
  onOnline: (callback: () => void) => () => void;
  onOffline: (callback: () => void) => () => void;
  database: any;
  fileSystem: any;
  auth: any;
  sync: any;
}

// Extend window interface to include electronAPI
declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}

/**
 * Custom hook to interact with Electron APIs
 * Provides a safe way to access Electron functionality from React components
 */
export const useElectron = () => {
  const [isElectron, setIsElectron] = useState(false);
  const [appVersion, setAppVersion] = useState<string>('');
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    // Check if running in Electron environment
    const electronAPI = window.electronAPI;
    setIsElectron(!!electronAPI);

    if (electronAPI) {
      // Get app version
      electronAPI.getVersion().then(setAppVersion).catch(console.error);

      // Set up online/offline listeners
      const removeOnlineListener = electronAPI.onOnline(() => {
        setIsOnline(true);
      });

      const removeOfflineListener = electronAPI.onOffline(() => {
        setIsOnline(false);
      });

      // Cleanup listeners on unmount
      return () => {
        removeOnlineListener();
        removeOfflineListener();
      };
    }
  }, []);

  // Helper functions for Electron APIs
  const showMessageBox = async (options: {
    type?: 'info' | 'warning' | 'error' | 'question';
    title?: string;
    message: string;
    buttons?: string[];
  }) => {
    if (window.electronAPI) {
      return await window.electronAPI.showMessageBox(options);
    }
    // Fallback for web environment
    alert(options.message);
  };

  const getAppVersion = () => appVersion;

  const isElectronEnvironment = () => isElectron;

  const getNetworkStatus = () => isOnline;

  return {
    isElectron,
    appVersion,
    isOnline,
    showMessageBox,
    getAppVersion,
    isElectronEnvironment,
    getNetworkStatus,
    electronAPI: window.electronAPI,
  };
};

/**
 * Hook specifically for online/offline status
 */
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const electronAPI = window.electronAPI;

    if (electronAPI) {
      // Use Electron's network status detection
      const removeOnlineListener = electronAPI.onOnline(() => {
        setIsOnline(true);
      });

      const removeOfflineListener = electronAPI.onOffline(() => {
        setIsOnline(false);
      });

      return () => {
        removeOnlineListener();
        removeOfflineListener();
      };
    } else {
      // Fallback to web APIs
      const handleOnline = () => setIsOnline(true);
      const handleOffline = () => setIsOnline(false);

      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, []);

  return isOnline;
};
