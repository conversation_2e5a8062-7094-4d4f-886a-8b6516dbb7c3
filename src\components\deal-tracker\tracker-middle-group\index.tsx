import React from "react";

import { useInjectBox } from "../../../hooks/useInjectBox";

import styles from "./index.module.scss";

interface ITrackerLeftGroup {
  children: React.ReactNode;
  formik?: any;
  data?: any;
  isLoading?: boolean;
  fullScreen?: any;
  isSuccess?: boolean;
  setFullScreen?: any;
}

const TrackerMiddleGroup = ({
  children,
  formik,
  data,
  isLoading,
  isSuccess,
}: ITrackerLeftGroup) => {
  useInjectBox(data);

  return (
    <section
      className={`col-12 col-md-6 col-lg-6 col-xl-6 px-0 ${styles.wrapper} position-relative`}
    >
      {React.Children.map(children, (child: any) => {
        return React.cloneElement(child, {
          formik,
          data,
          isLoading,
          isSuccess,
        });
      })}
    </section>
  );
};

export default TrackerMiddleGroup;
