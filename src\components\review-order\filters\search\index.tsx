import React from "react";
import { SearchIcon } from "../../../../exports";
import styles from "./index.module.scss";

const cancelIcon = (
  <svg
    width="14"
    height="14"
    viewBox="0 0 8 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.02151 6.2704C0.826215 6.46563 0.826154 6.78221 1.02138 6.97751C1.2166 7.17281 1.53319 7.17287 1.72849 6.97764L1.02151 6.2704ZM4.35349 4.35364C4.54879 4.15842 4.54885 3.84184 4.35362 3.64654C4.1584 3.45124 3.84181 3.45118 3.64651 3.6464L4.35349 4.35364ZM3.64631 3.6466C3.45112 3.84194 3.45124 4.15852 3.64658 4.35371C3.84192 4.5489 4.1585 4.54878 4.35369 4.35344L3.64631 3.6466ZM6.97769 1.72744C7.17288 1.53211 7.17276 1.21552 6.97742 1.02034C6.78208 0.825148 6.4655 0.825268 6.27031 1.0206L6.97769 1.72744ZM4.35349 3.6464C4.15819 3.45118 3.8416 3.45124 3.64638 3.64654C3.45115 3.84184 3.45121 4.15842 3.64651 4.35364L4.35349 3.6464ZM6.27151 6.97764C6.46681 7.17287 6.7834 7.17281 6.97862 6.97751C7.17385 6.78221 7.17379 6.46563 6.97849 6.2704L6.27151 6.97764ZM3.64638 4.35351C3.8416 4.54881 4.15819 4.54887 4.35349 4.35364C4.54879 4.15842 4.54885 3.84184 4.35362 3.64654L3.64638 4.35351ZM1.72862 1.02054C1.5334 0.825238 1.21681 0.825178 1.02151 1.0204C0.826215 1.21563 0.826154 1.53221 1.02138 1.72751L1.72862 1.02054ZM1.72849 6.97764L4.35349 4.35364L3.64651 3.6464L1.02151 6.2704L1.72849 6.97764ZM4.35369 4.35344L6.97769 1.72744L6.27031 1.0206L3.64631 3.6466L4.35369 4.35344ZM3.64651 4.35364L6.27151 6.97764L6.97849 6.2704L4.35349 3.6464L3.64651 4.35364ZM4.35362 3.64654L1.72862 1.02054L1.02138 1.72751L3.64638 4.35351L4.35362 3.64654Z"
      fill="#A1AAA6"
    />
  </svg>
);

interface ISearchProps {
  setSearchClientState: (arg1: string) => void;
  searchClientState: string;
}

const debounce = (fn: any, delay: number, f: any) => {
  let timer: any;

  return (value: string) => {
    f.current.value = value;
    if (timer) clearTimeout(timer);

    timer = setTimeout(() => {
      fn(value);
    }, delay);
  };
};

export default function Search({
  searchClientState,
  setSearchClientState,
}: ISearchProps) {
  const ref = React.useRef(null);
  const debounceFunc = debounce(setSearchClientState, 1000, ref);

  const handleClear = () => {
    ref.current.value = "";
    setSearchClientState("");
  };

  return (
    <section style={{ flex: 2 }} className="mr-1 w-100">
      <div className={`d-flex align-items-center ${styles.container}`}>
        <aside style={{ flex: 1 }}>
          <input
            ref={ref}
            onChange={({ target: { value } }) => {
              debounceFunc(value);
            }}
            placeholder="Search Review Order by Client, Deal or Sponsor"
            className={`${styles.input} pl-3`}
          />
        </aside>

        <aside className={`${styles.search} px-3 py-2 align-items-center`}>
          {!!ref.current?.value ? (
            <div className={styles.cancelIcon} onClick={handleClear}>
              {cancelIcon}
            </div>
          ) : (
            <img src={SearchIcon} className="img-fluid" alt="search icons" />
          )}
        </aside>
      </div>
    </section>
  );
}
