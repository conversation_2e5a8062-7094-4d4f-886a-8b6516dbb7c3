import React from "react";

import DragAndDrop from "../dnd";
import FileList from "../file-list";

import styles from "./index.module.scss";
import EmptyFile from "./../empty-file/index";

interface ILenderListUpload {
  data: Array<any>;
  formik?: any;
  label: string;
}

const LenderListUpload = ({ formik, data, label }: ILenderListUpload) => {
  return (
    <section className={`${styles.wrapper}`}>
      <DragAndDrop
        {...{
          name: "any",
          formik,
          label,
        }}
      />

      <div className={styles.fileWrapper}>
        {!data.length ? (
          <EmptyFile />
        ) : (
          data.map((value, i) => {
            return (
              <FileList
                key={i}
                {...{
                  file: value,
                }}
              />
            );
          })
        )}
      </div>
    </section>
  );
};

export default LenderListUpload;
