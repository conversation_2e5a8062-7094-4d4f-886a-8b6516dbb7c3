export const extractWaiverFields = (data: Array<any>) => {
  return data.map((data: any) => {
    const id = data.id;

    const commentId = `comment_${id}`;
    const statusId = `status_${id}`;
    const typeId = `type_${id}`;
    const archiveId = `archive_${id}`;
    const waiverExceptionId = `waiver_exception_${id}`;

    return {
      id,
      waiver: waiverExceptionId,
      waiver_comment: commentId,
      status: statusId,
      type: typeId,
      archive: archiveId,
    };
  });
};

export const extractWaiverValues = (data: Array<any>) => {
  const mappedNameValue = data.map((data: any) => {
    const id = data.id;

    const commentId = `comment_${id}`;
    const statusId = `status_${id}`;
    const typeId = `type_${id}`;
    const archiveId = `archive_${id}`;
    const waiverExceptionId = `waiver_exception_${id}`;

    return {
      [commentId]: data.waiver_comment,
      [statusId]: data.status,
      [typeId]: data.type,
      [archiveId]: data.archive,
      [waiverExceptionId]: data.waiver_exception,
    };
  });

  let extractor = {};
  mappedNameValue.forEach((v) => {
    extractor = { ...extractor, ...v };
  });

  return extractor;
};

export const extraWaiversValuesNew = (waivers: any) => {
  const waiversDB = [
    {
      id: 1,
      archive: false,
      type: {
        label: "Type",
        name: "type",
        type: "buttons",
        options: ["peter", "maiyaki"],
        value: ["peter", "maiyaki"],
      },
      waiver_exception: {
        name: "waiver_exception",
        label: "Waiver exception",
        type: "text",
        value: "hello",
      },
      status: {
        name: "status",
        label: "Status",
        type: "buttons",
        options: ["peter", "maiyaki"],
        value: "maiyaki",
      },
      comment: {
        name: "comment",
        label: "Waiver Comment ",
        type: "text",
        value: "hi",
      },
    },
  ];

  return {
    waivers: (waiversDB || []).map((waiver: any) => {
      return Object.entries(waiver).reduce((acc: any, [k, v]: any) => {
        acc[k] = typeof v === "object" ? v.value : v;
        return acc;
      }, {});
    }),
  };
};
