---
description: Guidelines for continuously improving <PERSON> rules based on emerging code patterns and best practices, leveraging <PERSON>'s advanced capabilities.
globs: **/*
alwaysApply: true
---

- **<PERSON>-Enhanced Rule Improvement Triggers:**
  - New code patterns identified through multi-file analysis
  - Repeated implementations discovered via Desktop Commander search
  - Common error patterns that <PERSON>'s reasoning can prevent
  - Integration opportunities with <PERSON>'s advanced tools
  - Best practices discovered through web search research

- **Advanced Analysis Process:**
  - Use Desktop Commander to analyze entire codebase patterns
  - Leverage <PERSON>'s web search for latest industry standards
  - Compare implementations across multiple projects
  - Identify architectural patterns and anti-patterns
  - Research security and performance implications

- **Intelligent Rule Updates:**
  - **Add New Rules When:**
    - Pattern analysis reveals consistent approaches across 3+ files
    - <PERSON>'s research identifies industry best practices
    - Security or performance patterns emerge from analysis
    - Integration patterns with tools become standardized
    - Architectural decisions need codification

  - **Enhance Existing Rules When:**
    - Better examples discovered through codebase analysis
    - Web research reveals updated best practices
    - <PERSON> identifies edge cases through reasoning
    - Tool integration improvements are available
    - Performance optimizations are discovered

- **Claude-Powered Pattern Recognition:**
  ```typescript
  // <PERSON> can identify and analyze patterns like:
  const pattern = await analyzeCodebase({
    searchPatterns: ['async/await usage', 'error handling'],
    analyzeArchitecture: true,
    researchBestPractices: true
  });
  
  // Then suggest rule improvements based on findings
  ```

- **Enhanced Rule Quality Checks:**
  - Rules validated through Claude's reasoning capabilities
  - Examples verified against actual codebase using Desktop Commander
  - References updated through web search for currency
  - Patterns tested across multiple file contexts
  - Architecture implications thoroughly considered

- **Continuous Improvement with Claude:**
  - Monitor development conversations for emerging patterns
  - Use web search to stay current with technology trends
  - Analyze commit patterns and code evolution
  - Research security advisories and performance updates
  - Cross-reference with industry-standard practices

- **Advanced Rule Deprecation:**
  - Use Claude's reasoning to identify obsolete patterns
  - Research replacement patterns through web search
  - Analyze impact across codebase using Desktop Commander
  - Provide migration guidance with specific examples
  - Update cross-references automatically

- **Documentation Enhancement:**
  - Maintain examples synchronized with live code
  - Update external references through web research
  - Link related rules with intelligent cross-referencing
  - Document architectural decisions with full context
  - Provide reasoning for rule changes

- **Claude-Specific Improvements:**
  - Leverage artifact creation for complex rule examples
  - Use analysis tool for rule effectiveness metrics
  - Integrate with Task Master for rule-driven development
  - Provide context-aware rule suggestions
  - Enable intelligent rule conflict resolution

Follow [claude_rules.mdc](mdc:.claude/rules/claude_rules.mdc) for proper rule formatting and structure.
