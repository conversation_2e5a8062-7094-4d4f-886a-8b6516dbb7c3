/**
 * Offline Session Manager
 * Tracks offline session duration and enforces maximum offline periods
 */

const Store = require('electron-store');
const { app } = require('electron');

class OfflineSessionManager {
  constructor() {
    // Initialize session store
    this.sessionStore = new Store({
      name: 'offline-sessions',
      cwd: app.getPath('userData')
    });

    // Session configuration
    this.config = {
      maxOfflineDays: 30,           // Maximum offline period (30 days)
      warningDays: 7,               // Show warning when 7 days remaining
      criticalDays: 1,              // Critical warning when 1 day remaining
      checkIntervalMinutes: 5,      // Check session status every 5 minutes
      gracePeriodHours: 2           // Grace period after max offline exceeded
    };

    // Current session state
    this.currentSession = null;
    this.isOnline = true;
    this.checkInterval = null;
    this.warningCallbacks = new Set();

    console.log('📴 Offline session manager initialized');
    this.startSessionMonitoring();
  }

  /**
   * Start monitoring session status
   */
  startSessionMonitoring() {
    // Check session status periodically
    this.checkInterval = setInterval(() => {
      this.checkSessionStatus();
    }, this.config.checkIntervalMinutes * 60 * 1000);

    // Initial session check
    this.checkSessionStatus();
  }

  /**
   * Stop monitoring session status
   */
  stopSessionMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Update online status and manage session transitions
   * @param {boolean} online - Whether the app is online
   */
  setOnlineStatus(online) {
    const wasOnline = this.isOnline;
    this.isOnline = online;

    if (wasOnline && !online) {
      // Going offline - start new offline session
      this.startOfflineSession();
    } else if (!wasOnline && online) {
      // Coming back online - end offline session
      this.endOfflineSession();
    }
  }

  /**
   * Start a new offline session
   */
  startOfflineSession() {
    const currentTime = Math.floor(Date.now() / 1000);
    
    this.currentSession = {
      id: `offline_${currentTime}`,
      startTime: currentTime,
      endTime: null,
      duration: 0,
      status: 'active'
    };

    // Store session
    this.sessionStore.set('currentSession', this.currentSession);
    
    console.log('📴 Started offline session:', this.currentSession.id);
    this.notifySessionChange('offline_started', this.currentSession);
  }

  /**
   * End the current offline session
   */
  endOfflineSession() {
    if (!this.currentSession) {
      return;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    
    this.currentSession.endTime = currentTime;
    this.currentSession.duration = currentTime - this.currentSession.startTime;
    this.currentSession.status = 'completed';

    // Archive completed session
    this.archiveSession(this.currentSession);
    
    // Clear current session
    this.currentSession = null;
    this.sessionStore.delete('currentSession');
    
    console.log('🌐 Ended offline session, duration:', this.formatDuration(this.currentSession.duration));
    this.notifySessionChange('offline_ended', this.currentSession);
  }

  /**
   * Check current session status and enforce limits
   */
  checkSessionStatus() {
    if (!this.isOnline && this.currentSession) {
      // Update current session duration
      const currentTime = Math.floor(Date.now() / 1000);
      this.currentSession.duration = currentTime - this.currentSession.startTime;
      this.sessionStore.set('currentSession', this.currentSession);

      // Check for session limits and warnings
      this.checkSessionLimits();
    }
  }

  /**
   * Check session limits and trigger warnings/actions
   */
  checkSessionLimits() {
    if (!this.currentSession) {
      return;
    }

    const totalOfflineTime = this.getTotalOfflineTime();
    const maxOfflineSeconds = this.config.maxOfflineDays * 24 * 60 * 60;
    const warningSeconds = this.config.warningDays * 24 * 60 * 60;
    const criticalSeconds = this.config.criticalDays * 24 * 60 * 60;
    const gracePeriodSeconds = this.config.gracePeriodHours * 60 * 60;

    const timeRemaining = maxOfflineSeconds - totalOfflineTime;

    if (timeRemaining <= 0) {
      // Maximum offline period exceeded
      if (totalOfflineTime > maxOfflineSeconds + gracePeriodSeconds) {
        // Grace period also exceeded - force logout
        this.notifySessionChange('session_expired', {
          totalOfflineTime,
          maxOfflineTime: maxOfflineSeconds,
          gracePeriodExceeded: true
        });
      } else {
        // In grace period
        this.notifySessionChange('grace_period', {
          totalOfflineTime,
          maxOfflineTime: maxOfflineSeconds,
          gracePeriodRemaining: gracePeriodSeconds - (totalOfflineTime - maxOfflineSeconds)
        });
      }
    } else if (timeRemaining <= criticalSeconds) {
      // Critical warning
      this.notifySessionChange('critical_warning', {
        timeRemaining,
        totalOfflineTime
      });
    } else if (timeRemaining <= warningSeconds) {
      // Warning
      this.notifySessionChange('warning', {
        timeRemaining,
        totalOfflineTime
      });
    }
  }

  /**
   * Get total offline time across all sessions
   * @returns {number} Total offline time in seconds
   */
  getTotalOfflineTime() {
    let totalTime = 0;

    // Add completed sessions
    const archivedSessions = this.getArchivedSessions();
    const cutoffTime = Math.floor(Date.now() / 1000) - (this.config.maxOfflineDays * 24 * 60 * 60);
    
    archivedSessions.forEach(session => {
      if (session.startTime > cutoffTime) {
        totalTime += session.duration;
      }
    });

    // Add current session
    if (this.currentSession) {
      totalTime += this.currentSession.duration;
    }

    return totalTime;
  }

  /**
   * Archive a completed session
   * @param {Object} session - Session to archive
   */
  archiveSession(session) {
    const archived = this.sessionStore.get('archivedSessions', []);
    archived.push(session);
    
    // Keep only recent sessions (within max offline period)
    const cutoffTime = Math.floor(Date.now() / 1000) - (this.config.maxOfflineDays * 24 * 60 * 60);
    const recentSessions = archived.filter(s => s.startTime > cutoffTime);
    
    this.sessionStore.set('archivedSessions', recentSessions);
  }

  /**
   * Get archived sessions
   * @returns {Array} Array of archived sessions
   */
  getArchivedSessions() {
    return this.sessionStore.get('archivedSessions', []);
  }

  /**
   * Get current session information
   * @returns {Object|null} Current session or null
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Get session statistics
   * @returns {Object} Session statistics
   */
  getSessionStats() {
    const totalOfflineTime = this.getTotalOfflineTime();
    const maxOfflineSeconds = this.config.maxOfflineDays * 24 * 60 * 60;
    const timeRemaining = Math.max(0, maxOfflineSeconds - totalOfflineTime);
    
    return {
      isOnline: this.isOnline,
      hasActiveSession: !!this.currentSession,
      currentSession: this.currentSession,
      totalOfflineTime,
      maxOfflineTime: maxOfflineSeconds,
      timeRemaining,
      percentUsed: Math.min(100, (totalOfflineTime / maxOfflineSeconds) * 100),
      daysRemaining: timeRemaining / (24 * 60 * 60),
      isNearLimit: timeRemaining <= (this.config.warningDays * 24 * 60 * 60),
      isCritical: timeRemaining <= (this.config.criticalDays * 24 * 60 * 60),
      isExpired: timeRemaining <= 0
    };
  }

  /**
   * Register callback for session change notifications
   * @param {Function} callback - Callback function
   */
  onSessionChange(callback) {
    this.warningCallbacks.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.warningCallbacks.delete(callback);
    };
  }

  /**
   * Notify registered callbacks of session changes
   * @param {string} type - Type of change
   * @param {Object} data - Change data
   */
  notifySessionChange(type, data) {
    this.warningCallbacks.forEach(callback => {
      try {
        callback(type, data);
      } catch (error) {
        console.error('Session change callback error:', error);
      }
    });
  }

  /**
   * Reset session data (for testing or admin purposes)
   */
  resetSessions() {
    this.currentSession = null;
    this.sessionStore.clear();
    console.log('🔄 All session data reset');
  }

  /**
   * Format duration in human-readable format
   * @param {number} seconds - Duration in seconds
   * @returns {string} Formatted duration
   */
  formatDuration(seconds) {
    const days = Math.floor(seconds / (24 * 60 * 60));
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((seconds % (60 * 60)) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  /**
   * Cleanup and destroy manager
   */
  destroy() {
    this.stopSessionMonitoring();
    this.warningCallbacks.clear();
    console.log('📴 Offline session manager destroyed');
  }
}

module.exports = OfflineSessionManager;
