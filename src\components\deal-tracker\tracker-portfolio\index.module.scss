@import "../../../../styles/_mixins.scss";

.wrapper {
  border-left: 4px solid #d59c00;
  background-color: #ffffff;
  @include scrollbars(0px);
}

.header {
  top: 0;
  background-color: #f1f3f2;

  overflow: scroll;

  @include scrollbars(0px);
}

.view {
  @include font(0.9em, 600);
  @include scrollbars(0px);
  color: #181818;

  @media screen and (max-width: 1100px) {
    @include font(0.75em, 600);
  }
}

.cursor {
  cursor: pointer;
}

.reveal {
  @extend .cursor;

  transition: 0.3s;

  &:hover {
    opacity: 0.6;
  }
}

.add {
  @extend .cursor;

  transition: 0.3s;

  &:hover {
    opacity: 0.6;
  }
}

.count {
  width: 20px;
  height: 20px;
  background-color: #ffffff;
  color: rgb(165, 167, 166);
  @include font(0.9em, 600);
}

.portfolioName {
  background-color: #ffffff;
  height: 15vh;
  margin-right: 2px;
  overflow-y: auto;
  cursor: pointer;
  font-size: 0.9em;
  color: #181818;
  font-weight: 400;

  @media screen and (max-width: 1000px) {
    @include font(0.85em, 400);
  }
}

.name:hover {
  opacity: 0.7;
}

.name {
  transition: 0.4s;
}

.portfolioName::-webkit-scrollbar {
  width: 2px;
  height: 1px;
  border-radius: 5px;
}
.portfolioName::-webkit-scrollbar-thumb {
  background: #a1aaa6;
  border-radius: 5px;
}
.portfolioName::-webkit-scrollbar-track {
  background: #e2e2e2;
  border-radius: 5px;
}
