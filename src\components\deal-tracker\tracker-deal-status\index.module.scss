@import "../../../../styles/_mixins.scss";

.font {
  @include font(0.95em, 600);
}

.border {
  border-radius: 20px;
}

.padding {
  padding: 4px 10px;
}

.paid {
  @extend .font;
  @extend .border;
  @extend .padding;
  border: 2px solid var(--risk-primary);
  border-radius: 20px;
  background-color: transparent;
  color: var(--risk-primary);
}
.active {
  @extend .font;
  @extend .border;
  @extend .padding;
  background-color: var(--risk-primary);
  color: #ffffff;
}
.on-hold {
  @extend .font;
  @extend .border;
  @extend .padding;
  background-color: #d59c00;
  color: #ffffff;
}
.dead {
  @extend .font;
  @extend .border;
  @extend .padding;
  background-color: #910000;
  color: #ffffff;
}

.archive {
  @extend .font;
  @extend .border;
  @extend .padding;
  background-color: #e8e8e8;
  color: #a0a0a0;
}

.closed {
  @extend .font;
  @extend .border;
  @extend .padding;
  background-color: #595959;
  color: #ffffff;
}
