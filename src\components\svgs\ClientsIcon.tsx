import React from "react";

export default function ClientsIcon({ color }: { color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="11"
      height="13"
      fill="none"
      viewBox="0 0 11 13"
    >
      <mask
        id="mask0_1722_767"
        style={{ maskType: "alpha" }}
        width="11"
        height="5"
        x="0"
        y="8"
        maskUnits="userSpaceOnUse"
      >
        <path
          fill={color}
          fillRule="evenodd"
          d="M.5 8.06h9.9v4.609H.5V8.06z"
          clipRule="evenodd"
        ></path>
      </mask>
      <g mask="url(#mask0_1722_767)">
        <path
          fill={color}
          fillRule="evenodd"
          d="M5.45 8.998c-2.663 0-4.013.457-4.013 1.36 0 .911 1.35 1.373 4.014 1.373 2.662 0 4.011-.457 4.011-1.36 0-.912-1.349-1.373-4.011-1.373zm0 3.67c-1.224 0-4.95 0-4.95-2.31 0-2.06 2.826-2.298 4.95-2.298 1.225 0 4.95 0 4.95 2.31 0 2.06-2.825 2.299-4.95 2.299z"
          clipRule="evenodd"
        ></path>
      </g>
      <mask
        id="mask1_1722_767"
        style={{ maskType: "alpha" }}
        width="7"
        height="7"
        x="2"
        y="0"
        maskUnits="userSpaceOnUse"
      >
        <path
          fill={color}
          fillRule="evenodd"
          d="M2.131.25H8.77v6.637H2.13V.25z"
          clipRule="evenodd"
        ></path>
      </mask>
      <g mask="url(#mask1_1722_767)">
        <path
          fill={color}
          fillRule="evenodd"
          d="M5.45 1.142A2.43 2.43 0 003.025 3.57a2.42 2.42 0 002.408 2.426l.019.446v-.446a2.429 2.429 0 002.425-2.426 2.428 2.428 0 00-2.425-2.427zm0 5.745h-.02a3.312 3.312 0 01-3.299-3.32A3.322 3.322 0 015.451.25a3.322 3.322 0 013.318 3.319A3.321 3.321 0 015.45 6.887z"
          clipRule="evenodd"
        ></path>
      </g>
    </svg>
  );
}
