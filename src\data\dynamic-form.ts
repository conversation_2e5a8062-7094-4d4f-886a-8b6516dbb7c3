import { IDynamicForm } from "../models/dynamicForm";

export const dynamicFormData: Record<string, Array<IDynamicForm>> = {
  orderSummary: [
    {
      name: "loan_closing_date",
      label: "Loan Closing Date:",
      placeholder: "Loan Closing Date",
      type: "date",
    },
    {
      name: "reviewed_needed_date",
      label: "Review Need by Date:",
      placeholder: "Review Need by Date",
      type: "date",
    },
    {
      name: "requested_items",
      label: "Requested Items:",
      placeholder: "Requested Items",
      type: "multiSelect",
      options: [
        {
          label: "Flood Zone Determination",
          value: "Flood Zone Determination",
        },
        {
          label: "Insurance Review",
          value: "Insurance Review",
        },
      ],
    },
    {
      name: "status",
      label: "Status:",
      placeholder: "Status",
      type: "select",
      options: ["Active", "On-hold", "Closed", "Dead", "Paid", "Archive"],
    },
    {
      name: "revenue",
      label: "Revenue:",
      placeholder: "Revenue",
      type: "number",
    },
    {
      name: "is_portfolio",
      label: "Is this order for a portfolio (multiple properties)?",
      placeholder: "Is this order for a portfolio (multiple properties)?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "portfolio_name",
      label: "Portfolio Name:",
      placeholder: "Portfolio Name",
      type: "text",
    },
    {
      name: "portfolio_property_count",
      label: "How many properties?:",
      placeholder: "How many properties?",
      type: "number",
    },
    {
      name: "loan_per_property",
      label:
        "Is this one loan for multiple properties, or a separate loan for each?:",
      placeholder: "Loan Per Property",
      type: "select",
      options: ["One Loan", "Seperate Loan For Each Property"],
    },
    {
      name: "is_portfolio_review_one_report",
      label: "Is a separate Insurance Review Report needed for each property?:",
      placeholder:
        "Is a separate Insurance Review Report needed for each property?",
      type: "select",
      options: ["Yes", "No"],
    },
  ],

  loanSummary: [
    {
      name: "loan_execution",
      label: "Loan Execution:",
      placeholder: "Loan Execution",
      type: "select",
      options: ["Balance Sheet", "CLO", "CMBS", "Fannie Mae", "Freddie Mac"],
    },

    {
      name: "loan_type",
      label: "Loan Type:",
      placeholder: "Loan Type",
      type: "select",
      options: ["Acquisition", "Assumption", "Refinance", "Supplemental"],
    },
    {
      name: "loan_number",
      label: "Loan Number/ID:",
      placeholder: "Loan Number/ID",
      type: "text",
    },
    {
      name: "mortgage_lender",
      label: "Mortgage Lender:",
      placeholder: "Mortgage Lender",
      type: "text",
      required: true,
    },
    {
      name: "mortgage_loan_amount",
      label: "Mortgage Loan Amount:",
      placeholder: "Mortgage Loan Number",
      type: "text",
      required: true,
    },
    {
      name: "has_upb_on_existing_lien",
      label: "UPB on Exisiting Liens?:",
      placeholder: "UPB on Exisiting Liens?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "upb_amount",
      label: "Enter Amount:",
      placeholder: "Enter Amount",
      type: "number",
      required: true,
    },
    {
      name: "has_mezz_lender",
      label: "Has Mezz Lender?:",
      placeholder: "Has Mezz Lender?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "mezz_lender",
      label: "Mezz Lender:",
      placeholder: "Mezz Lender",
      type: "text",
    },
    {
      name: "mezz_loan_amount",
      label: "Mezz Loan Amount:",
      placeholder: "Mezz Loan Amount",
      type: "text",
    },
    {
      name: "has_repo_whse_lender",
      label: "Has Repo/Warehouse Lender?:",
      placeholder: "Has Repo/Warehouse Lender?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "repo_whse_lender",
      label: "Repo/Warehouse Lender:",
      placeholder: "Repo/Warehouse Lender",
      type: "text",
    },
    {
      name: "is_repo_reps_provided",
      label: "Repo Reps provided:",
      placeholder: "Repo Reps provided",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "repo_reps_upload_url",
      label: "Upload Repo Reps:",
      placeholder: "Upload Repo Reps",
      type: "file",
    },
    {
      name: "borrower_sponsor_name",
      label: "Borrower/Sponsor Name:",
      placeholder: "Borrower/Sponsor Name",
      type: "text",
    },
    {
      name: "is_repeat_borrower_sponsor",
      label: "Repeat Borrower/Sponsor?:",
      placeholder: "Repeat Borrower/Sponsor?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "recently_reviewed_deal",
      label: "Name of Recently reviewed deal:",
      placeholder: "Name of recently reviewed deal",
      type: "text",
    },
    {
      name: "borrowing_entity_name",
      label: "Borrowing Entity Name:",
      placeholder: "Borrowing Entity Name",
      type: "text",
    },

    {
      name: "is_load_agreement_attached",
      label: "Attach Loan Agreement?:",
      placeholder: "Attach Loan Agreement?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "loan_agreement_upload_url",
      label: "Upload Loan Agreement:",
      placeholder: "Upload Loan Agreement",
      type: "file",
    },
  ],

  propertySummary: [
    {
      name: "property_name",
      label: "Property Name:",
      placeholder: "Property Name",
      type: "text",
      occupy: true,
    },
    {
      name: "property_street",
      label: "Property Address:",
      placeholder: "Property Address",
      type: "text",
      occupy: true,
    },
    {
      name: "property_city",
      label: "Property City:",
      placeholder: "Property City",
      type: "text",
    },
    {
      name: "property_state",
      label: "Property State:",
      placeholder: "Property State",
      type: "text",
    },
    {
      name: "property_zipcode",
      label: "Property Zipcode:",
      placeholder: "Property Zipcode",
      type: "text",
    },
    // {
    //   name: "property_country",
    //   label: "Property Country:",
    //   placeholder: "Property Country",
    //   type: "text",
    // },
    {
      name: "property_type",
      label: "Property Type:",
      placeholder: "Property Type",
      type: "select",
      options: [
        "Healthcare Facility",
        "Hotel",
        "Mixed Use",
        "Multifamily",
        "Office",
        "Retail",
        "Storage Facility",
        "Student Housing",
        "Warehouse",
      ],
    },

    {
      name: "is_coop",
      label: "Co-op?:",
      placeholder: "Co-op",
      type: "select",
      options: ["Yes", "No"],
    },

    {
      name: "insurable_value",
      label: "Insurable Value:",
      placeholder: "Insurable Value",
      type: "number",
    },
    {
      name: "land_value",
      label: "Land Value",
      placeholder: "Land Value",
      type: "number",
    },
    {
      name: "uw_annual_egi",
      label: "UW Annual EGI:",
      placeholder: "UW Annual EGI",
      type: "number",
    },

    {
      name: "year_built",
      label: "Year Built:",
      placeholder: "Year Built",
      type: "text",
    },
    {
      name: "age_of_roofs",
      label: "Age of Roofs:",
      placeholder: "Age of Roofs",
      type: "text",
    },
    {
      name: "number_of_buildings",
      label: "Number of Buildings:",
      placeholder: "Number of Buildings",
      type: "text",
    },

    {
      name: "number_of_stories",
      label: "Number of Stories:",
      placeholder: "Number of Stories",
      type: "text",
    },
    {
      name: "number_of_units",
      label: "Number of Units:",
      placeholder: "Number of Units",
      type: "text",
    },
    {
      name: "number_of_licensed_beds",
      label: "Number of Licensed Beds:",
      placeholder: "Number of Licensed Beds",
      type: "text",
    },
    {
      name: "problematic_bldg_materials",
      label: "Problematic Bldg Materials?:",
      placeholder: "Problematic Bldg Materials?",
      type: "select",
      options: [
        "Aluminium wiring",
        "Galvanized piping",
        "Polybutylene piping",
        "Stab-lok panales",
      ],
    },
    {
      name: "has_elevators",
      label: "Elevators?:",
      placeholder: "Elevators",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "has_boilers",
      label: "Boilers?:",
      placeholder: "Boilers?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "has_high_pressure",
      label: "High Pressure?:",
      placeholder: "High Pressure?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "is_centralized",
      label: "Centralized?:",
      placeholder: "Centralized?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "is_state_regulated",
      label: "State Regulated?:",
      placeholder: "State Regulated?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "flood_zone",
      label: "Flood Zone?:",
      placeholder: "Flood Zone?",
      type: "select",
      options: [
        "A1-30 (specifiy)",
        "AH",
        "AO",
        "AR",
        "A99",
        "V",
        "VE",
        "V1-30 (specify)",
        "D",
        "B",
        "B/X",
        "C",
        "X",
        "X500",
        "AE",
        "VO",
        "Other",
      ],
    },
    {
      name: "flood_zone_specify",
      label: "Flood Zone (specify):",
      placeholder: "Flood Zone (specify)",
      type: "text",
      disabled: true,
      occupy: true,
    },
    {
      name: "seismic_pml_sel",
      label: "Seismic PML/SEL?:",
      placeholder: "Seismic PML/SEL?",
      type: "select",
      options: [
        "TBD",
        "Less than or equal to 20",
        "Greater than 20% less than 40%	",
        "40% or greater	",
      ],
    },
    {
      name: "localized_regional_perils",
      label: "Localized/Regional Perils?:",
      placeholder: "Localized/Regional Perils?",
      type: "select",
      options: [
        "TBD",
        "Avalanche",
        "Mine Subsidence",
        "Sinkhole",
        "Volcanic Eruption",
        "Wildfire",
        "N/A",
      ],
    },
    {
      name: "zoning_status",
      label: "Zoning Status:",
      placeholder: "Zoning Status",
      type: "select",
      options: ["Legal Conforming", "Legal Noncomforming", "Noncomfirming"],
    },
    {
      name: "be_has_emp_at_property",
      label: "BE has Employees at the Property?:",
      placeholder: "BE has Employees at the Property?",
      type: "select",
      options: ["TBD", "Yes", "No"],
    },
    {
      name: "be_operates_auto_at_property",
      label: "BE operates autos at the Property?:",
      placeholder: "BE operates autos at the Property?",
      type: "select",
      options: ["TBD", "Yes", "No"],
    },
    {
      name: "is_env_insurance_req",
      label: "Environmental Insurance Required?:",
      placeholder: "Environmental Insurance Required?",
      type: "select",
      options: ["TBD", "Yes", "No"],
    },
    {
      name: "any_constr_at_property",
      label: "Any Construction at the Property?:",
      placeholder: "Any Construction at the Property?",
      type: "select",
      options: ["TBD", "Yes", "No"],
    },
    {
      name: "is_property_part_of_condo",
      label: "Is Property part of a Condo?:",
      placeholder: "Is Property part of a Condo?",
      type: "select",
      options: ["TBD", "Yes", "No"],
    },
    {
      name: "is_borrower_tenant_under_lease",
      label: "Is Borrower a tenant under any lease?:",
      placeholder: "Is Borrower a tenant under any lease?",
      type: "select",
      options: ["TBD", "Yes", "No"],
    },
    {
      name: "is_tenant_req_to_insure_collateral",
      label: "Is any tenant required to insure collateral?:",
      placeholder: "Is any tenant required to insure collateral?",
      type: "select",
      options: ["TBD", "Yes", "No"],
    },
  ],

  dueDiligenceSummary: [
    {
      name: "is_appraisal_attached",
      label: "Attach Appraisal?:",
      placeholder: "Attach Appraisal?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "appraisal_upload_url",
      label: "Upload Appraisal:",
      placeholder: "Upload Appraisal",
      type: "file",
    },
    // {
    //   name: "is_esa_attached",
    //   label: "Attach Environmental Site Assessment (ESA)?:",
    //   placeholder: "Attach Environmental Site Assessment (ESA)?",
    //   type: "select",
    //   options: ["Yes", "No"],
    // },

    // {
    //   name: "esa_upload_url",
    //   label: "Upload Environmental Site Assessment (ESA):",
    //   placeholder: "Upload Environmental Site Assessment (ESA)",updated
    //   type: "file",
    // },
    {
      name: "is_fzd_attached",
      label: "Attach Flood Zone Determination (FZD)?:",
      placeholder: "Attach Flood Zone Determination (FZD)?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "fzd_upload_url",
      label: "Upload Flood Zone Determination (FZD):",
      placeholder: "Upload Flood Zone Determination (FZD)",
      type: "file",
    },
    {
      name: "is_pca_attached",
      label: "Attach Property Condition Assessment (PCA)?:",
      placeholder: "Attach Property Condition Assessment (PCA)?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "pca_upload_url",
      label: "Upload Property Condition Assessment (PCA):",
      placeholder: "Upload Property Condition Assessment (PCA)",
      type: "file",
    },
    {
      name: "is_sra_attached",
      label: "Attach Seismic Risk Assessment (SRA)?:",
      placeholder: "Attach Seismic Risk Assessment (SRA)?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "sra_upload_url",
      label: "Upload Seismic Risk Assessment (SRA):",
      placeholder: "Upload Seismic Risk Assessment (SRA)",
      type: "file",
    },
    {
      name: "is_pzr_attached",
      label: "Attach Property Zoning Report (PZR)?:",
      placeholder: "Attach Property Zoning Report (PZR)?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "pzr_upload_url",
      label: "Upload Property Zoning Report (PZR):",
      placeholder: "Upload Property Zoning Report (PZR)",
      type: "file",
    },
  ],
  contactsSummary: [
    {
      name: "",
      placeholder: "",
      display: "linear",
      type: "",
      label: "Lender Loan Contact",
      children: [
        {
          name: "lender_cc_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "lender_cc_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "lender_cc_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },

    {
      name: "",
      placeholder: "",
      type: "",
      display: "linear",
      label: "Lender Underwriting Contact:",
      children: [
        {
          name: "lender_uw_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "lender_uw_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "lender_uw_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },

    {
      name: "",
      placeholder: "",
      type: "",
      display: "linear",
      label: "Counsel Contact:",
      children: [
        {
          name: "counsel_contact_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "counsel_contact_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "counsel_contact_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },

    {
      name: "",
      placeholder: "",
      type: "",
      display: "linear",
      label: "Borrower Contact:",
      children: [
        {
          name: "borrower_contact_name",

          placeholder: "Name",
          type: "text",
        },
        {
          name: "borrower_contact_email",

          placeholder: "Email",
          type: "email",
        },
        {
          name: "borrower_contact_phone",

          placeholder: "Phone",
          type: "tel",
        },
      ],
    },

    {
      name: "",
      placeholder: "",
      type: "",
      display: "linear",
      label: "Borrower’s Insurance Contact:",
      children: [
        {
          name: "borrower_ic_name",

          placeholder: "Name",
          type: "text",
        },
        {
          name: "borrower_ic_email",

          placeholder: "Email",
          type: "email",
        },
        {
          placeholder: "Phone",
          type: "tel",
          name: "borrower_ic_phone",
        },
      ],
    },
  ],
  condoSummary: [
    {
      name: "does_borr_entity_control_board",
      label: "Does Borrowing Entity control the board?:",
      placeholder: "Does Borrowing Entity control the board?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "is_site_plan_attached",
      label: "Attach Site plan?:",
      placeholder: "Attach Site plan?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "site_plan_upload_url",
      label: "Upload Site plan:",
      placeholder: "Upload Site plan",
      type: "file",
    },
    {
      name: "is_condo_bylaws_attached",
      label: "Attach Condo By-Laws?:",
      placeholder: "Attach Condo By-Laws?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "condo_byloaws_upload_url",
      label: "Upload Condo By-Laws:",
      placeholder: "Upload Site plan",
      type: "file",
    },
    {
      name: "total_condo_units",
      label: "Total number of units in condo:",
      placeholder: "Total number of units in condo",
      type: "text",
    },
    {
      name: "structure_sqft",
      label:
        "Square footage of entire structure Including non-owned units Including non-owned units:",
      placeholder:
        "Square footage of entire structure Including non-owned units Including non-owned units",
      type: "text",
    },
    {
      name: "structure_insurable_val",
      label: "Insurable Value of entire structure:",
      placeholder: "Insurable Value of entire structure",
      type: "text",
    },
    {
      name: "total_borr_owned_units",
      label: "Total number of Borrower-owned units:",
      placeholder: "Total number of Borrower-owned units",
      type: "text",
    },
    {
      name: "borr_owned_units_sqft",
      label: "Square footage of Borrower-owned units:",
      placeholder: "Square footage of Borrower-owned units",
      type: "text",
    },
    {
      name: "borr_owned_units_insurable_val",
      label: "Insurable Value of Borrower-owned units:",
      placeholder: "Insurable Value of Borrower-owned units",
      type: "text",
    },

    {
      name: "",
      placeholder: "",
      display: "linear",
      type: "",
      label: "Condo Association’s Contact:",
      children: [
        {
          name: "condo_ac_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "condo_ac_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "condo_ac_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },

    {
      name: "",
      placeholder: "",
      display: "linear",
      type: "",
      label: "Condo Association’s Insurance Contact:",
      children: [
        {
          name: "condo_aic_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "condo_aic_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "condo_aic_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },
  ],
  constructionSummary: [
    {
      name: "is_proj_schedule_attached",
      label: "Attach Project schedule?:",
      placeholder: "Attach Project schedule?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "proj_schedule_upload_url",
      label: "Upload Project schedule:",
      placeholder: "Upload Project schedule",
      type: "file",
    },
    {
      name: "is_gmp_contract_attached",
      label: "Attach GMP Contract?:",
      placeholder: "Attach GMP Contract?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "gmp_contract_upload_url",
      label: "Upload GMP Contract:",
      placeholder: "Upload GMP Contract",
      type: "file",
    },
    {
      name: "is_hsc_breakdown_attached",
      label: "Attach Hard & Soft Costs breakdown?:",
      placeholder: "Attach Hard & Soft Costs breakdown?",
      type: "select",
      options: ["Hard", "Soft"],
    },
    {
      name: "hsc_upload_url",
      label: "Upload Hard & Soft Costs breakdown:",
      placeholder: "Upload Hard & Soft Costs breakdown",
      type: "file",
    },
    {
      name: "has_demo_abatement",
      label: "Any demo/abatement?:",
      placeholder: "Any demo/abatement?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "existing_structures_insurable_val",
      label: "Insurable Value of existing structures:",
      placeholder: "Insurable Value of existing structures",
      type: "text",
    },
    {
      name: "est_annual_inc_on_completion",
      label: "Estimated annual income upon completion:",
      placeholder: "Estimated annual income upon completion",
      type: "text",
    },
    {
      name: "",
      placeholder: "",
      display: "linear",
      type: "",
      label: "GC’s Contact:",
      children: [
        {
          name: "gc_contact_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "gc_contact_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "gc_contact_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },

    // {
    //   name: "",
    //   placeholder: "",
    //   display: "linear",
    //   type: "",
    //   label: "GC’s Insurance Contact:",
    //   children: [
    //     {
    //       name: "gc_ic_name",
    //       placeholder: "Name",
    //       type: "text",
    //     },
    //     {
    //       name: "gc_ic_email",
    //       placeholder: "Email",
    //       type: "email",
    //     },
    //     {
    //       name: "gc_ic_email",
    //       placeholder: "Phone",
    //       type: "tel",
    //     },
    //   ],
    // },

    {
      name: "",
      placeholder: "",
      display: "linear",
      type: "",
      label: "Architect’s Contact:",
      children: [
        {
          name: "architect_contact_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "architect_contact_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "architect_contact_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },

    {
      name: "",
      placeholder: "",
      display: "linear",
      type: "",
      label: "Architects’s Insurance Contact:",
      children: [
        {
          name: "architect_ic_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "architect_ic_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "architect_ic_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },
  ],
  environmentalSummary: [
    {
      name: "is_esa_attached",
      label: "Attach Environmental Site Assessment (ESA)?:",
      placeholder: "Attach Environmental Site Assessment (ESA)?",
      type: "select",
      options: ["Yes", "No"],
    },

    {
      name: "esa_upload_url",
      label: "Upload Environmental Site Assessment (ESA):",
      placeholder: "Upload Environmental Site Assessment (ESA)",
      type: "file",
    },
    {
      name: "loan_term_incl_ext_option",
      label: "Loan term including extension options:",
      placeholder: "Loan term including extension options",
      type: "text",
    },
    {
      name: "worst_case_cleanup_cost_est",
      label: "Worst case cleanup cost estimate:",
      placeholder: "Worst case cleanup cost estimate",
      type: "text",
    },
    {
      name: "is_coverage_req_in_lieu",
      label: "Is coverage required in lieu of a Phase II?:",
      placeholder: "Is coverage required in lieu of a Phase II?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "is_sponsor_env_ind",
      label: "Is there a sponsor environmental indemnity?:",
      placeholder: "Is there a sponsor environmental indemnity?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "is_add_security_req",
      label: "If yes, required as additional security?:",
      placeholder: "If yes, required as additional security?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "add_resaon_for_coverage",
      label: "Additional detail as to why coverage is required:",
      placeholder: "Additional detail as to why coverage is required",
      type: "text",
    },
  ],
  tenantsSummary: [
    {
      name: "is_borr_lease_attached",
      label: "Attach Borrower Lease agreement?:",
      placeholder: "Attach Borrower Lease agreement?:",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "upload_borr_lease_url",
      label: "Upload Borrower Lease agreement:",
      placeholder: "Upload Borrower Lease agreement",
      type: "file",
    },
    {
      name: "type_of_lease",
      label: "of lease:",
      placeholder: "of lease",
      type: "text",
    },
    {
      name: "is_tenant_lease_attached",
      label: "Attach Tenant Lease agreement?:",
      placeholder: "Attach Tenant Lease agreement?",
      type: "select",
      options: ["Yes", "No"],
    },
    {
      name: "tenant_lease_upload_url",
      label: "Upload Tenant Lease agreement:",
      placeholder: "Upload Tenant Lease agreement",
      type: "file",
    },
    {
      name: "tenant_occ_insurable_val",
      label: "Insurable Value of tenant occupancy:",
      placeholder: "Insurable Value of tenant occupancy",
      type: "text",
    },
    {
      name: "tenant_occ_address",
      occupy: true,
      label: "Address of tenant occupancy:",
      placeholder: "Address of tenant occupancy",
      type: "text",
    },
    {
      name: "tenant_occ_city",
      label: "City:",
      placeholder: "City",
      type: "text",
    },
    {
      name: "tenant_occ_state",
      label: "State:",
      placeholder: "State",
      type: "text",
    },
    {
      name: "tenant_occ_zipcode",
      label: "Zipcode:",
      placeholder: "Zipcode",
      type: "text",
    },
    // {
    //   name: "tenant_occ_country",
    //   label: "Country:",
    //   placeholder: "Country",
    //   type: "text",
    // },

    {
      name: "annual_inc_from_tenant",
      label: "Annual income from tenant:",
      placeholder: "Annual income from tenant",
      type: "text",
    },

    {
      name: "",
      placeholder: "",
      display: "linear",
      type: "",
      label: "Tenant’s Contact:",
      children: [
        {
          name: "tenant_contact_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "architect_contact_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "tenant_contact_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },

    {
      name: "",
      placeholder: "",
      display: "linear",
      type: "",
      label: "Tenant’s Insurance Contact:",
      children: [
        {
          name: "tenant_ic_name",
          placeholder: "Name",
          type: "text",
        },
        {
          name: "tenant_ic_email",
          placeholder: "Email",
          type: "email",
        },
        {
          name: "tenant_ic_phone",
          placeholder: "Phone",
          type: "tel",
        },
      ],
    },
  ],
};
