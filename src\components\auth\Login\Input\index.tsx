import React from "react";
import { useField } from "formik";
import styles from "./index.module.css";
import { ErrorMessage } from "formik";
import { HidePasswordIcon, ShowPasswordIcon } from "../../../../exports";

export const Input = ({ name, label, ...props }: any) => {
  const [field, meta] = useField(name);

  return (
    <section className="mt-3 ">
      <div>
        <label className={`${styles.label} mb-0`} htmlFor={field.name}>
          {label}
        </label>
      </div>
      <div>
        <input
          arial-label={label}
          className={`form-control ${
            meta.error && meta.touched ? `${styles.inputBorderError}` : ""
          } py-3 px-3 ${styles.inputBorder} `}
          {...field}
          {...props}
        />
      </div>

      <ErrorMessage
        name={field.name}
        component="div"
        className="small text-danger px-3"
      />
    </section>
  );
};

export const InputPassword = ({
  name,
  label,
  handleToggle,
  toggleType,
  ...props
}: any) => {
  const [field, meta] = useField(name);
  return (
    <section className="mt-3">
      <div>
        <label className={`${styles.label} mb-0`} htmlFor={field.name}>
          {label}
        </label>
      </div>

      <div
        className={`d-flex align-items-center p-0 m-0 ${
          meta.error && meta.touched
            ? `${styles.inputBorderError}`
            : `${styles.inputBorder}`
        } `}
      >
        <input
          className={`border py-2 border-0 px-3 flex-fill ${styles.stylePassword}`}
          {...field}
          {...props}
        />
        <div className="py-2 px-2" onClick={handleToggle}>
          {toggleType ? (
            <img
              src={ShowPasswordIcon}
              className="img-fluid"
              alt="show password icon"
            />
          ) : (
            <img
              src={HidePasswordIcon}
              className="img-fluid"
              alt="show password icon"
            />
          )}
        </div>
      </div>
      {/*<ErrorMessage
        name={field.name}
        component="div"
        className="small text-danger px-3"
      />*/}
    </section>
  );
};
