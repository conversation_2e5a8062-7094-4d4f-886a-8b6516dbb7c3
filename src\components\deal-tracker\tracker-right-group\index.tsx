import React from "react";

import styles from "./index.module.scss";

interface ITrackerLeftGroup {
  children: React.ReactNode;
  formik?: any;
  data?: any;
  setIsNote?: (arg: boolean) => void;
  handleSubmitNote?: () => void;
}

const TrackerRightGroup = ({
  children,
  formik,
  setIsNote,
  handleSubmitNote,
  data,
}: ITrackerLeftGroup) => {
  return (
    <section
      className={`col-12 col-md-3 col-lg-3 col-xl-3 ${styles.outerWrapper}`}
    >
      <div className={`${styles.wrapper}`}>
        {React.Children.map(children, (child: any) => {
          return React.cloneElement(child, {
            formik,
            data,
            setIsNote,
            handleSubmitNote,
          });
        })}
      </div>
    </section>
  );
};

export default TrackerRightGroup;
