.wrapper {
  height: 100vh;
  background: #ffffff;
}

.innerWrapper {
  height: 100vh;
}

.inputBorder {
  background-color: #f1f3f2;
  border: 1px solid #a1aaa6;
  border-radius: 20px;
  min-width: 368px;
}

.inputBorderError {
  background-color: #f1f3f2;
  border: 1px solid #db4655;
  border-radius: 20px;
  min-width: 368px;
}
.inputBorder:focus {
  border: 1px solid #f1f3f2;
  background-color: #f1f3f2;
  box-shadow: 5px 3px 5px #a1aaa6;
  outline: 0;
}

.stylePassword {
  outline: 0;
  border: 0;
  border-radius: 20px;
}
.stylePassword:focus {
  outline: 0;
  border: 0;
}
.login {
  font-size: 1.3em;
  color: #3a3b3c;
}
.enterDetails {
  font-family: Avenir LT Std;
  font-size: 1em;
  color: #a1aaa6;
}

.label {
  color: #181818;
  font-size: 0.97em;
}

.button {
  min-width: 368px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.97em;
  background: rgba(46, 113, 111, 1);
  color: #ffffff;
  outline: 0;
  border: 0;
  font-weight: 600;
  transition: 0.3s ease-in-out;
}

.button:hover {
  font-size: 0.97em;
  color: #ffffff;
  opacity: 0.8;
}

.forgetPassword {
  font-size: 0.98em;
  color: #a1aaa6;
}

@media screen and (max-width: 375px) {
  .inputBorder {
    min-width: 90vw;
  }
}
