import React from "react";
import styles from "./index.module.scss";
import { useHistory } from "react-router";
import { setLastLocation } from "../../../utils/setLastLocation";

interface ITabeBodyProps {
  getTableProps: () => { [key: string]: any };
  headerGroups: Array<any>;
  getTableBodyProps: () => { [key: string]: any };
  page: Array<any>;
  prepareRow: (arg: any) => void;
}

export default function TableBody({
  getTableProps,
  headerGroups,
  getTableBodyProps,
  page,
  prepareRow,
}: ITabeBodyProps) {
  const history = useHistory();
  return (
    <div className={`table-responsive ${styles.tableWrapper}`}>
      <table
        className={`table table-borderless  ${styles.tableInnerWrapper}`}
        {...getTableProps()}
      >
        <thead className={`${styles.tableHeader} mb-3`}>
          {headerGroups.map((headerGroup) => (
            <tr {...headerGroup.getHeaderGroupProps()} className={styles.rows}>
              {headerGroup.headers.map((column: any) => (
                <td {...column.getHeaderProps()} className={styles.tableTh}>
                  {column.render("Header")}
                </td>
              ))}
            </tr>
          ))}
        </thead>

        <tbody {...getTableBodyProps()} className={`${styles.tableBody}`}>
          {page.map((row) => {
            prepareRow(row);
            return (
              <tr
                {...row.getRowProps()}
                onClick={() => {
                  console.log('=== DEAL ROW CLICKED ===');
                  console.log('Row data:', row?.original);
                  console.log('Deal ID:', row?.original?.id);
                  console.log('Navigating to:', `/deals/${row?.original?.id}`);
                  console.log('History object:', history);
                  console.log('Current location before push:', history.location);
                  console.log('========================');

                  setLastLocation("Order Summary");

                  try {
                    history.push(`/deals/${row?.original?.id}`);
                    console.log('✅ history.push called successfully');
                    console.log('New location after push:', history.location);
                  } catch (error) {
                    console.error('❌ Error during history.push:', error);
                  }
                }}
              >
                {row.cells.map((cell: any) => (
                  <td {...cell.getCellProps()} className={styles.tableRow}>
                    {cell.render("Cell")}
                  </td>
                ))}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}
