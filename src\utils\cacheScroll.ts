import { readLastLocation } from "./setLastLocation";
import { handleRef } from "./handleRef";

export const cacheScroll = ({
  orderSummaryRef,
  loanSummaryRef,
  propertySummaryRef,
  dueDiligenceRef,
  contactsRef,
  condoRef,
  constructionRef,
  environmentalRef,
  tenantRef,
}: any) => {
  if (readLastLocation()) {
    switch (readLastLocation()?.toLowerCase()) {
      case "loan summary":
        if (loanSummaryRef) handleRef(loanSummaryRef, readLastLocation());
        break;
      case "order summary":
        if (orderSummaryRef) handleRef(orderSummaryRef, readLastLocation());
        break;
      case "property summary":
        if (propertySummaryRef)
          handleRef(propertySummaryRef, readLastLocation());
        break;
      case "due dilegence":
        if (dueDiligenceRef) handleRef(dueDiligenceRef, readLastLocation());
        break;
      case "contacts":
        if (contactsRef) handleRef(contactsRef, readLastLocation());
        break;
      case "condo":
        if (condoRef) handleRef(condoRef, readLastLocation());
        break;
      case "construction":
        handleRef(constructionRef, readLastLocation());
        break;
      case "environmental":
        if (environmentalRef) handleRef(environmentalRef, readLastLocation());
        break;
      case "tenant summary":
        if (tenantRef) handleRef(tenantRef, readLastLocation());
        break;
      default:
        handleRef(orderSummaryRef, readLastLocation());
    }
  }
};
