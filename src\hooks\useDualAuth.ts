/**
 * React hook for dual-expiration authentication system
 * Provides authentication state management for online/offline modes
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useNetworkStatus } from './useElectron';

interface User {
  id: string;
  email: string;
  username: string;
  role: string;
  permissions: string[];
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  error: string | null;
  mode: 'online' | 'offline' | null;
  needsRefresh: boolean;
  canRefresh: boolean;
  expiresIn: number;
}

interface LoginCredentials {
  email: string;
  pass: string;
}

interface AuthStats {
  hasTokens: boolean;
  isOnline: boolean;
  isAuthenticated: boolean;
  authMode: 'online' | 'offline' | null;
  needsRefresh: boolean;
  canRefresh: boolean;
  onlineExpiresIn: number;
  offlineExpiresIn: number;
  user: {
    id: string;
    username: string;
  };
  lastLogin: number;
  lastRefresh: number;
}

/**
 * Hook for managing dual-expiration authentication
 */
export const useDualAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
    error: null,
    mode: null,
    needsRefresh: false,
    canRefresh: false,
    expiresIn: 0
  });

  const isOnline = useNetworkStatus();
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const statusCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Check if we're in Electron environment
   */
  const isElectron = typeof window !== 'undefined' && window.electronAPI;

  /**
   * Update authentication status
   */
  const updateAuthStatus = useCallback(async () => {
    if (!isElectron) {
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return;
    }

    try {
      const status = await window.electronAPI.auth.getStatus();
      
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: status.isAuthenticated,
        user: status.user || null,
        mode: status.mode || null,
        needsRefresh: status.needsRefresh || false,
        canRefresh: status.canRefresh || false,
        expiresIn: status.expiresIn || 0,
        error: status.error || null,
        isLoading: false
      }));

      // Schedule automatic refresh if needed
      if (status.needsRefresh && status.canRefresh && isOnline) {
        scheduleTokenRefresh();
      }
    } catch (error) {
      console.error('Failed to update auth status:', error);
      setAuthState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Authentication check failed',
        isLoading: false
      }));
    }
  }, [isElectron, isOnline]);

  /**
   * Schedule automatic token refresh
   */
  const scheduleTokenRefresh = useCallback(() => {
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Refresh token 5 minutes before expiration
    const refreshDelay = Math.max(0, (authState.expiresIn - 300) * 1000);
    
    refreshTimeoutRef.current = setTimeout(async () => {
      if (isOnline && authState.isAuthenticated) {
        await refreshToken();
      }
    }, refreshDelay);
  }, [authState.expiresIn, authState.isAuthenticated, isOnline]);

  /**
   * Login with credentials
   */
  const login = useCallback(async (credentials: LoginCredentials) => {
    if (!isElectron) {
      throw new Error('Authentication only available in Electron environment');
    }

    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const result = await window.electronAPI.auth.login(credentials);
      
      if (result.success) {
        await updateAuthStatus();
        return { success: true, user: result.user };
      } else {
        setAuthState(prev => ({
          ...prev,
          error: result.error || 'Login failed',
          isLoading: false
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setAuthState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }));
      return { success: false, error: errorMessage };
    }
  }, [isElectron, updateAuthStatus]);

  /**
   * Logout user
   */
  const logout = useCallback(async () => {
    if (!isElectron) {
      return { success: false, error: 'Logout only available in Electron environment' };
    }

    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const result = await window.electronAPI.auth.logout();
      
      if (result.success) {
        setAuthState({
          isAuthenticated: false,
          user: null,
          isLoading: false,
          error: null,
          mode: null,
          needsRefresh: false,
          canRefresh: false,
          expiresIn: 0
        });

        // Clear refresh timeout
        if (refreshTimeoutRef.current) {
          clearTimeout(refreshTimeoutRef.current);
          refreshTimeoutRef.current = null;
        }

        return { success: true };
      } else {
        setAuthState(prev => ({
          ...prev,
          error: result.error || 'Logout failed',
          isLoading: false
        }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Logout failed';
      setAuthState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }));
      return { success: false, error: errorMessage };
    }
  }, [isElectron]);

  /**
   * Refresh authentication token
   */
  const refreshToken = useCallback(async () => {
    if (!isElectron || !isOnline) {
      return { success: false, error: 'Token refresh not available' };
    }

    try {
      const result = await window.electronAPI.auth.refreshToken();
      
      if (result.success) {
        await updateAuthStatus();
        return { success: true };
      } else {
        if (result.requiresLogin) {
          // Token refresh failed, user needs to re-authenticate
          setAuthState(prev => ({
            ...prev,
            isAuthenticated: false,
            user: null,
            error: 'Session expired. Please log in again.'
          }));
        }
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Token refresh failed';
      return { success: false, error: errorMessage };
    }
  }, [isElectron, isOnline, updateAuthStatus]);

  /**
   * Check if user has specific permission
   */
  const hasPermission = useCallback(async (permission: string): Promise<boolean> => {
    if (!isElectron || !authState.isAuthenticated) {
      return false;
    }

    try {
      return await window.electronAPI.auth.hasPermission(permission);
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    }
  }, [isElectron, authState.isAuthenticated]);

  /**
   * Get authentication statistics
   */
  const getAuthStats = useCallback(async (): Promise<AuthStats | null> => {
    if (!isElectron) {
      return null;
    }

    try {
      return await window.electronAPI.auth.getStats();
    } catch (error) {
      console.error('Failed to get auth stats:', error);
      return null;
    }
  }, [isElectron]);

  /**
   * Clear authentication error
   */
  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  // Update online status in authentication service
  useEffect(() => {
    if (isElectron) {
      window.electronAPI.auth.setOnlineStatus(isOnline).catch(console.error);
    }
  }, [isOnline, isElectron]);

  // Initial authentication status check
  useEffect(() => {
    updateAuthStatus();
  }, [updateAuthStatus]);

  // Periodic status check (every 30 seconds)
  useEffect(() => {
    if (statusCheckIntervalRef.current) {
      clearInterval(statusCheckIntervalRef.current);
    }

    statusCheckIntervalRef.current = setInterval(() => {
      updateAuthStatus();
    }, 30000);

    return () => {
      if (statusCheckIntervalRef.current) {
        clearInterval(statusCheckIntervalRef.current);
      }
    };
  }, [updateAuthStatus]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
      if (statusCheckIntervalRef.current) {
        clearInterval(statusCheckIntervalRef.current);
      }
    };
  }, []);

  return {
    // State
    ...authState,
    isOnline,
    isElectron,

    // Actions
    login,
    logout,
    refreshToken,
    hasPermission,
    getAuthStats,
    clearError,
    updateAuthStatus,

    // Computed properties
    isOfflineMode: authState.mode === 'offline',
    isOnlineMode: authState.mode === 'online',
    sessionExpiresSoon: authState.expiresIn > 0 && authState.expiresIn < 600, // Less than 10 minutes
  };
};

export default useDualAuth;
