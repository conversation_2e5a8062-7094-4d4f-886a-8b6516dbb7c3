import { toast } from 'react-toastify';

interface NotificationOptions {
  title: string;
  body: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  duration?: number;
  useNative?: boolean;
}

/**
 * Enhanced notification system that uses Electron's native notifications
 * when available, with fallback to react-toastify
 */
export class ElectronNotifications {
  private static isElectron = !!window.electronAPI;

  /**
   * Show a notification using the best available method
   */
  static async show(options: NotificationOptions) {
    const { title, body, type = 'info', duration = 5000, useNative = true } = options;

    // Try to use native Electron notifications first
    if (this.isElectron && useNative && 'Notification' in window) {
      try {
        // Request permission if needed
        if (Notification.permission === 'default') {
          await Notification.requestPermission();
        }

        if (Notification.permission === 'granted') {
          const notification = new Notification(title, {
            body,
            icon: this.getIconForType(type),
            silent: false,
          });

          // Auto-close after duration
          setTimeout(() => {
            notification.close();
          }, duration);

          return notification;
        }
      } catch (error) {
        console.warn('Native notification failed, falling back to toast:', error);
      }
    }

    // Fallback to react-toastify
    this.showToast(title, body, type, duration);
  }

  /**
   * Show a toast notification using react-toastify
   */
  private static showToast(title: string, body: string, type: string, duration: number) {
    const message = title ? `${title}: ${body}` : body;
    
    switch (type) {
      case 'success':
        toast.success(message, { autoClose: duration });
        break;
      case 'warning':
        toast.warning(message, { autoClose: duration });
        break;
      case 'error':
        toast.error(message, { autoClose: duration });
        break;
      default:
        toast.info(message, { autoClose: duration });
    }
  }

  /**
   * Get appropriate icon for notification type
   */
  private static getIconForType(type: string): string {
    // In a real app, you'd have actual icon files
    // For now, we'll use a default icon path
    return './favicon.jpeg';
  }

  /**
   * Show a success notification
   */
  static success(title: string, body: string, duration?: number) {
    return this.show({ title, body, type: 'success', duration });
  }

  /**
   * Show an error notification
   */
  static error(title: string, body: string, duration?: number) {
    return this.show({ title, body, type: 'error', duration });
  }

  /**
   * Show a warning notification
   */
  static warning(title: string, body: string, duration?: number) {
    return this.show({ title, body, type: 'warning', duration });
  }

  /**
   * Show an info notification
   */
  static info(title: string, body: string, duration?: number) {
    return this.show({ title, body, type: 'info', duration });
  }

  /**
   * Check if native notifications are supported
   */
  static isNativeSupported(): boolean {
    return this.isElectron && 'Notification' in window && Notification.permission !== 'denied';
  }

  /**
   * Request notification permission
   */
  static async requestPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  }
}
