import { getDimensions } from "./getDimensions";
import { setLastLocation } from "./setLastLocation";

export const handleScroll = ({
  reviewOrderMenuData,
  orderSummaryRef,
  reviewOrderSectionsRef,
  setSelectedMenu,
  selectedMenu,
}: any) => {
  const { height } = getDimensions(orderSummaryRef.current);

  if (reviewOrderSectionsRef?.current) {
    const scrollPosition = reviewOrderSectionsRef?.current?.scrollTop + height;

    const selected = reviewOrderMenuData.find(({ ref }: any) => {
      const element = ref?.current;
      if (element) {
        const { offsetBottom, offsetTop } = getDimensions(element);
        return scrollPosition > offsetTop && scrollPosition < offsetBottom;
      }
      return null;
    });
    if (selected && selected.name !== selectedMenu) {
      setSelectedMenu(selected.name);
      setLastLocation(selected.name);
    } else if (!selected && selectedMenu) {
      setSelectedMenu(undefined);
    }
  }
};
