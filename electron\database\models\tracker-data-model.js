/**
 * Tracker Data model for handling tracker-specific operations
 * Provides specialized methods for working with tracker data within deals
 */

class TrackerDataModel {
  constructor(db, dealsModel, formFieldsModel) {
    this.db = db;
    this.dealsModel = dealsModel;
    this.formFieldsModel = formFieldsModel;
  }

  /**
   * Get tracker data for a deal
   * @param {number} dealId - Deal ID
   * @returns {Object} Tracker data
   */
  getTrackerData(dealId) {
    try {
      const deal = this.dealsModel.findById(dealId);
      if (!deal) {
        throw new Error(`Deal not found with ID: ${dealId}`);
      }

      return deal.trackerData || {};
    } catch (error) {
      console.error('Error getting tracker data:', error);
      throw error;
    }
  }

  /**
   * Update tracker data for a deal
   * @param {number} dealId - Deal ID
   * @param {Object} trackerData - Tracker data to update
   * @param {boolean} merge - Whether to merge with existing data (default: true)
   * @returns {Object} Updated deal
   */
  updateTrackerData(dealId, trackerData, merge = true) {
    try {
      if (merge) {
        return this.dealsModel.mergeTrackerData(dealId, trackerData);
      } else {
        return this.dealsModel.updateTrackerData(dealId, trackerData);
      }
    } catch (error) {
      console.error('Error updating tracker data:', error);
      throw error;
    }
  }

  /**
   * Update specific tracker field
   * @param {number} dealId - Deal ID
   * @param {string} fieldName - Field name
   * @param {any} fieldValue - Field value
   * @returns {Object} Updated deal
   */
  updateTrackerField(dealId, fieldName, fieldValue) {
    try {
      const updateData = { [fieldName]: fieldValue };
      return this.updateTrackerData(dealId, updateData, true);
    } catch (error) {
      console.error('Error updating tracker field:', error);
      throw error;
    }
  }

  /**
   * Get tracker field value
   * @param {number} dealId - Deal ID
   * @param {string} fieldName - Field name
   * @returns {any} Field value
   */
  getTrackerField(dealId, fieldName) {
    try {
      const trackerData = this.getTrackerData(dealId);
      return trackerData[fieldName];
    } catch (error) {
      console.error('Error getting tracker field:', error);
      throw error;
    }
  }

  /**
   * Validate tracker data against form field definitions
   * @param {Object} trackerData - Tracker data to validate
   * @param {string} section - Form section to validate against (optional)
   * @returns {Object} Validation result
   */
  validateTrackerData(trackerData, section = null) {
    try {
      const errors = [];
      const warnings = [];

      // Get form fields for validation
      let formFields;
      if (section) {
        formFields = this.formFieldsModel.getFieldsBySection(section);
      } else {
        const allFields = this.formFieldsModel.getAllActiveFieldsBySection();
        formFields = Object.values(allFields).flat();
      }

      // Validate each field
      for (const field of formFields) {
        const value = trackerData[field.name];
        const fieldErrors = this.validateField(field, value);
        errors.push(...fieldErrors);
      }

      // Check for unknown fields
      const knownFieldNames = formFields.map(f => f.name);
      for (const fieldName of Object.keys(trackerData)) {
        if (!knownFieldNames.includes(fieldName)) {
          warnings.push(`Unknown field: ${fieldName}`);
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error) {
      console.error('Error validating tracker data:', error);
      throw error;
    }
  }

  /**
   * Validate individual field
   * @param {Object} field - Form field definition
   * @param {any} value - Field value
   * @returns {Array} Array of error messages
   */
  validateField(field, value) {
    const errors = [];

    // Check required fields
    if (field.required && (value === null || value === undefined || value === '')) {
      errors.push(`${field.label} is required`);
      return errors; // Don't validate further if required field is missing
    }

    // Skip validation if field is empty and not required
    if (value === null || value === undefined || value === '') {
      return errors;
    }

    // Type-specific validation
    switch (field.type) {
      case 'select':
      case 'buttons':
        if (field.options && !field.options.includes(value)) {
          errors.push(`${field.label} must be one of: ${field.options.join(', ')}`);
        }
        break;

      case 'multiSelect':
        if (Array.isArray(value)) {
          if (field.options) {
            for (const item of value) {
              const validOption = field.options.some(opt => 
                typeof opt === 'string' ? opt === item : opt.value === item
              );
              if (!validOption) {
                errors.push(`${field.label} contains invalid option: ${item}`);
              }
            }
          }
        } else {
          errors.push(`${field.label} must be an array`);
        }
        break;

      case 'date':
        if (!this.isValidDate(value)) {
          errors.push(`${field.label} must be a valid date`);
        }
        break;

      case 'number':
        if (isNaN(parseFloat(value))) {
          errors.push(`${field.label} must be a valid number`);
        }
        break;

      case 'text':
      case 'textarea':
        if (typeof value !== 'string') {
          errors.push(`${field.label} must be a string`);
        }
        break;
    }

    // Custom validation rules
    if (field.validation_rules) {
      const customErrors = this.applyCustomValidation(field, value);
      errors.push(...customErrors);
    }

    return errors;
  }

  /**
   * Apply custom validation rules
   * @param {Object} field - Form field definition
   * @param {any} value - Field value
   * @returns {Array} Array of error messages
   */
  applyCustomValidation(field, value) {
    const errors = [];
    const rules = field.validation_rules;

    if (rules.minLength && value.length < rules.minLength) {
      errors.push(`${field.label} must be at least ${rules.minLength} characters`);
    }

    if (rules.maxLength && value.length > rules.maxLength) {
      errors.push(`${field.label} must be no more than ${rules.maxLength} characters`);
    }

    if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
      errors.push(`${field.label} format is invalid`);
    }

    if (rules.min && parseFloat(value) < rules.min) {
      errors.push(`${field.label} must be at least ${rules.min}`);
    }

    if (rules.max && parseFloat(value) > rules.max) {
      errors.push(`${field.label} must be no more than ${rules.max}`);
    }

    return errors;
  }

  /**
   * Check if date string is valid
   * @param {string} dateString - Date string to validate
   * @returns {boolean} Whether date is valid
   */
  isValidDate(dateString) {
    if (!dateString) return false;
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  /**
   * Process tracker data with field transformations
   * @param {Object} trackerData - Raw tracker data
   * @param {string} section - Form section (optional)
   * @returns {Object} Processed tracker data
   */
  processTrackerData(trackerData, section = null) {
    try {
      const processedData = { ...trackerData };

      // Get form fields for processing
      let formFields;
      if (section) {
        formFields = this.formFieldsModel.getFieldsBySection(section);
      } else {
        const allFields = this.formFieldsModel.getAllActiveFieldsBySection();
        formFields = Object.values(allFields).flat();
      }

      // Process each field
      for (const field of formFields) {
        if (processedData.hasOwnProperty(field.name)) {
          processedData[field.name] = this.transformFieldValue(field, processedData[field.name]);
        }
      }

      return processedData;
    } catch (error) {
      console.error('Error processing tracker data:', error);
      throw error;
    }
  }

  /**
   * Transform field value based on field type
   * @param {Object} field - Form field definition
   * @param {any} value - Field value
   * @returns {any} Transformed value
   */
  transformFieldValue(field, value) {
    if (value === null || value === undefined || value === '') {
      return value;
    }

    switch (field.type) {
      case 'number':
        return parseFloat(value);

      case 'date':
        // Ensure date is in ISO format
        const date = new Date(value);
        return date.toISOString().split('T')[0];

      case 'multiSelect':
        // Ensure multiSelect values are arrays
        return Array.isArray(value) ? value : [value];

      case 'text':
      case 'textarea':
        // Trim whitespace
        return typeof value === 'string' ? value.trim() : value;

      default:
        return value;
    }
  }

  /**
   * Get tracker data summary for a deal
   * @param {number} dealId - Deal ID
   * @returns {Object} Tracker data summary
   */
  getTrackerSummary(dealId) {
    try {
      const trackerData = this.getTrackerData(dealId);
      const allFields = this.formFieldsModel.getAllActiveFieldsBySection();
      
      const summary = {
        totalFields: 0,
        completedFields: 0,
        sections: {}
      };

      for (const [sectionName, fields] of Object.entries(allFields)) {
        const sectionSummary = {
          totalFields: fields.length,
          completedFields: 0,
          fields: []
        };

        for (const field of fields) {
          const value = trackerData[field.name];
          const isCompleted = value !== null && value !== undefined && value !== '';
          
          if (isCompleted) {
            sectionSummary.completedFields++;
            summary.completedFields++;
          }

          sectionSummary.fields.push({
            name: field.name,
            label: field.label,
            value,
            isCompleted
          });

          summary.totalFields++;
        }

        summary.sections[sectionName] = sectionSummary;
      }

      summary.completionPercentage = summary.totalFields > 0 
        ? Math.round((summary.completedFields / summary.totalFields) * 100)
        : 0;

      return summary;
    } catch (error) {
      console.error('Error getting tracker summary:', error);
      throw error;
    }
  }
}

module.exports = TrackerDataModel;
