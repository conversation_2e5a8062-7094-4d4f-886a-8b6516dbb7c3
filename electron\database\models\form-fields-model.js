/**
 * Form Fields model for managing dynamic field definitions
 * Handles form field metadata for dynamic form generation
 */

const BaseModel = require('./base-model');

class FormFieldsModel extends BaseModel {
  constructor(db) {
    super(db, 'form_fields');
    this.initializeFormFieldsStatements();
  }

  /**
   * Initialize form fields specific prepared statements
   */
  initializeFormFieldsStatements() {
    this.preparedStatements.findBySection = this.db.prepare(`
      SELECT * FROM ${this.tableName} 
      WHERE form_section = ? AND active = TRUE
      ORDER BY display_order ASC
    `);

    this.preparedStatements.findByName = this.db.prepare(`
      SELECT * FROM ${this.tableName} WHERE name = ?
    `);

    this.preparedStatements.findActiveFields = this.db.prepare(`
      SELECT * FROM ${this.tableName} 
      WHERE active = TRUE
      ORDER BY form_section ASC, display_order ASC
    `);

    this.preparedStatements.updateDisplayOrder = this.db.prepare(`
      UPDATE ${this.tableName} 
      SET display_order = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    this.preparedStatements.toggleActive = this.db.prepare(`
      UPDATE ${this.tableName} 
      SET active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
  }

  /**
   * Get fields by form section
   * @param {string} section - Form section name
   * @returns {Array} Form fields for the section
   */
  getFieldsBySection(section) {
    try {
      const fields = this.preparedStatements.findBySection.all(section);
      return fields.map(field => this.parseFieldOptions(field));
    } catch (error) {
      console.error(`Error getting fields for section ${section}:`, error);
      throw error;
    }
  }

  /**
   * Get field by name
   * @param {string} name - Field name
   * @returns {Object|null} Form field or null if not found
   */
  getFieldByName(name) {
    try {
      const field = this.preparedStatements.findByName.get(name);
      return field ? this.parseFieldOptions(field) : null;
    } catch (error) {
      console.error(`Error getting field ${name}:`, error);
      throw error;
    }
  }

  /**
   * Get all active fields grouped by section
   * @returns {Object} Fields grouped by section
   */
  getAllActiveFieldsBySection() {
    try {
      const fields = this.preparedStatements.findActiveFields.all();
      const fieldsBySection = {};

      for (const field of fields) {
        const parsedField = this.parseFieldOptions(field);
        if (!fieldsBySection[field.form_section]) {
          fieldsBySection[field.form_section] = [];
        }
        fieldsBySection[field.form_section].push(parsedField);
      }

      return fieldsBySection;
    } catch (error) {
      console.error('Error getting all active fields:', error);
      throw error;
    }
  }

  /**
   * Create new form field
   * @param {Object} fieldData - Field data
   * @returns {Object} Created field
   */
  create(fieldData) {
    try {
      // Validate required fields
      this.validateFieldData(fieldData);

      // Serialize options if provided
      if (fieldData.options && typeof fieldData.options !== 'string') {
        fieldData.options = JSON.stringify(fieldData.options);
      }

      // Serialize validation rules if provided
      if (fieldData.validation_rules && typeof fieldData.validation_rules !== 'string') {
        fieldData.validation_rules = JSON.stringify(fieldData.validation_rules);
      }

      return super.create(fieldData);
    } catch (error) {
      console.error('Error creating form field:', error);
      throw error;
    }
  }

  /**
   * Update form field
   * @param {number} id - Field ID
   * @param {Object} fieldData - Updated field data
   * @returns {Object} Updated field
   */
  update(id, fieldData) {
    try {
      // Serialize options if provided
      if (fieldData.options && typeof fieldData.options !== 'string') {
        fieldData.options = JSON.stringify(fieldData.options);
      }

      // Serialize validation rules if provided
      if (fieldData.validation_rules && typeof fieldData.validation_rules !== 'string') {
        fieldData.validation_rules = JSON.stringify(fieldData.validation_rules);
      }

      return super.update(id, fieldData);
    } catch (error) {
      console.error('Error updating form field:', error);
      throw error;
    }
  }

  /**
   * Update field display order
   * @param {number} id - Field ID
   * @param {number} displayOrder - New display order
   * @returns {boolean} Success status
   */
  updateDisplayOrder(id, displayOrder) {
    try {
      const result = this.preparedStatements.updateDisplayOrder.run(displayOrder, id);
      return result.changes > 0;
    } catch (error) {
      console.error('Error updating display order:', error);
      throw error;
    }
  }

  /**
   * Toggle field active status
   * @param {number} id - Field ID
   * @param {boolean} active - Active status
   * @returns {boolean} Success status
   */
  toggleActive(id, active) {
    try {
      const result = this.preparedStatements.toggleActive.run(active, id);
      return result.changes > 0;
    } catch (error) {
      console.error('Error toggling field active status:', error);
      throw error;
    }
  }

  /**
   * Parse field options and validation rules from JSON
   * @param {Object} field - Raw field data
   * @returns {Object} Field with parsed options
   */
  parseFieldOptions(field) {
    const parsedField = { ...field };

    // Parse options if it's a JSON string
    if (field.options) {
      try {
        parsedField.options = JSON.parse(field.options);
      } catch (error) {
        console.warn(`Invalid JSON in options for field ${field.name}:`, field.options);
        parsedField.options = [];
      }
    }

    // Parse validation rules if it's a JSON string
    if (field.validation_rules) {
      try {
        parsedField.validation_rules = JSON.parse(field.validation_rules);
      } catch (error) {
        console.warn(`Invalid JSON in validation_rules for field ${field.name}:`, field.validation_rules);
        parsedField.validation_rules = {};
      }
    }

    return parsedField;
  }

  /**
   * Validate form field data
   * @param {Object} data - Field data to validate
   * @param {boolean} isCreate - Whether this is for creation
   */
  validateFieldData(data, isCreate = true) {
    const errors = [];

    if (isCreate) {
      if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
        errors.push('Field name is required');
      }

      if (!data.label || typeof data.label !== 'string' || data.label.trim().length === 0) {
        errors.push('Field label is required');
      }

      if (!data.type || typeof data.type !== 'string') {
        errors.push('Field type is required');
      }
    }

    // Validate field type
    if (data.type) {
      const validTypes = ['text', 'select', 'buttons', 'date', 'multiSelect', 'number', 'textarea'];
      if (!validTypes.includes(data.type)) {
        errors.push(`Invalid field type. Must be one of: ${validTypes.join(', ')}`);
      }
    }

    if (errors.length > 0) {
      throw new Error(`Form field validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Get form schema for a specific section
   * @param {string} section - Form section name
   * @returns {Object} Form schema object
   */
  getFormSchema(section) {
    try {
      const fields = this.getFieldsBySection(section);
      return {
        section,
        fields,
        fieldCount: fields.length
      };
    } catch (error) {
      console.error(`Error getting form schema for section ${section}:`, error);
      throw error;
    }
  }

  /**
   * Bulk update field display orders
   * @param {Array} fieldOrders - Array of {id, display_order} objects
   * @returns {boolean} Success status
   */
  bulkUpdateDisplayOrders(fieldOrders) {
    try {
      const transaction = this.db.transaction(() => {
        for (const { id, display_order } of fieldOrders) {
          this.updateDisplayOrder(id, display_order);
        }
      });

      transaction();
      return true;
    } catch (error) {
      console.error('Error bulk updating display orders:', error);
      throw error;
    }
  }
}

module.exports = FormFieldsModel;
